# Makefile para Busca CUDA Ultra-Rápida

# Configurações do compilador
NVCC = nvcc
CFLAGS = -O3 -arch=sm_75 -std=c++11
TARGET = busca_cuda
SOURCE = busca_cuda_simples.cu
SOURCE_ULTRA = busca_cuda_ultra_rapida.cu

# Detectar arquitetura da GPU automaticamente
GPU_ARCH_RAW := $(shell nvidia-smi --query-gpu=compute_cap --format=csv,noheader,nounits | head -1)
GPU_ARCH_MAJOR := $(shell echo $(GPU_ARCH_RAW) | cut -d'.' -f1)
GPU_ARCH_MINOR := $(shell echo $(GPU_ARCH_RAW) | cut -d'.' -f2)

# Mapear arquiteturas conhecidas
ifeq ($(GPU_ARCH_MAJOR),12)
    GPU_ARCH = 89  # RTX 5090 usa sm_89
else ifeq ($(GPU_ARCH_MAJOR),8)
    GPU_ARCH = 86  # RTX 30xx/40xx
else ifeq ($(GPU_ARCH_MAJOR),7)
    GPU_ARCH = 75  # RTX 20xx
else ifeq ($(GPU_ARCH_MAJOR),6)
    GPU_ARCH = 61  # GTX 10xx
else
    GPU_ARCH = 75  # Default seguro
endif

# Flags otimizadas para máxima performance
NVCC_FLAGS = -O3 \
             -arch=sm_$(GPU_ARCH) \
             -std=c++11 \
             -Xptxas -O3 \
             -use_fast_math \
             -maxrregcount=64 \
             --ptxas-options=-v

all: $(TARGET)

$(TARGET): $(SOURCE)
	@echo "🔧 Compilando busca CUDA ultra-rápida..."
	@echo "Arquitetura GPU detectada: sm_$(GPU_ARCH)"
	$(NVCC) $(NVCC_FLAGS) -o $(TARGET) $(SOURCE)
	@echo "✅ Compilação concluída!"

clean:
	rm -f $(TARGET)

test: $(TARGET)
	@echo "🧪 Testando com hash errado da chave 1..."
	./$(TARGET) 36df2f22295784ab7f81989f9247bfd99bb00c03 1 1000000

test-chave2: $(TARGET)
	@echo "🧪 Testando com hash errado da chave 2..."
	./$(TARGET) 5fed51813a4b0353320dbee6fc24a63c5f695181 1 1000000

test-chave3: $(TARGET)
	@echo "🧪 Testando com hash errado da chave 3..."
	./$(TARGET) b0548c85212204a8a9555adbbdb6dab85b77afa4 1 1000000

benchmark: $(TARGET)
	@echo "📊 Executando benchmark de velocidade..."
	@echo "Testando 10 milhões de chaves..."
	./$(TARGET) 36df2f22295784ab7f81989f9247bfd99bb00c03 1 10000000

info:
	@echo "ℹ️  Informações do sistema CUDA:"
	nvidia-smi
	nvcc --version

install-deps:
	@echo "📦 Verificando dependências CUDA..."
	@which nvcc > /dev/null || (echo "❌ NVCC não encontrado! Instale CUDA Toolkit" && exit 1)
	@which nvidia-smi > /dev/null || (echo "❌ nvidia-smi não encontrado! Instale drivers NVIDIA" && exit 1)
	@echo "✅ Dependências CUDA OK"

help:
	@echo "🚀 BUSCA CUDA ULTRA-RÁPIDA - Comandos disponíveis:"
	@echo ""
	@echo "make                 - Compilar o programa"
	@echo "make test            - Testar com hash da chave 1"
	@echo "make test-chave2     - Testar com hash da chave 2"
	@echo "make test-chave3     - Testar com hash da chave 3"
	@echo "make benchmark       - Benchmark de velocidade"
	@echo "make info            - Informações do sistema CUDA"
	@echo "make install-deps    - Verificar dependências"
	@echo "make clean           - Limpar arquivos compilados"
	@echo ""
	@echo "Uso manual:"
	@echo "./busca_cuda <hash_errado_hex> [start_range] [end_range]"
	@echo ""
	@echo "Exemplos:"
	@echo "./busca_cuda 36df2f22295784ab7f81989f9247bfd99bb00c03 1 1000000"
	@echo "./busca_cuda 5fed51813a4b0353320dbee6fc24a63c5f695181 1000000 2000000"

.PHONY: all clean test test-chave2 test-chave3 benchmark info install-deps help
