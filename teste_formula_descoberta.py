#!/usr/bin/env python3
"""
TESTE DA FÓRMULA DESCOBERTA - Validação Matemática

Este programa testa se a fórmula descoberta através da análise de sincronicidades
consegue gerar corretamente os números mágicos das carteiras conhecidas.
"""

from main import (
    analisar_padroes_carteiras_conhecidas,
    descobrir_formula_matematica, 
    formula_hash_correto_para_errado,
    calculate_target_hash160,
    simular_gpu_errada_para_chave
)

def testar_formula_com_carteiras_conhecidas():
    """Testa a fórmula descoberta com as carteiras conhecidas"""
    print("="*80)
    print("🧪 TESTE DA FÓRMULA DESCOBERTA COM CARTEIRAS CONHECIDAS")
    print("="*80)
    
    # Usar os números mágicos REAIS que a função gera
    carteiras = [
        (1, "1BgGZ9tcN4rm9KBzDn7KprQz87SZ26SAMH", "36df2f22295784ab7f81989f9247bfd99bb00c03"),
        (2, "1cMh228HTCiwS8ZsaakH8A8wze1JR5ZsP", "5fed51813a4b0353320dbee6fc24a63c5f695181"),
        (3, "1CUNEBjYrCn2y1SdiUMohaKUi4wpP326Lb", "b0548c85212204a8a9555adbbdb6dab85b77afa4")
    ]
    
    resultados = []
    
    for chave_privada, endereco, hash_errado_esperado in carteiras:
        print(f"\n🔍 TESTANDO CARTEIRA - CHAVE {chave_privada}")
        print(f"   Endereço: {endereco}")
        print(f"   Hash errado esperado: {hash_errado_esperado}")
        
        # Obter hash160 correto
        hash160_correto = calculate_target_hash160(endereco)
        print(f"   Hash160 correto: {hash160_correto.hex()}")
        
        # Aplicar fórmula simples
        hash_errado_calculado = formula_hash_correto_para_errado(hash160_correto)
        hash_errado_calculado_hex = hash_errado_calculado.hex()
        
        print(f"   Hash errado calculado: {hash_errado_calculado_hex}")
        
        # Comparar resultados
        sucesso = hash_errado_calculado_hex == hash_errado_esperado
        print(f"   ✅ Resultado: {'SUCESSO' if sucesso else 'FALHOU'}")
        
        if not sucesso:
            print(f"   📊 Análise da diferença:")
            for i in range(20):
                esperado_byte = int(hash_errado_esperado[i*2:i*2+2], 16)
                calculado_byte = hash_errado_calculado[i]
                if esperado_byte != calculado_byte:
                    print(f"     Byte {i}: esperado {esperado_byte:02x}, calculado {calculado_byte:02x}")
        
        resultados.append({
            'chave_privada': chave_privada,
            'endereco': endereco,
            'sucesso': sucesso,
            'esperado': hash_errado_esperado,
            'calculado': hash_errado_calculado_hex
        })
    
    return resultados

def testar_formula_com_endereco_desconhecido():
    """Testa a fórmula com um endereço desconhecido"""
    print(f"\n" + "="*80)
    print("🧪 TESTE COM ENDEREÇO DESCONHECIDO")
    print("="*80)
    
    endereco_teste = "1L2GM8eE7mJWLdo3HZS6su1832NX2txaac"
    
    print(f"\n🔍 TESTANDO ENDEREÇO DESCONHECIDO")
    print(f"   Endereço: {endereco_teste}")
    
    # Obter hash160 correto
    hash160_correto = calculate_target_hash160(endereco_teste)
    print(f"   Hash160 correto: {hash160_correto.hex()}")
    
    # Aplicar fórmula descoberta
    hash_errado_calculado = formula_hash_correto_para_errado(hash160_correto)
    hash_errado_calculado_hex = hash_errado_calculado.hex()
    
    print(f"   Hash160 errado calculado: {hash_errado_calculado_hex}")
    
    # Verificar se é diferente do hash correto
    diferente = hash_errado_calculado_hex != hash160_correto.hex()
    print(f"   ✅ Diferente do hash correto: {'SIM' if diferente else 'NÃO'}")
    
    return {
        'endereco': endereco_teste,
        'hash_correto': hash160_correto.hex(),
        'hash_errado': hash_errado_calculado_hex,
        'diferente': diferente
    }

def analisar_eficacia_formula(resultados_conhecidas, resultado_desconhecido):
    """Analisa a eficácia da fórmula descoberta"""
    print(f"\n" + "="*80)
    print("📊 ANÁLISE DA EFICÁCIA DA FÓRMULA")
    print("="*80)
    
    # Analisar resultados das carteiras conhecidas
    sucessos = sum(1 for r in resultados_conhecidas if r['sucesso'])
    total = len(resultados_conhecidas)
    
    print(f"\n📈 RESULTADOS COM CARTEIRAS CONHECIDAS:")
    print(f"   Sucessos: {sucessos}/{total}")
    print(f"   Taxa de sucesso: {(sucessos/total)*100:.1f}%")
    
    if sucessos == total:
        print(f"   🎉 PERFEITO: A fórmula funciona para todas as carteiras conhecidas!")
    elif sucessos > 0:
        print(f"   ⚠️  PARCIAL: A fórmula funciona para algumas carteiras.")
    else:
        print(f"   ❌ FALHOU: A fórmula não funciona para nenhuma carteira conhecida.")
    
    # Analisar resultado do endereço desconhecido
    print(f"\n📈 RESULTADO COM ENDEREÇO DESCONHECIDO:")
    print(f"   Gerou hash diferente: {'SIM' if resultado_desconhecido['diferente'] else 'NÃO'}")
    
    if resultado_desconhecido['diferente']:
        print(f"   ✅ BOM: A fórmula gera um hash160 errado diferente do correto.")
    else:
        print(f"   ❌ PROBLEMA: A fórmula não está gerando diferença suficiente.")
    
    # Conclusão geral
    print(f"\n🎯 CONCLUSÃO GERAL:")
    if sucessos == total and resultado_desconhecido['diferente']:
        print(f"   🎉 SUCESSO TOTAL: A fórmula está funcionando perfeitamente!")
        print(f"   ✅ Funciona para carteiras conhecidas")
        print(f"   ✅ Gera números mágicos válidos para endereços desconhecidos")
        print(f"   ✅ Pronta para uso no programa principal")
    elif sucessos > 0:
        print(f"   ⚠️  SUCESSO PARCIAL: A fórmula precisa de ajustes.")
        print(f"   📝 Sugestões:")
        print(f"     • Analisar os casos que falharam")
        print(f"     • Ajustar as constantes da fórmula")
        print(f"     • Testar transformações adicionais")
    else:
        print(f"   ❌ FALHOU: A fórmula precisa ser completamente reformulada.")
        print(f"   📝 Sugestões:")
        print(f"     • Analisar mais profundamente os padrões")
        print(f"     • Testar abordagens matemáticas diferentes")
        print(f"     • Considerar análise criptográfica mais avançada")

def main():
    """Função principal"""
    print("🔬 TESTE DA FÓRMULA DESCOBERTA - VALIDAÇÃO MATEMÁTICA")
    print("Este programa valida se a fórmula descoberta funciona corretamente")
    
    try:
        # Teste 1: Carteiras conhecidas
        resultados_conhecidas = testar_formula_com_carteiras_conhecidas()
        
        # Teste 2: Endereço desconhecido
        resultado_desconhecido = testar_formula_com_endereco_desconhecido()
        
        # Análise da eficácia
        analisar_eficacia_formula(resultados_conhecidas, resultado_desconhecido)
        
    except Exception as e:
        print(f"❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
