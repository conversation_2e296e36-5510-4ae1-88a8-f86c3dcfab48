#!/usr/bin/env python3
"""
CALCULAR HASH ERRADO DE UM ENDEREÇO
Calcula qual seria o hash errado de qualquer endereço Bitcoin
usando a lógica do kernel CUDA
"""

import hashlib
import base58
from main import simular_gpu_errada_para_chave

def endereco_para_hash160(endereco):
    """Converte endereço Bitcoin para hash160"""
    try:
        # Decodificar Base58Check
        decoded = base58.b58decode(endereco)
        
        # Verificar checksum
        payload = decoded[:-4]
        checksum = decoded[-4:]
        hash_check = hashlib.sha256(hashlib.sha256(payload).digest()).digest()[:4]
        
        if checksum != hash_check:
            raise ValueError("Checksum inválido")
        
        # Extrair hash160 (remover version byte)
        hash160 = payload[1:]
        
        return hash160.hex()
        
    except Exception as e:
        print(f"❌ Erro ao decodificar endereço: {e}")
        return None

def calcular_hash_errado_endereco(endereco):
    """
    Calcula o hash errado que seria gerado por uma chave privada
    que produz este endereço
    """
    print(f"🔍 CALCULANDO HASH ERRADO PARA ENDEREÇO")
    print("=" * 50)
    print(f"Endereço: {endereco}")
    
    # Converter endereço para hash160
    hash160_correto = endereco_para_hash160(endereco)
    if not hash160_correto:
        print("❌ Não foi possível converter endereço")
        return None
    
    print(f"Hash160 correto: {hash160_correto}")
    
    # Para calcular o hash errado, precisamos simular uma chave privada
    # que geraria este endereço. Como não sabemos a chave real,
    # vamos usar uma aproximação baseada no hash160
    
    # Converter hash160 para um número que simule uma chave privada
    hash160_bytes = bytes.fromhex(hash160_correto)
    
    # Método 1: Usar os primeiros 8 bytes como chave privada simulada
    chave_simulada = int.from_bytes(hash160_bytes[:8], byteorder='big')
    
    print(f"Chave simulada: {chave_simulada} (0x{chave_simulada:x})")
    
    # Calcular hash errado usando a função do kernel
    hash_errado = simular_gpu_errada_para_chave(chave_simulada)
    hash_errado_hex = hash_errado.hex()
    
    print(f"Hash errado calculado: {hash_errado_hex}")
    
    return hash_errado_hex

def calcular_hash_errado_multiplos_metodos(endereco):
    """
    Calcula hash errado usando múltiplos métodos para maior precisão
    """
    print(f"🎯 MÚLTIPLOS MÉTODOS PARA CALCULAR HASH ERRADO")
    print("=" * 60)
    print(f"Endereço: {endereco}")
    
    hash160_correto = endereco_para_hash160(endereco)
    if not hash160_correto:
        return None
    
    hash160_bytes = bytes.fromhex(hash160_correto)
    print(f"Hash160 correto: {hash160_correto}")
    
    metodos = []
    
    # Método 1: Primeiros 8 bytes
    chave1 = int.from_bytes(hash160_bytes[:8], byteorder='big')
    hash_errado1 = simular_gpu_errada_para_chave(chave1).hex()
    metodos.append(("Primeiros 8 bytes", chave1, hash_errado1))
    
    # Método 2: Últimos 8 bytes (com padding)
    padded = hash160_bytes + b'\x00' * 4  # Pad para 24 bytes
    chave2 = int.from_bytes(padded[-8:], byteorder='big')
    hash_errado2 = simular_gpu_errada_para_chave(chave2).hex()
    metodos.append(("Últimos 8 bytes", chave2, hash_errado2))
    
    # Método 3: XOR de todos os bytes
    xor_result = 0
    for byte in hash160_bytes:
        xor_result ^= byte
    chave3 = xor_result * 0x123456789ABCDEF  # Multiplicar para espalhar
    hash_errado3 = simular_gpu_errada_para_chave(chave3).hex()
    metodos.append(("XOR expandido", chave3, hash_errado3))
    
    # Método 4: Hash do hash160
    hash_of_hash = hashlib.sha256(hash160_bytes).digest()
    chave4 = int.from_bytes(hash_of_hash[:8], byteorder='big')
    hash_errado4 = simular_gpu_errada_para_chave(chave4).hex()
    metodos.append(("Hash do hash160", chave4, hash_errado4))
    
    # Método 5: Combinação de bytes
    combined = 0
    for i, byte in enumerate(hash160_bytes):
        combined += byte * (256 ** i)
    chave5 = combined % (2**64)  # Manter em 64 bits
    hash_errado5 = simular_gpu_errada_para_chave(chave5).hex()
    metodos.append(("Combinação bytes", chave5, hash_errado5))
    
    print(f"\n📊 RESULTADOS DOS MÉTODOS:")
    print("-" * 80)
    
    for i, (nome, chave, hash_errado) in enumerate(metodos, 1):
        print(f"{i}. {nome}:")
        print(f"   Chave simulada: {chave} (0x{chave:x})")
        print(f"   Hash errado: {hash_errado}")
        print()
    
    return metodos

def testar_endereco_conhecido():
    """Testa com endereço conhecido para validar"""
    print("🧪 TESTE COM ENDEREÇO CONHECIDO")
    print("=" * 40)
    
    # Endereço da chave privada 1
    endereco_chave1 = "1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2"
    hash_errado_esperado = "36df2f22295784ab7f81989f9247bfd99bb00c03"
    
    print(f"Endereço da chave 1: {endereco_chave1}")
    print(f"Hash errado esperado: {hash_errado_esperado}")
    
    # Calcular usando nossos métodos
    metodos = calcular_hash_errado_multiplos_metodos(endereco_chave1)
    
    print(f"🔍 COMPARANDO COM ESPERADO:")
    for i, (nome, chave, hash_calculado) in enumerate(metodos, 1):
        if hash_calculado == hash_errado_esperado:
            print(f"✅ Método {i} ({nome}): MATCH!")
        else:
            print(f"❌ Método {i} ({nome}): Diferente")

def main():
    """Função principal"""
    print("🎯 CALCULAR HASH ERRADO DE ENDEREÇO BITCOIN")
    print("=" * 60)
    
    print("\nEscolha uma opção:")
    print("1. 🧪 Testar com endereço conhecido (validação)")
    print("2. 🎯 Calcular hash errado de um endereço")
    print("3. 📊 Calcular com múltiplos métodos")
    
    opcao = input("\nOpção (1/2/3): ").strip()
    
    if opcao == "1":
        testar_endereco_conhecido()
        
    elif opcao == "2":
        endereco = input("\nDigite o endereço Bitcoin: ").strip()
        if endereco:
            hash_errado = calcular_hash_errado_endereco(endereco)
            if hash_errado:
                print(f"\n✅ HASH ERRADO CALCULADO:")
                print(f"Hash errado: {hash_errado}")
                print(f"\n🚀 USE ESTE COMANDO:")
                print(f"./buscar_chave_por_hash_errado {hash_errado} 1:1000000000")
        
    elif opcao == "3":
        endereco = input("\nDigite o endereço Bitcoin: ").strip()
        if endereco:
            metodos = calcular_hash_errado_multiplos_metodos(endereco)
            
            print(f"\n🎯 ESCOLHA O MÉTODO MAIS PROVÁVEL:")
            for i, (nome, chave, hash_errado) in enumerate(metodos, 1):
                print(f"{i}. {nome}: {hash_errado}")
            
            escolha = input(f"\nEscolha o método (1-{len(metodos)}): ").strip()
            try:
                idx = int(escolha) - 1
                if 0 <= idx < len(metodos):
                    nome, chave, hash_errado = metodos[idx]
                    print(f"\n✅ MÉTODO ESCOLHIDO: {nome}")
                    print(f"Hash errado: {hash_errado}")
                    print(f"\n🚀 USE ESTE COMANDO:")
                    print(f"./buscar_chave_por_hash_errado {hash_errado} 1:1000000000")
            except ValueError:
                print("❌ Escolha inválida")
    
    else:
        print("❌ Opção inválida!")

if __name__ == "__main__":
    main()
