# Makefile para Descobrir Hash Errado

NVCC = nvcc
TARGET = descobrir_hash_errado
SOURCE = descobrir_hash_errado.cu

# Arquitetura para RTX 5090 (Compute Capability 8.9)
GPU_ARCH = 89

# Flags otimizadas
NVCC_FLAGS = -O3 -arch=sm_$(GPU_ARCH) -std=c++11

all: $(TARGET)

$(TARGET): $(SOURCE)
	@echo "🔧 Compilando descobrir hash errado para RTX 5090..."
	$(NVCC) $(NVCC_FLAGS) -o $(TARGET) $(SOURCE)
	@echo "✅ Compilação concluída!"

# Versão com arquitetura automática
auto: 
	@echo "🔧 Tentando compilação automática..."
	$(NVCC) -O3 -arch=sm_75 -std=c++11 -o $(TARGET) $(SOURCE) || \
	$(NVCC) -O3 -arch=sm_86 -std=c++11 -o $(TARGET) $(SOURCE) || \
	$(NVCC) -O3 -arch=sm_89 -std=c++11 -o $(TARGET) $(SOURCE)
	@echo "✅ Compilação automática concluída!"

clean:
	rm -f $(TARGET)

# Teste com carteira alvo
test: $(TARGET)
	@echo "🧪 Teste com carteira alvo..."
	./$(TARGET) f6f5431d25bbf7b12e8add9af5e3475c44a0a5b8 1 1000000

# Teste completo com range específico
test-range: $(TARGET)
	@echo "🧪 Teste com range específico..."
	./$(TARGET) f6f5431d25bbf7b12e8add9af5e3475c44a0a5b8 4611686018427387904 4611686018427387904

info:
	@echo "ℹ️  Informações CUDA:"
	nvcc --version
	nvidia-smi --query-gpu=name,compute_cap,memory.total --format=csv

help:
	@echo "🔍 DESCOBRIR HASH ERRADO - Comandos:"
	@echo ""
	@echo "make all         - Compilar programa"
	@echo "make auto        - Compilação automática"
	@echo "make test        - Teste rápido"
	@echo "make test-range  - Teste com range específico"
	@echo "make clean       - Limpar"
	@echo "make info        - Informações CUDA"
	@echo ""
	@echo "Uso manual:"
	@echo "./descobrir_hash_errado <hash160_correto> <start_range> <end_range>"
	@echo ""
	@echo "Exemplo carteira alvo:"
	@echo "./descobrir_hash_errado f6f5431d25bbf7b12e8add9af5e3475c44a0a5b8 4611686018427387904 9223372036854775807"

.PHONY: all auto clean test test-range info help
