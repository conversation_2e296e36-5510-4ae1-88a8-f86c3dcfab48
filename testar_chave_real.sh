#!/bin/bash

# TESTAR CHAVE REAL - Script de Teste

echo "🎯 TESTAR ENCONTRAR CHAVE REAL"
echo "=============================="
echo ""

echo "📋 OPÇÕES DE TESTE:"
echo "1. 🧪 Teste com chave privada 1 (deve funcionar)"
echo "2. 🧪 Teste com chave privada 2 (deve funcionar)"
echo "3. 🎯 Teste com endereço personalizado"
echo "4. 📊 <PERSON><PERSON>r todas as chaves conhecidas"
echo ""

read -p "Escolha uma opção (1-4): " opcao

case $opcao in
    1)
        echo ""
        echo "🧪 TESTANDO CHAVE PRIVADA 1"
        echo "=========================="
        echo "Endereço: 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2"
        echo "Range: 1:10 (deve encontrar chave 1)"
        echo ""
        python3 encontrar_chave_python.py 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2 1:10
        ;;
    
    2)
        echo ""
        echo "🧪 TESTANDO CHAVE PRIVADA 2"
        echo "=========================="
        echo "Endereço: **********************************"
        echo "Range: 1:10 (deve encontrar chave 2)"
        echo ""
        python3 encontrar_chave_python.py ********************************** 1:10
        ;;
    
    3)
        echo ""
        echo "🎯 TESTE COM ENDEREÇO PERSONALIZADO"
        echo "=================================="
        read -p "Digite o endereço Bitcoin: " endereco
        read -p "Digite o range (ex: 1:1000000): " range
        
        if [ -n "$endereco" ] && [ -n "$range" ]; then
            echo ""
            echo "Testando endereço: $endereco"
            echo "Range: $range"
            echo ""
            python3 encontrar_chave_python.py "$endereco" "$range"
        else
            echo "❌ Endereço ou range não fornecido!"
        fi
        ;;
    
    4)
        echo ""
        echo "📊 VALIDANDO TODAS AS CHAVES CONHECIDAS"
        echo "======================================"
        echo "Executando teste de validação..."
        echo ""
        python3 -c "
from encontrar_chave_python import testar_chaves_conhecidas
testar_chaves_conhecidas()
"
        ;;
    
    *)
        echo "❌ Opção inválida!"
        exit 1
        ;;
esac

echo ""
echo "✅ TESTE CONCLUÍDO!"
echo ""
echo "💡 PRÓXIMOS PASSOS:"
echo "Se encontrou a chave real e o hash errado:"
echo "1. Copie o hash errado mostrado"
echo "2. Compile: make -f Makefile_buscar_chave auto"
echo "3. Execute: ./buscar_chave_por_hash_errado [HASH_ERRADO] [RANGE]"
