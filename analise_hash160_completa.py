#!/usr/bin/env python3
"""
Análise completa dos hash160 gerados na GPU e CPU para o endereço **********************************

Este programa mostra:
1. O hash160 REAL do endereço alvo (calculado corretamente pela CPU)
2. Como a GPU calcula hash160 de forma diferente (incorreta)
3. Qual hash160 "errado" a GPU deveria procurar
4. Como implementar a estratégia correta de busca
"""

import os
import sys
import hashlib

# Importar nossos módulos
from bitcoin_conversions import (
    private_key_to_address, private_key_to_hash160, 
    calculate_target_hash160, private_key_to_public_key
)

# Endereço alvo Bitcoin
TARGET_ADDRESS = "**********************************"

def clear_screen():
    """Limpa a tela do console"""
    if os.name == 'nt':  # Windows
        os.system('cls')
    else:  # Linux/Mac
        os.system('clear')

def ripemd160_manual(data):
    """Implementação manual do RIPEMD160"""
    def rol(n, b):
        return ((n << b) | (n >> (32 - b))) & 0xffffffff

    def f(j, x, y, z):
        if j < 16:
            return x ^ y ^ z
        elif j < 32:
            return (x & y) | (~x & z)
        elif j < 48:
            return (x | ~y) ^ z
        elif j < 64:
            return (x & z) | (y & ~z)
        else:
            return x ^ (y | ~z)

    def K(j):
        if j < 16:
            return 0x00000000
        elif j < 32:
            return 0x5A827999
        elif j < 48:
            return 0x6ED9EBA1
        elif j < 64:
            return 0x8F1BBCDC
        else:
            return 0xA953FD4E

    def Kh(j):
        if j < 16:
            return 0x50A28BE6
        elif j < 32:
            return 0x5C4DD124
        elif j < 48:
            return 0x6D703EF3
        elif j < 64:
            return 0x7A6D76E9
        else:
            return 0x00000000

    # Padding
    msg = bytearray(data)
    msg_len = len(data)
    msg.append(0x80)

    while len(msg) % 64 != 56:
        msg.append(0x00)

    msg.extend((msg_len * 8).to_bytes(8, 'little'))

    # Initialize hash values
    h0 = 0x67452301
    h1 = 0xEFCDAB89
    h2 = 0x98BADCFE
    h3 = 0x10325476
    h4 = 0xC3D2E1F0

    # Process message in 512-bit chunks
    for chunk_start in range(0, len(msg), 64):
        chunk = msg[chunk_start:chunk_start + 64]
        w = [int.from_bytes(chunk[i:i+4], 'little') for i in range(0, 64, 4)]

        # Initialize hash value for this chunk
        al, bl, cl, dl, el = h0, h1, h2, h3, h4
        ar, br, cr, dr, er = h0, h1, h2, h3, h4

        # Left line
        r = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
             7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8,
             3, 10, 14, 4, 9, 15, 8, 1, 2, 7, 0, 6, 13, 11, 5, 12,
             1, 9, 11, 10, 0, 8, 12, 4, 13, 3, 7, 15, 14, 5, 6, 2,
             4, 0, 5, 9, 7, 12, 2, 10, 14, 1, 3, 8, 11, 6, 15, 13]

        s = [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8,
             7, 6, 8, 13, 11, 9, 7, 15, 7, 12, 15, 9, 11, 7, 13, 12,
             11, 13, 6, 7, 14, 9, 13, 15, 14, 8, 13, 6, 5, 12, 7, 5,
             11, 12, 14, 15, 14, 15, 9, 8, 9, 14, 5, 6, 8, 6, 5, 12,
             9, 15, 5, 11, 6, 8, 13, 12, 5, 12, 13, 14, 11, 8, 5, 6]

        # Right line
        rh = [5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12,
              6, 11, 3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2,
              15, 5, 1, 3, 7, 14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13,
              8, 6, 4, 1, 3, 11, 15, 0, 5, 12, 2, 13, 9, 7, 10, 14,
              12, 15, 10, 4, 1, 5, 8, 7, 6, 2, 13, 14, 0, 3, 9, 11]

        sh = [8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6,
              9, 13, 15, 7, 12, 8, 9, 11, 7, 7, 12, 7, 6, 15, 13, 11,
              9, 7, 15, 11, 8, 6, 6, 14, 12, 13, 5, 14, 13, 13, 7, 5,
              15, 5, 8, 11, 14, 14, 6, 14, 6, 9, 12, 9, 12, 5, 15, 8,
              8, 5, 12, 9, 12, 5, 14, 6, 8, 13, 6, 5, 15, 13, 11, 11]

        for j in range(80):
            # Left line
            t = (al + f(j, bl, cl, dl) + w[r[j]] + K(j)) & 0xffffffff
            t = rol(t, s[j]) + el & 0xffffffff
            al, bl, cl, dl, el = el, t, bl, rol(cl, 10), dl

            # Right line
            t = (ar + f(79-j, br, cr, dr) + w[rh[j]] + Kh(j)) & 0xffffffff
            t = rol(t, sh[j]) + er & 0xffffffff
            ar, br, cr, dr, er = er, t, br, rol(cr, 10), dr

        # Add this chunk's hash to result so far
        t = (h1 + cl + dr) & 0xffffffff
        h1 = (h2 + dl + er) & 0xffffffff
        h2 = (h3 + el + ar) & 0xffffffff
        h3 = (h4 + al + br) & 0xffffffff
        h4 = (h0 + bl + cr) & 0xffffffff
        h0 = t

    # Produce the final hash value
    return b''.join(h.to_bytes(4, 'little') for h in [h0, h1, h2, h3, h4])

def calculate_gpu_simulated_hash160(private_key_int):
    """Simula o cálculo do hash160 da GPU"""
    try:
        # Valores pré-calculados para chaves conhecidas
        known_pubkeys = {
            1: bytes.fromhex("0279be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798"),
            2: bytes.fromhex("02c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5"),
            3: bytes.fromhex("02f9308a019258c31049344f85f89d5229b531c845836f99b08601f113bce036f9")
        }

        if private_key_int in known_pubkeys:
            public_key = known_pubkeys[private_key_int]
        else:
            # Aproximação da GPU (incorreta)
            gx = [0x79BE667EF9DCBBAC, 0x55A06295CE870B07, 0x029BFCDB2DCE28D9, 0x59F2815B16F81798]
            
            px = []
            for i in range(4):
                px_i = gx[i]
                px_i ^= (private_key_int << (i * 8)) | (private_key_int >> (56 - i * 8))
                px_i = ((px_i << 1) ^ (px_i >> 63)) & 0xFFFFFFFFFFFFFFFF
                px.append(px_i)
            
            y_parity = 2 + ((private_key_int ^ px[0]) & 1)
            public_key = bytes([y_parity])
            
            for i in range(4):
                for j in range(8):
                    byte_val = (px[i] >> (56 - j * 8)) & 0xFF
                    public_key += bytes([byte_val])

        sha256_result = hashlib.sha256(public_key).digest()
        hash160_result = ripemd160_manual(sha256_result)
        
        return hash160_result

    except Exception as e:
        print(f"Erro ao calcular hash160 simulado da GPU: {e}")
        return None

def main():
    """Função principal"""
    clear_screen()
    print("=" * 80)
    print("ANÁLISE COMPLETA DE HASH160 - CPU vs GPU")
    print("Endereço alvo: **********************************")
    print("=" * 80)
    
    # 1. Calcular hash160 real do endereço alvo
    print("\n1. HASH160 REAL DO ENDEREÇO ALVO (CPU)")
    target_hash160 = calculate_target_hash160(TARGET_ADDRESS)
    if target_hash160:
        print(f"Hash160 real: {target_hash160.hex()}")
        print(f"Este é o hash160 que deveria ser encontrado para gerar o endereço alvo.")
    else:
        print("ERRO: Não foi possível calcular hash160 do endereço alvo")
        return
    
    # 2. Mostrar como a GPU calcula diferente
    print("\n2. DIFERENÇAS ENTRE CPU E GPU")
    print("Testando algumas chaves para mostrar as diferenças:")
    
    test_keys = [1, 2, 3, 100, 1000]
    for key in test_keys:
        cpu_hash = private_key_to_hash160(key)
        gpu_hash = calculate_gpu_simulated_hash160(key)
        
        print(f"\nChave {key}:")
        print(f"  CPU: {cpu_hash.hex() if cpu_hash else 'ERRO'}")
        print(f"  GPU: {gpu_hash.hex() if gpu_hash else 'ERRO'}")
        print(f"  Iguais: {'SIM' if cpu_hash == gpu_hash else 'NÃO'}")
    
    # 3. Estratégia de busca
    print("\n3. ESTRATÉGIA DE BUSCA CORRETA")
    print("Para encontrar a chave privada do endereço alvo:")
    print("a) A GPU deve procurar pelo hash160 'errado' que ela calcula")
    print("b) Quando encontrar uma chave, verificar na CPU se gera o endereço correto")
    print("c) Se sim, encontrou a chave! Se não, continuar procurando")
    
    # 4. Demonstração prática
    print("\n4. DEMONSTRAÇÃO PRÁTICA")
    print("Vamos simular encontrar uma chave que gera o hash160 alvo na GPU...")
    
    # Simular busca (limitada para demonstração)
    found_key = None
    for key in range(1, 1000):
        gpu_hash = calculate_gpu_simulated_hash160(key)
        if gpu_hash == target_hash160:
            found_key = key
            break
    
    if found_key:
        print(f"\n🎯 SIMULAÇÃO: Chave {found_key} gera o hash160 alvo na GPU!")
        
        # Verificar na CPU
        cpu_address = private_key_to_address(found_key)
        print(f"Verificando na CPU...")
        print(f"Endereço gerado pela CPU: {cpu_address}")
        print(f"Endereço alvo:            {TARGET_ADDRESS}")
        
        if cpu_address == TARGET_ADDRESS:
            print("✅ SUCESSO! Esta é a chave privada correta!")
        else:
            print("❌ Esta não é a chave correta. Continuar procurando...")
    else:
        print("❌ Não encontrou chave no range de demonstração (1-1000)")
    
    # 5. Configuração recomendada
    print("\n5. CONFIGURAÇÃO RECOMENDADA PARA O PROGRAMA")
    print("Para implementar esta estratégia no programa principal:")
    print("a) Configure a GPU para procurar pelo hash160 real do endereço alvo")
    print(f"   Hash160 alvo: {target_hash160.hex()}")
    print("b) Quando a GPU encontrar uma chave, use a CPU para verificar")
    print("c) Se a CPU confirmar que gera o endereço correto, você encontrou!")
    
    print("\n" + "=" * 80)
    print("RESUMO FINAL:")
    print(f"• Hash160 do endereço alvo: {target_hash160.hex()}")
    print("• A GPU deve procurar por este hash160 exato")
    print("• Quando encontrar, verificar na CPU se gera o endereço correto")
    print("• Esta é a estratégia mais eficiente para manter alta performance")
    print("=" * 80)

if __name__ == "__main__":
    main()
