#!/usr/bin/env python3
"""
DESCOBRIR HASH ERRADO DA CARTEIRA ALVO
Calcula qual seria o hash errado da carteira 1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU
"""

import time
from main import simular_gpu_errada_para_chave, private_key_to_address

def descobrir_hash_errado_carteira_alvo():
    """
    Descobre o hash errado da carteira alvo testando chaves no range conhecido
    """
    print("🎯 DESCOBRIR HASH ERRADO DA CARTEIRA ALVO")
    print("=========================================")
    
    endereco_alvo = "1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU"
    print(f"Carteira alvo: {endereco_alvo}")
    
    # Range onde a chave privada está localizada
    start_range = 4611686018427387904  # 0x400000000000000000
    end_range = 9223372036854775807    # 0x7fffffffffffffffff
    
    print(f"Range de busca: {start_range:,} - {end_range:,}")
    print("⚠️  ATENÇÃO: Este range é muito grande!")
    print("Vamos testar alguns chunks menores primeiro...")
    
    # Testar chunks menores
    chunk_size = 1000000000  # 1 bilhão por chunk
    chunks_para_testar = [
        (start_range, start_range + chunk_size),
        (start_range + chunk_size, start_range + 2 * chunk_size),
        (start_range + 2 * chunk_size, start_range + 3 * chunk_size),
        # Adicionar mais chunks conforme necessário
    ]
    
    print(f"\n🔍 TESTANDO CHUNKS MENORES")
    print("=" * 40)
    
    for i, (chunk_start, chunk_end) in enumerate(chunks_para_testar):
        print(f"\n📍 CHUNK {i+1}: {chunk_start:,} - {chunk_end:,}")
        print(f"Total chaves: {chunk_end - chunk_start:,}")
        
        continuar = input("Testar este chunk? (s/N): ").lower().startswith('s')
        if not continuar:
            continue
        
        print("🔍 Procurando chave privada que gera o endereço alvo...")
        inicio = time.time()
        
        for chave_privada in range(chunk_start, chunk_end):
            try:
                # Gerar endereço para esta chave
                endereco_gerado = private_key_to_address(chave_privada)
                
                if endereco_gerado == endereco_alvo:
                    # ENCONTROU!
                    tempo_busca = time.time() - inicio
                    
                    print(f"\n🎉 CHAVE PRIVADA ENCONTRADA!")
                    print(f"Chave privada: {chave_privada}")
                    print(f"Chave (hex): 0x{chave_privada:x}")
                    print(f"Endereço: {endereco_gerado}")
                    print(f"Tempo de busca: {tempo_busca:.2f} segundos")
                    
                    # Calcular hash errado
                    print(f"\n🎩 CALCULANDO HASH ERRADO...")
                    hash_errado = simular_gpu_errada_para_chave(chave_privada)
                    hash_errado_hex = hash_errado.hex()
                    
                    print(f"✅ HASH ERRADO DESCOBERTO!")
                    print(f"Hash errado: {hash_errado_hex}")
                    
                    # Salvar resultado
                    with open('hash_errado_carteira_alvo.txt', 'w') as f:
                        f.write(f"Carteira alvo: {endereco_alvo}\n")
                        f.write(f"Chave privada: {chave_privada}\n")
                        f.write(f"Chave (hex): 0x{chave_privada:x}\n")
                        f.write(f"Hash errado: {hash_errado_hex}\n")
                        f.write(f"Descoberto em: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    
                    print(f"💾 Resultado salvo em: hash_errado_carteira_alvo.txt")
                    
                    print(f"\n✅ AGORA USE ESTE HASH ERRADO:")
                    print(f"./buscar_chave_por_hash_errado {hash_errado_hex} {start_range}:{end_range}")
                    
                    return hash_errado_hex
                
                # Progresso a cada 10M chaves
                if (chave_privada - chunk_start) % 10000000 == 0:
                    progresso = ((chave_privada - chunk_start) / (chunk_end - chunk_start)) * 100
                    tempo_decorrido = time.time() - inicio
                    velocidade = (chave_privada - chunk_start) / tempo_decorrido if tempo_decorrido > 0 else 0
                    
                    print(f"   Progresso: {progresso:.2f}% | Chave: {chave_privada:,} | Velocidade: {velocidade:.0f} chaves/seg")
                
            except KeyboardInterrupt:
                print(f"\n⏹️  Busca interrompida pelo usuário")
                return None
            except Exception as e:
                continue
        
        print(f"❌ Não encontrada no chunk {i+1}")
    
    print(f"\n❌ CHAVE NÃO ENCONTRADA NOS CHUNKS TESTADOS")
    print("💡 A chave pode estar em um chunk não testado")
    print("💡 Use o programa CUDA para busca mais rápida")
    
    return None

def testar_hashes_conhecidos():
    """
    Testa os hashes errados conhecidos para validar a lógica
    """
    print("\n🧪 TESTANDO HASHES ERRADOS CONHECIDOS")
    print("=" * 40)
    
    chaves_conhecidas = [
        (1, "1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2"),
        (2, "1JwSSubhmg6iPtRjtyqhUYYH7bZg3Lfy1T"),
        (3, "1C4vLLyKHdMsQ4vw5xnATkxgXLHdniaA1s"),
    ]
    
    for chave_privada, endereco_esperado in chaves_conhecidas:
        print(f"\n🔍 Testando chave privada {chave_privada}:")
        
        # Gerar endereço
        endereco_gerado = private_key_to_address(chave_privada)
        print(f"   Endereço gerado: {endereco_gerado}")
        print(f"   Endereço esperado: {endereco_esperado}")
        
        if endereco_gerado == endereco_esperado:
            print(f"   ✅ Endereço correto!")
        else:
            print(f"   ❌ Endereço incorreto!")
        
        # Calcular hash errado
        hash_errado = simular_gpu_errada_para_chave(chave_privada)
        hash_errado_hex = hash_errado.hex()
        print(f"   Hash errado: {hash_errado_hex}")

def main():
    """Função principal"""
    print("🎯 DESCOBRIR HASH ERRADO - CARTEIRA ALVO")
    print("=" * 50)
    
    print("\nEscolha uma opção:")
    print("1. 🧪 Testar hashes conhecidos (validação)")
    print("2. 🎯 Descobrir hash errado da carteira alvo")
    print("3. 📋 Mostrar informações da carteira alvo")
    
    opcao = input("\nOpção (1/2/3): ").strip()
    
    if opcao == "1":
        testar_hashes_conhecidos()
        
    elif opcao == "2":
        hash_errado = descobrir_hash_errado_carteira_alvo()
        if hash_errado:
            print(f"\n🎊 SUCESSO!")
            print(f"Hash errado descoberto: {hash_errado}")
        else:
            print(f"\n😞 Não foi possível descobrir o hash errado")
            
    elif opcao == "3":
        print(f"\n📋 INFORMAÇÕES DA CARTEIRA ALVO")
        print("=" * 40)
        print(f"Endereço: 1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU")
        print(f"Range conhecido: 400000000000000000:7fffffffffffffffff")
        print(f"Total chaves no range: ~4.6 trilhões")
        print(f"Tempo estimado (Python): Vários dias")
        print(f"Tempo estimado (CUDA): 2-5 horas")
        
    else:
        print("❌ Opção inválida!")

if __name__ == "__main__":
    main()
