#!/bin/bash

# EXEMPLO DE USO - Descobrir Hash Errado Interativo

echo "🎯 EXEMPLO DE USO - DESCOBRIR HASH ERRADO"
echo "========================================="
echo ""

# Verificar se programa existe
if [ ! -f "./descobrir_interativo" ]; then
    echo "❌ Programa não encontrado!"
    echo "💡 Compile primeiro:"
    echo "   make -f Makefile_interativo auto"
    exit 1
fi

echo "✅ Programa encontrado: ./descobrir_interativo"
echo ""

# Função para executar exemplo
executar_exemplo() {
    local titulo="$1"
    local endereco="$2"
    local range="$3"
    local descricao="$4"
    
    echo "📍 $titulo"
    echo "$(printf '=%.0s' {1..50})"
    echo "Endereço: $endereco"
    echo "Range: $range"
    echo "Descrição: $descricao"
    echo ""
    
    read -p "Executar este exemplo? (s/N): " executar
    if [[ $executar =~ ^[Ss]$ ]]; then
        echo "🚀 Executando..."
        ./descobrir_interativo "$endereco" "$range"
        echo ""
        echo "✅ Exemplo concluído!"
        echo ""
    else
        echo "⏭️  Pulando exemplo"
        echo ""
    fi
}

# EXEMPLO 1: Teste rápido com chave conhecida
executar_exemplo \
    "EXEMPLO 1: Teste Rápido" \
    "1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2" \
    "1:1000000" \
    "Busca a chave privada 1 (deve encontrar rapidamente)"

# EXEMPLO 2: Range hexadecimal
executar_exemplo \
    "EXEMPLO 2: Range Hexadecimal" \
    "1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2" \
    "1:ffffff" \
    "Mesmo teste mas com range em hexadecimal"

# EXEMPLO 3: Range maior
executar_exemplo \
    "EXEMPLO 3: Range Maior" \
    "1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2" \
    "1:10000000" \
    "Range maior para testar velocidade"

# EXEMPLO 4: Carteira alvo (chunk pequeno)
executar_exemplo \
    "EXEMPLO 4: Carteira Alvo (Chunk)" \
    "1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU" \
    "400000000000000000:400000000001000000" \
    "Pequeno chunk da carteira alvo (16M chaves)"

# EXEMPLO 5: Modo interativo
echo "📍 EXEMPLO 5: Modo Interativo"
echo "$(printf '=%.0s' {1..50})"
echo "Neste exemplo, o programa vai perguntar o endereço e range"
echo ""

read -p "Executar modo interativo? (s/N): " executar_interativo
if [[ $executar_interativo =~ ^[Ss]$ ]]; then
    echo ""
    echo "🚀 Executando modo interativo..."
    echo "💡 Sugestões para testar:"
    echo "   Endereço: 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2"
    echo "   Range: 1:1000000"
    echo ""
    ./descobrir_interativo
else
    echo "⏭️  Pulando modo interativo"
fi

echo ""
echo "🎊 EXEMPLOS CONCLUÍDOS!"
echo "======================"
echo ""
echo "💡 COMO USAR OS RESULTADOS:"
echo ""
echo "Quando o programa encontrar o hash errado, você verá algo como:"
echo "🎩 Hash160 errado: a1b2c3d4e5f6789012345678901234567890abcd"
echo ""
echo "Use este hash no programa principal:"
echo "./busca_cuda a1b2c3d4e5f6789012345678901234567890abcd 1 1000000"
echo ""
echo "📋 FORMATOS DE RANGE ACEITOS:"
echo "• Decimal: 1000000:2000000"
echo "• Hex: 1000000:1ffffff"
echo "• Hex com 0x: 0x1000000:0x1ffffff"
echo ""
echo "🎯 ENDEREÇOS PARA TESTAR:"
echo "• 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2 (chave privada 1)"
echo "• 1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU (carteira alvo)"
echo "• 1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa (Genesis block)"
echo ""
echo "🚀 Para usar:"
echo "./descobrir_interativo [endereco] [range]"
