#!/bin/bash

# BUSCA NO RANGE ESPECÍFICO
# Range: 400000000000000000:7fffffffffffffffff

echo "🎯 BUSCA NO RANGE ESPECÍFICO"
echo "============================"
echo "Carteira alvo: 1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU"
echo "Hash errado:   15dcad75ce214766086340311434d412874c7e77"
echo ""
echo "📊 RANGE CONFIGURADO:"
echo "Start (hex): 0x400000000000000000"
echo "End (hex):   0x7fffffffffffffffff"
echo "Start (dec): 4611686018427387904"
echo "End (dec):   9223372036854775807"
echo "Total:       ~4.6 trilhões de chaves"
echo ""

# Verificar se o programa existe
if [ ! -f "./busca_cuda" ]; then
    echo "❌ Programa busca_cuda não encontrado!"
    echo "Execute: make"
    exit 1
fi

# Configurações
TARGET_HASH="15dcad75ce214766086340311434d412874c7e77"
START_RANGE="4611686018427387904"
END_RANGE="9223372036854775807"

# Verificar GPU
echo "🔍 VERIFICANDO GPU..."
nvidia-smi --query-gpu=name,memory.total,utilization.gpu --format=csv,noheader

echo ""
echo "⚠️  ATENÇÃO: Esta busca pode levar várias horas!"
echo "Range total: 4.6 trilhões de chaves"
echo "Com RTX 5090: Estimativa de 2-5 horas"
echo ""

read -p "Continuar com a busca? (s/N): " continuar

if [[ ! $continuar =~ ^[Ss]$ ]]; then
    echo "Busca cancelada."
    exit 0
fi

echo ""
echo "🚀 INICIANDO BUSCA NO RANGE ESPECÍFICO..."
echo "========================================="

# Registrar início
start_time=$(date +%s)
echo "Início: $(date)"
echo ""

# Executar busca CUDA
echo "Executando comando:"
echo "./busca_cuda $TARGET_HASH $START_RANGE $END_RANGE"
echo ""

./busca_cuda "$TARGET_HASH" "$START_RANGE" "$END_RANGE"
result=$?

# Registrar fim
end_time=$(date +%s)
duration=$((end_time - start_time))

echo ""
echo "📊 RESULTADO DA BUSCA"
echo "===================="
echo "Fim: $(date)"
echo "Tempo total: ${duration} segundos ($((duration / 60)) minutos)"

if [ $result -eq 0 ]; then
    echo "🎉 SUCESSO! Chave privada encontrada!"
    echo ""
    echo "🔍 VALIDANDO RESULTADO..."
    
    # Extrair a chave encontrada do output (seria necessário modificar o programa para salvar)
    echo "Execute para validar:"
    echo "python3 verificar_resultado_cuda.py <chave_encontrada>"
    
else
    echo "❌ Chave não encontrada no range especificado"
    echo ""
    echo "💡 POSSÍVEIS CAUSAS:"
    echo "1. Hash160 errado pode estar incorreto"
    echo "2. Chave pode estar fora deste range"
    echo "3. Erro no algoritmo de cálculo"
fi

echo ""
echo "📈 ESTATÍSTICAS:"
if [ $duration -gt 0 ]; then
    total_keys=$((END_RANGE - START_RANGE))
    keys_per_second=$((total_keys / duration))
    echo "Chaves testadas: $total_keys"
    echo "Velocidade média: $keys_per_second chaves/segundo"
    echo "Velocidade: $((keys_per_second / 1000000)) M chaves/segundo"
fi
