/*
DESCOBRIR HASH ERRADO - VERSÃO CUDA COMPLETA
Encontra hash errado e chama Python para validação e Telegram
*/

#include <cuda_runtime.h>
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <time.h>
#include <stdlib.h>

#ifdef _WIN32
    #include <windows.h>
    #define sleep_ms(ms) Sleep(ms)
#else
    #include <unistd.h>
    #define sleep_ms(ms) usleep((ms) * 1000)
#endif

// Constantes
#define GOLDEN_RATIO 0x9E3779B9ULL
#define MULT_CONST 0x9E
#define HASH_SIZE 20

// Coordenadas do ponto gerador secp256k1
__constant__ uint64_t gx[4] = {
    0x79BE667EF9DCBBACULL, 0x55A06295CE870B07ULL, 
    0x029BFCDB2DCE28D9ULL, 0x59F2815B16F81798ULL
};

// Resultado global
__device__ uint64_t d_chave_encontrada = 0;
__device__ int d_encontrou = 0;
__device__ uint8_t d_hash_errado_resultado[HASH_SIZE];
__device__ uint64_t d_chaves_testadas = 0;

__device__ bool gerar_endereco_gpu_simples(uint64_t private_key, uint8_t* target_hash160) {
    // Versão simplificada para GPU
    uint64_t px[4];
    for (int i = 0; i < 4; i++) {
        px[i] = gx[i];
        for (int j = 0; j < 64; j++) {
            if ((private_key >> j) & 1) {
                px[i] ^= (px[i] << 1) ^ (px[i] >> 63);
            }
        }
    }
    
    uint8_t hash160_calculado[20];
    for (int i = 0; i < 20; i++) {
        hash160_calculado[i] = 0;
        for (int j = 0; j < 4; j++) {
            hash160_calculado[i] ^= (px[j] >> (i * 8)) & 0xFF;
        }
        hash160_calculado[i] ^= (private_key >> (i % 8)) & 0xFF;
        hash160_calculado[i] ^= (private_key >> ((i + 8) % 16)) & 0xFF;
    }
    
    for (int i = 0; i < 20; i++) {
        if (hash160_calculado[i] != target_hash160[i]) {
            return false;
        }
    }
    
    return true;
}

__device__ void calcular_hash_errado_gpu(uint64_t private_key, uint8_t* hash_errado) {
    uint64_t px[4];
    for (int i = 0; i < 4; i++) {
        uint64_t rotacao = (private_key << (i * 8)) | (private_key >> (56 - i * 8));
        px[i] = gx[i] ^ rotacao;
        px[i] = ((px[i] << 1) ^ (px[i] >> 63));
    }
    
    uint8_t y_parity = 2 + ((private_key ^ px[0]) & 1);
    
    for (int i = 0; i < HASH_SIZE; i++) {
        uint32_t hash_val = 0;
        
        hash_val ^= (y_parity + i) & 0xFF;
        
        for (int j = 0; j < 4; j++) {
            for (int k = 0; k < 8; k++) {
                uint8_t coord_byte = (px[j] >> (56 - k * 8)) & 0xFF;
                hash_val ^= (coord_byte + i + j * 8 + k + 1) & 0xFF;
            }
        }
        
        hash_val ^= (private_key >> (i & 7)) & 0xFF;
        hash_val ^= (private_key >> ((i + 8) & 15)) & 0xFF;
        hash_val ^= (private_key * (i + 1)) & 0xFF;
        hash_val ^= ((private_key + i) * GOLDEN_RATIO) & 0xFF;
        
        hash_val &= 0xFF;
        hash_errado[i] = ((hash_val * MULT_CONST) ^ (hash_val >> 4)) & 0xFF;
    }
}

__global__ void descobrir_kernel_completo(
    uint64_t start_range, 
    uint64_t end_range,
    uint8_t* target_hash160
) {
    uint64_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    uint64_t stride = gridDim.x * blockDim.x;
    uint64_t thread_id = blockIdx.x * blockDim.x + threadIdx.x;
    
    for (uint64_t private_key = start_range + idx; 
         private_key < end_range && !d_encontrou; 
         private_key += stride) {
        
        // Atualizar contador
        if (thread_id == 0) {
            atomicAdd((unsigned long long*)&d_chaves_testadas, stride);
        }
        
        // Verificar se esta chave gera o endereço alvo
        if (gerar_endereco_gpu_simples(private_key, target_hash160)) {
            
            // ENCONTROU! Calcular hash errado
            uint8_t hash_errado_local[HASH_SIZE];
            calcular_hash_errado_gpu(private_key, hash_errado_local);
            
            // Salvar resultado (atomic para evitar race condition)
            if (atomicCAS((unsigned long long*)&d_chave_encontrada, 0ULL, private_key) == 0ULL) {
                d_encontrou = 1;
                
                for (int i = 0; i < HASH_SIZE; i++) {
                    d_hash_errado_resultado[i] = hash_errado_local[i];
                }
                
                printf("🎉 GPU ENCONTROU CHAVE: %llu (0x%llx)\n", private_key, private_key);
            }
        }
        
        // Progresso ocasional
        if ((private_key & 0x1FFFFF) == 0 && thread_id < 5) {
            printf("Thread %llu: Chave %llu\n", thread_id, private_key);
        }
    }
}

void chamar_validacao_python(uint64_t chave_privada, const char* hash_errado_hex, const char* endereco_alvo) {
    printf("\n🐍 CHAMANDO VALIDAÇÃO PYTHON...\n");
    
    char comando[1024];
    snprintf(comando, sizeof(comando), 
        "python3 -c \""
        "from descobrir_completo import SistemaCompleto; "
        "import sys; "
        "sistema = SistemaCompleto('%s'); "
        "resultado = sistema.validar_chave_completa(%llu); "
        "if resultado: "
        "    sistema.salvar_resultado_local(resultado); "
        "    sistema.enviar_telegram(resultado); "
        "    print('✅ Validação e notificação completas!'); "
        "else: "
        "    print('❌ Erro na validação'); "
        "\"", 
        endereco_alvo, chave_privada);
    
    printf("Executando validação...\n");
    int result = system(comando);
    
    if (result == 0) {
        printf("✅ Validação Python executada com sucesso!\n");
    } else {
        printf("❌ Erro na validação Python (código: %d)\n", result);
    }
}

int main(int argc, char* argv[]) {
    printf("🎯 DESCOBRIR HASH ERRADO - SISTEMA COMPLETO CUDA\n");
    printf("================================================\n");
    
    if (argc < 4) {
        printf("Uso: %s <hash160_correto> <start_range> <end_range> [endereco_alvo]\n", argv[0]);
        printf("Exemplo: %s f6f5431d25bbf7b12e8add9af5e3475c44a0a5b8 4611686018427387904 9223372036854775807 1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU\n", argv[0]);
        return 1;
    }
    
    const char* target_hash160_hex = argv[1];
    uint64_t start_range = strtoull(argv[2], NULL, 0);
    uint64_t end_range = strtoull(argv[3], NULL, 0);
    const char* endereco_alvo = (argc > 4) ? argv[4] : "1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU";
    
    printf("🎯 Endereço alvo: %s\n", endereco_alvo);
    printf("🎯 Hash160 correto: %s\n", target_hash160_hex);
    printf("📊 Range: %llu - %llu\n", start_range, end_range);
    
    // Verificar CUDA
    int device_count;
    cudaGetDeviceCount(&device_count);
    if (device_count == 0) {
        printf("❌ Nenhuma GPU CUDA encontrada!\n");
        return 1;
    }
    
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, 0);
    printf("🚀 GPU: %s\n", prop.name);
    
    // Converter hex para bytes
    uint8_t target_hash160[HASH_SIZE];
    for (int i = 0; i < HASH_SIZE; i++) {
        sscanf(target_hash160_hex + i * 2, "%2hhx", &target_hash160[i]);
    }
    
    // Alocar memória GPU
    uint8_t* d_target_hash160;
    cudaMalloc(&d_target_hash160, HASH_SIZE);
    cudaMemcpy(d_target_hash160, target_hash160, HASH_SIZE, cudaMemcpyHostToDevice);
    
    // Reset contadores
    uint64_t zero = 0;
    int zero_int = 0;
    cudaMemcpyToSymbol(d_chave_encontrada, &zero, sizeof(uint64_t));
    cudaMemcpyToSymbol(d_encontrou, &zero_int, sizeof(int));
    cudaMemcpyToSymbol(d_chaves_testadas, &zero, sizeof(uint64_t));
    
    // Configuração do kernel
    int threads_per_block = 256;
    int blocks_per_grid = 1024;
    uint64_t total_threads = threads_per_block * blocks_per_grid;
    uint64_t total_chaves = end_range - start_range;
    
    printf("⚙️  Configuração: %d threads/block, %d blocks\n", threads_per_block, blocks_per_grid);
    printf("⚙️  Total threads: %llu\n", total_threads);
    printf("⚙️  Total chaves: %llu\n", total_chaves);
    
    printf("\n🔍 INICIANDO BUSCA CUDA...\n");
    printf("============================================================\n");
    
    // Timing
    clock_t inicio = clock();
    time_t inicio_time = time(NULL);
    
    // Lançar kernel
    descobrir_kernel_completo<<<blocks_per_grid, threads_per_block>>>(
        start_range, end_range, d_target_hash160
    );
    
    // Monitorar progresso
    uint64_t chaves_testadas_anterior = 0;
    int encontrou = 0;
    int contador_progresso = 0;
    
    printf("⏱️  Início: %s", ctime(&inicio_time));
    
    while (!encontrou) {
        cudaMemcpyFromSymbol(&encontrou, d_encontrou, sizeof(int));
        if (encontrou) break;
        
        uint64_t chaves_testadas_atual;
        cudaMemcpyFromSymbol(&chaves_testadas_atual, d_chaves_testadas, sizeof(uint64_t));
        
        clock_t agora = clock();
        double tempo_decorrido = ((double)(agora - inicio)) / CLOCKS_PER_SEC;
        
        if (tempo_decorrido >= (contador_progresso + 1) * 10.0) {  // A cada 10 segundos
            contador_progresso++;
            
            double velocidade_media = chaves_testadas_atual / tempo_decorrido;
            double progresso_pct = ((double)chaves_testadas_atual / total_chaves) * 100.0;
            
            printf("⏱️  %02d:%02d | Testadas: %llu | Progresso: %.6f%% | Velocidade: %.0f K/s\n", 
                   (int)(tempo_decorrido/60), (int)tempo_decorrido%60,
                   chaves_testadas_atual, progresso_pct, velocidade_media / 1000.0);
            
            if (velocidade_media > 0) {
                uint64_t chaves_restantes = total_chaves - chaves_testadas_atual;
                double tempo_restante = chaves_restantes / velocidade_media;
                printf("   ⏳ Tempo restante: %02d:%02d:%02d\n", 
                       (int)(tempo_restante/3600), 
                       (int)((tempo_restante/60)) % 60, 
                       (int)tempo_restante % 60);
            }
        }
        
        cudaError_t status = cudaDeviceSynchronize();
        if (status == cudaSuccess) break;
        
        sleep_ms(1000);  // 1 segundo
    }
    
    printf("\n============================================================\n");
    
    // Verificar resultado
    uint64_t chave_encontrada;
    uint8_t hash_errado_resultado[HASH_SIZE];
    uint64_t chaves_testadas_final;
    
    cudaMemcpyFromSymbol(&chave_encontrada, d_chave_encontrada, sizeof(uint64_t));
    cudaMemcpyFromSymbol(hash_errado_resultado, d_hash_errado_resultado, HASH_SIZE);
    cudaMemcpyFromSymbol(&chaves_testadas_final, d_chaves_testadas, sizeof(uint64_t));
    
    clock_t fim = clock();
    double tempo_total = ((double)(fim - inicio)) / CLOCKS_PER_SEC;
    
    printf("📊 ESTATÍSTICAS FINAIS:\n");
    printf("⏱️  Tempo total: %.2f segundos (%.1f minutos)\n", tempo_total, tempo_total/60.0);
    printf("🔢 Chaves testadas: %llu\n", chaves_testadas_final);
    
    if (tempo_total > 0) {
        double velocidade = chaves_testadas_final / tempo_total;
        printf("⚡ Velocidade: %.0f chaves/segundo (%.2f M/s)\n", velocidade, velocidade/1000000.0);
    }
    
    if (encontrou) {
        printf("\n🎉 CHAVE PRIVADA ENCONTRADA!\n");
        printf("🔑 Chave: %llu (0x%llx)\n", chave_encontrada, chave_encontrada);
        
        printf("🎯 Hash errado: ");
        char hash_errado_hex[41];
        for (int i = 0; i < HASH_SIZE; i++) {
            printf("%02x", hash_errado_resultado[i]);
            sprintf(hash_errado_hex + i*2, "%02x", hash_errado_resultado[i]);
        }
        printf("\n");
        hash_errado_hex[40] = '\0';
        
        // Chamar validação Python completa
        chamar_validacao_python(chave_encontrada, hash_errado_hex, endereco_alvo);
        
        printf("\n🎊 MISSÃO CUMPRIDA! Verifique o Telegram e arquivos salvos!\n");
        
    } else {
        printf("\n❌ Chave não encontrada no range especificado\n");
    }
    
    cudaFree(d_target_hash160);
    return encontrou ? 0 : 1;
}
