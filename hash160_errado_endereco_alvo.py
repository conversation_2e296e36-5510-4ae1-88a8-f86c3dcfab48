#!/usr/bin/env python3
"""
Programa para descobrir qual é o hash160 ERRADO que a GPU geraria
para o endereço **********************************

OBJETIVO:
Mostrar qual hash160 ERRADO a GPU deve procurar para encontrar
a chave privada que gera o endereço alvo.

Baseado na memória do usuário:
- CPU (correto): f6f5431d25bbf7b12e8add9af5e3475c44a0a5b8
- GPU (errado):  15dcad75ce214766086340311434d412874c7e77
"""

import os
import sys
from bitcoin_conversions import calculate_target_hash160

# Endereço alvo Bitcoin
TARGET_ADDRESS = "**********************************"

def clear_screen():
    """Limpa a tela do console"""
    if os.name == 'nt':  # Windows
        os.system('cls')
    else:  # Linux/Mac
        os.system('clear')

def main():
    """Função principal"""
    clear_screen()
    print("=" * 80)
    print("HASH160 ERRADO DA GPU PARA O ENDEREÇO ALVO")
    print("Endereço: **********************************")
    print("=" * 80)
    
    # Calcular hash160 correto
    hash160_correto = calculate_target_hash160(TARGET_ADDRESS)
    
    print(f"\n📍 ENDEREÇO ALVO:")
    print(f"   {TARGET_ADDRESS}")
    
    print(f"\n✅ HASH160 CORRETO (CPU):")
    print(f"   {hash160_correto.hex()}")
    
    print(f"\n❌ HASH160 ERRADO (GPU):")
    # Baseado na sua memória e nos testes anteriores
    hash160_errado = "15dcad75ce214766086340311434d412874c7e77"
    print(f"   {hash160_errado}")
    
    print(f"\n" + "=" * 80)
    print("🎯 CONFIGURAÇÃO PARA O PROGRAMA")
    print("=" * 80)
    
    print(f"\n📝 HASH160 QUE A GPU DEVE PROCURAR:")
    print(f"   {hash160_errado}")
    
    print(f"\n🔧 CONFIGURAÇÃO NO MAIN.PY:")
    print(f"   TARGET_HASH160 = bytes.fromhex('{hash160_errado}')")
    
    print(f"\n🔧 ARRAY PARA CÓDIGO C/CUDA:")
    # Converter para array de bytes
    hash_bytes = bytes.fromhex(hash160_errado)
    hash_array = ", ".join([f"0x{b:02x}" for b in hash_bytes])
    print(f"   uint8_t target_hash160[20] = {{{hash_array}}};")
    
    print(f"\n⚡ COMO FUNCIONA:")
    print(f"   1. GPU procura pelo hash160 ERRADO: {hash160_errado}")
    print(f"   2. Quando GPU encontrar uma chave que gera este hash160 errado,")
    print(f"   3. Verificar na CPU se essa chave gera o endereço: {TARGET_ADDRESS}")
    print(f"   4. Se SIM: SUCESSO! Chave privada encontrada!")
    print(f"   5. Se NÃO: Continuar procurando")
    
    print(f"\n📋 RESUMO:")
    print(f"   • Endereço alvo: {TARGET_ADDRESS}")
    print(f"   • Hash160 correto: {hash160_correto.hex()}")
    print(f"   • Hash160 errado:  {hash160_errado}")
    print(f"   • GPU deve procurar pelo hash160 ERRADO")
    print(f"   • CPU verifica se gera o endereço correto")
    
    print(f"\n" + "=" * 80)
    print("✅ RESPOSTA FINAL:")
    print("=" * 80)
    print(f"\nO hash160 ERRADO que a GPU deve procurar é:")
    print(f"🎯 {hash160_errado}")
    print(f"\nEste é o target que você deve configurar no programa!")
    print("=" * 80)

if __name__ == "__main__":
    main()
