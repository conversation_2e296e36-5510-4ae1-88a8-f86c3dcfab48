#!/usr/bin/env python3
"""
Programa para descobrir qual é o hash160 ERRADO que a GPU está gerando
para o endereço Bitcoin **********************************

Este programa:
1. Encontra a chave privada REAL que gera o endereço alvo na CPU
2. Calcula qual hash160 ERRADO a GPU geraria para essa chave
3. Mostra qual valor a GPU deveria procurar para encontrar a chave
"""

import os
import sys
import hashlib

# Importar nossos módulos
from bitcoin_conversions import (
    private_key_to_address, private_key_to_hash160, 
    calculate_target_hash160, private_key_to_public_key
)

# Endereço alvo Bitcoin
TARGET_ADDRESS = "**********************************"

def clear_screen():
    """Limpa a tela do console"""
    if os.name == 'nt':  # Windows
        os.system('cls')
    else:  # Linux/Mac
        os.system('clear')

def ripemd160_manual(data):
    """Implementação manual do RIPEMD160 (igual à da GPU)"""
    def rol(n, b):
        return ((n << b) | (n >> (32 - b))) & 0xffffffff

    def f(j, x, y, z):
        if j < 16:
            return x ^ y ^ z
        elif j < 32:
            return (x & y) | (~x & z)
        elif j < 48:
            return (x | ~y) ^ z
        elif j < 64:
            return (x & z) | (y & ~z)
        else:
            return x ^ (y | ~z)

    def K(j):
        if j < 16:
            return 0x00000000
        elif j < 32:
            return 0x5A827999
        elif j < 48:
            return 0x6ED9EBA1
        elif j < 64:
            return 0x8F1BBCDC
        else:
            return 0xA953FD4E

    def Kh(j):
        if j < 16:
            return 0x50A28BE6
        elif j < 32:
            return 0x5C4DD124
        elif j < 48:
            return 0x6D703EF3
        elif j < 64:
            return 0x7A6D76E9
        else:
            return 0x00000000

    # Padding
    msg = bytearray(data)
    msg_len = len(data)
    msg.append(0x80)

    while len(msg) % 64 != 56:
        msg.append(0x00)

    msg.extend((msg_len * 8).to_bytes(8, 'little'))

    # Initialize hash values
    h0 = 0x67452301
    h1 = 0xEFCDAB89
    h2 = 0x98BADCFE
    h3 = 0x10325476
    h4 = 0xC3D2E1F0

    # Process message in 512-bit chunks
    for chunk_start in range(0, len(msg), 64):
        chunk = msg[chunk_start:chunk_start + 64]
        w = [int.from_bytes(chunk[i:i+4], 'little') for i in range(0, 64, 4)]

        # Initialize hash value for this chunk
        al, bl, cl, dl, el = h0, h1, h2, h3, h4
        ar, br, cr, dr, er = h0, h1, h2, h3, h4

        # Left line
        r = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
             7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8,
             3, 10, 14, 4, 9, 15, 8, 1, 2, 7, 0, 6, 13, 11, 5, 12,
             1, 9, 11, 10, 0, 8, 12, 4, 13, 3, 7, 15, 14, 5, 6, 2,
             4, 0, 5, 9, 7, 12, 2, 10, 14, 1, 3, 8, 11, 6, 15, 13]

        s = [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8,
             7, 6, 8, 13, 11, 9, 7, 15, 7, 12, 15, 9, 11, 7, 13, 12,
             11, 13, 6, 7, 14, 9, 13, 15, 14, 8, 13, 6, 5, 12, 7, 5,
             11, 12, 14, 15, 14, 15, 9, 8, 9, 14, 5, 6, 8, 6, 5, 12,
             9, 15, 5, 11, 6, 8, 13, 12, 5, 12, 13, 14, 11, 8, 5, 6]

        # Right line
        rh = [5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12,
              6, 11, 3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2,
              15, 5, 1, 3, 7, 14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13,
              8, 6, 4, 1, 3, 11, 15, 0, 5, 12, 2, 13, 9, 7, 10, 14,
              12, 15, 10, 4, 1, 5, 8, 7, 6, 2, 13, 14, 0, 3, 9, 11]

        sh = [8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6,
              9, 13, 15, 7, 12, 8, 9, 11, 7, 7, 12, 7, 6, 15, 13, 11,
              9, 7, 15, 11, 8, 6, 6, 14, 12, 13, 5, 14, 13, 13, 7, 5,
              15, 5, 8, 11, 14, 14, 6, 14, 6, 9, 12, 9, 12, 5, 15, 8,
              8, 5, 12, 9, 12, 5, 14, 6, 8, 13, 6, 5, 15, 13, 11, 11]

        for j in range(80):
            # Left line
            t = (al + f(j, bl, cl, dl) + w[r[j]] + K(j)) & 0xffffffff
            t = rol(t, s[j]) + el & 0xffffffff
            al, bl, cl, dl, el = el, t, bl, rol(cl, 10), dl

            # Right line
            t = (ar + f(79-j, br, cr, dr) + w[rh[j]] + Kh(j)) & 0xffffffff
            t = rol(t, sh[j]) + er & 0xffffffff
            ar, br, cr, dr, er = er, t, br, rol(cr, 10), dr

        # Add this chunk's hash to result so far
        t = (h1 + cl + dr) & 0xffffffff
        h1 = (h2 + dl + er) & 0xffffffff
        h2 = (h3 + el + ar) & 0xffffffff
        h3 = (h4 + al + br) & 0xffffffff
        h4 = (h0 + bl + cr) & 0xffffffff
        h0 = t

    # Produce the final hash value
    return b''.join(h.to_bytes(4, 'little') for h in [h0, h1, h2, h3, h4])

def calculate_gpu_wrong_hash160(private_key_int):
    """
    Calcula exatamente o hash160 ERRADO que a GPU geraria para uma chave privada.
    Esta função replica EXATAMENTE a lógica incorreta da GPU.
    """
    try:
        # Valores pré-calculados para chaves conhecidas (do kernel CUDA)
        known_pubkeys = {
            1: bytes.fromhex("0279be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798"),
            2: bytes.fromhex("02c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5"),
            3: bytes.fromhex("02f9308a019258c31049344f85f89d5229b531c845836f99b08601f113bce036f9")
        }

        # Para chaves conhecidas, usar valores pré-calculados
        if private_key_int in known_pubkeys:
            public_key = known_pubkeys[private_key_int]
        else:
            # Para outras chaves, usar a aproximação ERRADA da GPU
            # Coordenadas do ponto gerador secp256k1
            gx = [0x79BE667EF9DCBBAC, 0x55A06295CE870B07, 0x029BFCDB2DCE28D9, 0x59F2815B16F81798]
            
            # Aplicar transformação baseada na chave privada (INCORRETA)
            px = []
            for i in range(4):
                px_i = gx[i]
                # Operações que dependem da chave privada (INCORRETAS)
                px_i ^= (private_key_int << (i * 8)) | (private_key_int >> (56 - i * 8))
                px_i = ((px_i << 1) ^ (px_i >> 63)) & 0xFFFFFFFFFFFFFFFF
                px.append(px_i)
            
            # Determinar paridade Y (simplificado e INCORRETO)
            y_parity = 2 + ((private_key_int ^ px[0]) & 1)
            
            # Construir chave pública INCORRETA
            public_key = bytes([y_parity])
            
            # Converter coordenada X para bytes (big-endian)
            for i in range(4):
                for j in range(8):
                    byte_val = (px[i] >> (56 - j * 8)) & 0xFF
                    public_key += bytes([byte_val])

        # SHA256 da chave pública (INCORRETA)
        sha256_result = hashlib.sha256(public_key).digest()
        
        # RIPEMD160 do SHA256
        hash160_result = ripemd160_manual(sha256_result)
        
        return hash160_result

    except Exception as e:
        print(f"Erro ao calcular hash160 errado da GPU: {e}")
        return None

def encontrar_chave_real_do_endereco():
    """
    Encontra a chave privada REAL que gera o endereço alvo.
    NOTA: Esta é uma busca de demonstração. Na prática, seria muito mais complexa.
    """
    print("=== PROCURANDO CHAVE REAL DO ENDEREÇO ALVO ===")
    print(f"Endereço alvo: {TARGET_ADDRESS}")
    
    # Para demonstração, vamos testar algumas chaves conhecidas
    # Na prática, você precisaria de informações adicionais ou uma busca mais ampla
    
    print("\nTestando chaves conhecidas...")
    
    # Testar algumas chaves específicas que podem estar relacionadas ao endereço
    test_keys = [
        # Chaves pequenas
        1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
        # Chaves com padrões
        0x123456789abcdef0,
        0x1111111111111111,
        0x2222222222222222,
        # Range do programa
        0x400000000000000000,
        0x400000000000000001,
        0x400000000000000002,
    ]
    
    for key in test_keys:
        address = private_key_to_address(key)
        if address == TARGET_ADDRESS:
            print(f"\n🎯 ENCONTROU! Chave {key:064x} gera o endereço alvo!")
            return key
    
    print("\n❌ Chave não encontrada no conjunto de teste.")
    print("NOTA: Para encontrar a chave real, seria necessária uma busca mais ampla.")
    print("Vamos simular com uma chave de exemplo para demonstrar o conceito...")
    
    # Para demonstração, vamos usar uma chave de exemplo
    exemplo_key = 0x400000000000000000
    print(f"\n📝 SIMULAÇÃO: Usando chave de exemplo {exemplo_key:064x}")
    return exemplo_key

def main():
    """Função principal"""
    clear_screen()
    print("=" * 80)
    print("DESCOBRIDOR DO HASH160 ERRADO DA GPU")
    print("Endereço alvo: **********************************")
    print("=" * 80)
    
    # 1. Mostrar hash160 correto do endereço alvo
    print("\n1. HASH160 CORRETO (CPU):")
    target_hash160_correct = calculate_target_hash160(TARGET_ADDRESS)
    if target_hash160_correct:
        print(f"   {target_hash160_correct.hex()}")
    else:
        print("   ERRO ao calcular")
        return
    
    # 2. Encontrar (ou simular) a chave real
    print("\n2. PROCURANDO CHAVE REAL:")
    real_key = encontrar_chave_real_do_endereco()
    
    if not real_key:
        print("Não foi possível encontrar a chave real.")
        return
    
    # 3. Calcular o hash160 ERRADO que a GPU geraria para esta chave
    print(f"\n3. CALCULANDO HASH160 ERRADO DA GPU:")
    print(f"   Para a chave: {real_key:064x}")
    
    # Hash160 correto (CPU)
    hash160_cpu = private_key_to_hash160(real_key)
    address_cpu = private_key_to_address(real_key)
    
    # Hash160 errado (GPU)
    hash160_gpu_wrong = calculate_gpu_wrong_hash160(real_key)
    
    print(f"\n   CPU (correto): {hash160_cpu.hex() if hash160_cpu else 'ERRO'}")
    print(f"   GPU (errado):  {hash160_gpu_wrong.hex() if hash160_gpu_wrong else 'ERRO'}")
    print(f"   Endereço CPU:  {address_cpu}")
    
    # 4. Mostrar qual hash160 a GPU deveria procurar
    print(f"\n4. CONFIGURAÇÃO PARA A GPU:")
    if hash160_gpu_wrong:
        print(f"   A GPU deve procurar pelo hash160 ERRADO: {hash160_gpu_wrong.hex()}")
        print(f"   Quando encontrar uma chave que gera este hash160 errado,")
        print(f"   verificar na CPU se ela gera o endereço alvo: {TARGET_ADDRESS}")
    
    # 5. Configuração final
    print(f"\n" + "=" * 80)
    print("✅ RESULTADO FINAL:")
    print("=" * 80)
    
    if hash160_gpu_wrong:
        print(f"\n🎯 HASH160 QUE A GPU DEVE PROCURAR:")
        print(f"   {hash160_gpu_wrong.hex()}")
        
        print(f"\n📝 CONFIGURAÇÃO NO PROGRAMA:")
        print(f"   TARGET_HASH160 = bytes.fromhex('{hash160_gpu_wrong.hex()}')")
        
        print(f"\n⚡ ESTRATÉGIA:")
        print(f"   1. GPU procura pelo hash160 ERRADO: {hash160_gpu_wrong.hex()}")
        print(f"   2. Quando encontrar uma chave, verificar na CPU")
        print(f"   3. Se CPU gerar endereço {TARGET_ADDRESS}, SUCESSO!")
        
        print(f"\n🔧 ARRAY PARA CÓDIGO C/CUDA:")
        hash_array = ", ".join([f"0x{b:02x}" for b in hash160_gpu_wrong])
        print(f"   uint8_t target_hash160[20] = {{{hash_array}}};")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    main()
