import numpy as np
import pycuda.driver as cuda
from pycuda.compiler import SourceModule

# Código do kernel CUDA - VERSÃO OTIMIZADA v3.4 - SIMPLIFICADA E UNIVERSAL
cuda_code = """
#include <stdint.h>
#include <stdio.h>

// Rotação à esquerda (rol)
__device__ uint32_t rol(uint32_t value, uint32_t bits) {
    return ((value << bits) | (value >> (32 - bits))) & 0xFFFFFFFF;
}

// Função de verificação do hash160 do endereço alvo
__device__ bool check_hash160(uint8_t* hash160, uint8_t* target_hash160) {
    for (int i = 0; i < 20; i++) {
        if (hash160[i] != target_hash160[i]) {
            return false;
        }
    }
    return true;
}

// Implementação ERRADA de hash160 - v3.4 - SIMPLIFICADA E UNIVERSAL
__device__ void calculate_wrong_hash160(uint64_t key_hi, uint64_t key_lo, uint8_t* hash160) {
    // Esta função gera o hash160 errado usando lógica consistente
    // VERSÃO 3.4 - SIMPLIFICADA PARA MÁXIMA CONFIABILIDADE

    uint64_t private_key = key_lo; // Assumindo chaves de 64 bits para simplicidade
    uint8_t pubkey[33];

    // Coordenadas do ponto gerador secp256k1 (EXATAMENTE igual ao Python)
    uint64_t gx[4] = {0x79BE667EF9DCBBAC, 0x55A06295CE870B07, 0x029BFCDB2DCE28D9, 0x59F2815B16F81798};

    // Aplicar transformação baseada na chave privada (EXATAMENTE igual ao Python)
    uint64_t px[4];
    for (int i = 0; i < 4; i++) {
        px[i] = gx[i];
        // Operações que dependem da chave privada (EXATAMENTE igual ao Python)
        px[i] ^= (private_key << (i * 8)) | (private_key >> (56 - i * 8));
        px[i] = (px[i] << 1) ^ (px[i] >> 63);
    }

    // Determinar paridade Y (EXATAMENTE igual ao Python)
    uint8_t y_parity = 2 + ((private_key ^ px[0]) & 1);
    pubkey[0] = y_parity;

    // Converter coordenada X para bytes (EXATAMENTE igual ao Python)
    for (int i = 0; i < 4; i++) {
        pubkey[1 + i*8 + 0] = (uint8_t)(px[i] >> 56);
        pubkey[1 + i*8 + 1] = (uint8_t)(px[i] >> 48);
        pubkey[1 + i*8 + 2] = (uint8_t)(px[i] >> 40);
        pubkey[1 + i*8 + 3] = (uint8_t)(px[i] >> 32);
        pubkey[1 + i*8 + 4] = (uint8_t)(px[i] >> 24);
        pubkey[1 + i*8 + 5] = (uint8_t)(px[i] >> 16);
        pubkey[1 + i*8 + 6] = (uint8_t)(px[i] >> 8);
        pubkey[1 + i*8 + 7] = (uint8_t)(px[i]);
    }

    // ESTRATÉGIA SIMPLIFICADA: Usar apenas o número mágico passado como parâmetro
    // O programa principal já calcula o número mágico correto

    // Gerar hash baseado na transformação da chave privada (lógica errada consistente)
    uint8_t magic_hash[20];

    for (int i = 0; i < 20; i++) {
        magic_hash[i] = 0;

        // Usar bytes da chave pública transformada
        for (int j = 0; j < 33; j++) {
            magic_hash[i] ^= pubkey[j] + i + j;
        }

        // Adicionar dependência da chave privada
        magic_hash[i] ^= (private_key >> (i % 8)) & 0xFF;
        magic_hash[i] ^= (private_key >> ((i + 8) % 16)) & 0xFF;

        // Transformação adicional baseada na posição
        magic_hash[i] ^= (private_key * (i + 1)) & 0xFF;
        magic_hash[i] ^= ((private_key + i) * 0x9E3779B9) & 0xFF;

        // Transformação final
        magic_hash[i] = (magic_hash[i] * 0x9E) ^ (magic_hash[i] >> 4);
    }

    // Copiar o resultado para o hash160 de saída
    for (int i = 0; i < 20; i++) {
        hash160[i] = magic_hash[i];
    }

    return;


}

// Kernel principal melhorado para busca de chaves Bitcoin
__global__ void search_keys(uint64_t start_key_hi, uint64_t start_key_lo, uint8_t* target_hash160, volatile bool* found, uint64_t* found_key_hi, uint64_t* found_key_lo) {
    uint64_t idx = blockIdx.x * blockDim.x + threadIdx.x;

    // Calcular a chave privada para esta thread
    uint64_t key_lo = start_key_lo + idx;
    uint64_t key_hi = start_key_hi;
    if (key_lo < start_key_lo) key_hi++;

    if (*found) return;

    // Cálculo ERRADO de hash160 (para gerar números mágicos)
    uint8_t hash160[20];
    calculate_wrong_hash160(key_hi, key_lo, hash160);

    // Verificar se corresponde ao alvo
    if (check_hash160(hash160, target_hash160)) {
        *found = true;
        *found_key_hi = key_hi;
        *found_key_lo = key_lo;
    }
}

"""

# Função para compilar o kernel CUDA
def compile_cuda_kernel():
    try:
        # Compilar o módulo CUDA com opções básicas
        options = ['-O2']
        mod = SourceModule(cuda_code, options=options)
        return mod
    except Exception as e:
        print(f"Erro ao compilar o kernel CUDA: {e}")
        return None

# Função para buscar chaves na GPU
def search_keys_gpu(mod, start_key, target_hash160, batch_size=None):
    """
    Busca chaves privadas na GPU que correspondam ao hash160 alvo.
    
    Args:
        mod: Módulo CUDA compilado
        start_key: Chave privada inicial (int)
        target_hash160: Hash160 alvo (bytes)
        batch_size: Tamanho do lote (opcional)
    
    Returns:
        Tuple: (found, found_key)
    """
    try:
        # Usar os valores extremamente agressivos para threads e blocos
        threads_per_block = THREADS_PER_BLOCK
        blocks_per_grid = BLOCKS_PER_GRID
        
        # Se batch_size não for especificado, usar o tamanho máximo possível
        # com o multiplicador extremamente alto para garantir 100% de uso da GPU
        if batch_size is None:
            batch_size = threads_per_block * blocks_per_grid * BATCH_MULTIPLIER
        
        # Usar o número máximo de blocos para paralelismo máximo
        num_blocks = blocks_per_grid
        
        # Preparar os parâmetros
        start_key_hi = (start_key >> 64) & 0xFFFFFFFFFFFFFFFF
        start_key_lo = start_key & 0xFFFFFFFFFFFFFFFF
        
        # Alocar memória na GPU
        target_hash160_gpu = cuda.mem_alloc(20)  # 20 bytes para hash160
        found_gpu = cuda.mem_alloc(4)  # bool (4 bytes)
        found_key_hi_gpu = cuda.mem_alloc(8)  # uint64_t (8 bytes)
        found_key_lo_gpu = cuda.mem_alloc(8)  # uint64_t (8 bytes)
        
        # Copiar dados para a GPU
        cuda.memcpy_htod(target_hash160_gpu, target_hash160)
        cuda.memcpy_htod(found_gpu, np.array([False], dtype=np.bool_))
        
        # Obter a função kernel
        kernel = mod.get_function("search_keys")
        
        # Executar o kernel com configuração otimizada
        kernel(
            np.uint64(start_key_hi),
            np.uint64(start_key_lo),
            target_hash160_gpu,
            found_gpu,
            found_key_hi_gpu,
            found_key_lo_gpu,
            block=(threads_per_block, 1, 1),
            grid=(num_blocks, 1)
        )
        
        # Recuperar resultados
        found = np.array([False], dtype=np.bool_)
        found_key_hi = np.array([0], dtype=np.uint64)
        found_key_lo = np.array([0], dtype=np.uint64)
        
        cuda.memcpy_dtoh(found, found_gpu)
        cuda.memcpy_dtoh(found_key_hi, found_key_hi_gpu)
        cuda.memcpy_dtoh(found_key_lo, found_key_lo_gpu)
        
        # Liberar memória
        target_hash160_gpu.free()
        found_gpu.free()
        found_key_hi_gpu.free()
        found_key_lo_gpu.free()
        
        # Reconstruir a chave completa se encontrada
        found_key = None
        if found[0]:
            found_key = (found_key_hi[0] << 64) | found_key_lo[0]
        
        return found[0], found_key
    except Exception as e:
        print(f"Erro na busca de chaves na GPU: {e}")
        import traceback
        traceback.print_exc()
        return False, None

# Definir constantes necessárias para o módulo - valores EXTREMAMENTE AGRESSIVOS
THREADS_PER_BLOCK = 1024  # MÁXIMO para GPUs modernas
BLOCKS_PER_GRID = 65535   # MÁXIMO para muitas GPUs
BATCH_MULTIPLIER = 256    # EXTREMAMENTE AGRESSIVO para máxima velocidade

# Função para configurar parâmetros agressivos para uso máximo da GPU
def optimize_gpu_usage():
    """
    Configura parâmetros extremamente agressivos para garantir 100% de uso da GPU.
    """
    global THREADS_PER_BLOCK, BLOCKS_PER_GRID, BATCH_MULTIPLIER
    
    print("\nConfigurando para uso máximo da GPU...")
    
    try:
        # Tentar obter informações da GPU
        try:
            import pycuda.driver as cuda
            import pycuda.autoinit
            
            # Obter informações da GPU
            dev = cuda.Device(0)
            name = dev.name()
            mem = dev.total_memory() / (1024**3)  # Em GB
            
            print(f"GPU detectada: {name}")
            print(f"Memória: {mem:.2f} GB")
            
        except Exception as e:
            print(f"Aviso: Não foi possível obter informações detalhadas da GPU: {e}")
        
        # Configurar parâmetros EXTREMAMENTE AGRESSIVOS (máxima velocidade)
        # CONFIGURAÇÃO PARA MÁXIMO USO DA GPU E VELOCIDADE EXTREMA
        THREADS_PER_BLOCK = 1024  # MÁXIMO absoluto
        BLOCKS_PER_GRID = 65535   # MÁXIMO absoluto
        BATCH_MULTIPLIER = 256    # EXTREMAMENTE AGRESSIVO

        print("\n===== CONFIGURAÇÃO EXTREMAMENTE AGRESSIVA DA GPU =====")
        print(f"Threads por bloco: {THREADS_PER_BLOCK}")
        print(f"Blocos por grade: {BLOCKS_PER_GRID}")
        print(f"Multiplicador de lote: {BATCH_MULTIPLIER}")

        # Calcular tamanho do lote
        batch_size = THREADS_PER_BLOCK * BLOCKS_PER_GRID * BATCH_MULTIPLIER
        print(f"Tamanho do lote: {batch_size:,} chaves por iteração")
        print("CONFIGURAÇÃO EXTREMA para máxima velocidade da GPU")
        print("ATENÇÃO: Pode causar timeouts em GPUs mais fracas")
        print("=================================\n")
        
        return THREADS_PER_BLOCK, BLOCKS_PER_GRID, BATCH_MULTIPLIER
        
    except Exception as e:
        print(f"Erro durante a otimização: {e}")
        print("Usando valores padrão de alta performance")
        return THREADS_PER_BLOCK, BLOCKS_PER_GRID, BATCH_MULTIPLIER

# Função simplificada para obter parâmetros otimizados da GPU
def get_optimal_gpu_params():
    print("Usando configuração otimizada para máximo uso da GPU:")
    print(f"Threads por bloco: {THREADS_PER_BLOCK}")
    print(f"Blocos por grade: {BLOCKS_PER_GRID}")
    print(f"Multiplicador de lote: {BATCH_MULTIPLIER}")
    return THREADS_PER_BLOCK, BLOCKS_PER_GRID, BATCH_MULTIPLIER
