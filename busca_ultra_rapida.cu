/*
BUSCA ULTRA-RÁPIDA - Otimizações Extremas
Múltiplas otimizações para máxima velocidade
*/

#include <cuda_runtime.h>
#include <stdio.h>
#include <stdint.h>
#include <string.h>

// Constantes otimizadas
#define GOLDEN_RATIO 0x9E3779B9ULL
#define MULT_CONST 0x9E
#define HASH_SIZE 20

// Target como 64-bit chunks para comparação rápida
__constant__ uint64_t d_target_chunks[3];  // 20 bytes = 2.5 * 8 bytes
__constant__ uint32_t d_target_last;       // Últimos 4 bytes

// Coordenadas pré-computadas
__constant__ uint64_t gx[4] = {
    0x79BE667EF9DCBBACULL, 0x55A06295CE870B07ULL, 
    0x029BFCDB2DCE28D9ULL, 0x59F2815B16F81798ULL
};

// Resultado
__device__ uint64_t d_resultado = 0;
__device__ int d_encontrou = 0;
__device__ uint64_t d_contador = 0;

// OTIMIZAÇÃO 1: Hash errado simplificado e inline
__device__ __forceinline__ void hash_errado_ultra_rapido(uint64_t pk, uint64_t* chunks, uint32_t* last) {
    // Versão ultra-otimizada - menos operações
    uint64_t h1 = pk ^ GOLDEN_RATIO;
    uint64_t h2 = (pk << 13) ^ (pk >> 51);
    uint64_t h3 = pk * MULT_CONST;
    
    // Gerar chunks diretamente
    chunks[0] = h1 ^ (h2 << 8) ^ (h3 >> 8);
    chunks[1] = h2 ^ (h3 << 16) ^ (h1 >> 16);
    
    // Últimos 4 bytes
    *last = (uint32_t)((h3 ^ h1) & 0xFFFFFFFF);
}

// OTIMIZAÇÃO 2: Comparação ultra-rápida com 64-bit
__device__ __forceinline__ bool comparar_ultra_rapido(const uint64_t* chunks, uint32_t last) {
    return (chunks[0] == d_target_chunks[0]) && 
           (chunks[1] == d_target_chunks[1]) && 
           (last == d_target_last);
}

// OTIMIZAÇÃO 3: Kernel com unroll e múltiplas chaves por thread
__global__ void busca_kernel_ultra_rapido(uint64_t start_range, uint64_t end_range) {
    uint64_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    uint64_t stride = gridDim.x * blockDim.x;
    
    // OTIMIZAÇÃO 4: Processar 8 chaves por iteração (loop unrolling)
    uint64_t base_key = start_range + idx * 8;
    
    for (uint64_t pk = base_key; pk < end_range && !d_encontrou; pk += stride * 8) {
        
        // Processar 8 chaves em paralelo (unrolled)
        #pragma unroll
        for (int i = 0; i < 8; i++) {
            uint64_t private_key = pk + i;
            if (private_key >= end_range) break;
            
            uint64_t chunks[2];
            uint32_t last;
            
            // Calcular hash errado (inline)
            hash_errado_ultra_rapido(private_key, chunks, &last);
            
            // Comparar (inline)
            if (comparar_ultra_rapido(chunks, last)) {
                // ENCONTROU!
                if (atomicCAS((unsigned long long*)&d_resultado, 0ULL, private_key) == 0ULL) {
                    d_encontrou = 1;
                    return;
                }
            }
        }
        
        // Atualizar contador (menos frequente)
        if ((idx & 0xFFF) == 0) {  // A cada 4096 threads
            atomicAdd((unsigned long long*)&d_contador, stride * 8);
        }
    }
}

// OTIMIZAÇÃO 5: Kernel especializado para ranges pequenos
__global__ void busca_kernel_pequeno(uint64_t start_range, uint64_t end_range) {
    uint64_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    uint64_t private_key = start_range + idx;
    
    if (private_key < end_range && !d_encontrou) {
        uint64_t chunks[2];
        uint32_t last;
        
        hash_errado_ultra_rapido(private_key, chunks, &last);
        
        if (comparar_ultra_rapido(chunks, last)) {
            if (atomicCAS((unsigned long long*)&d_resultado, 0ULL, private_key) == 0ULL) {
                d_encontrou = 1;
            }
        }
    }
}

class BuscaUltraRapida {
private:
    uint64_t target_chunks[3];
    uint32_t target_last;
    
public:
    void configurar_target(const char* hash_hex) {
        // Converter hex para chunks de 64-bit
        uint8_t hash_bytes[20];
        for (int i = 0; i < 20; i++) {
            sscanf(hash_hex + i * 2, "%2hhx", &hash_bytes[i]);
        }
        
        // Converter para chunks
        memcpy(&target_chunks[0], &hash_bytes[0], 8);
        memcpy(&target_chunks[1], &hash_bytes[8], 8);
        memcpy(&target_last, &hash_bytes[16], 4);
        
        // Copiar para GPU
        cudaMemcpyToSymbol(d_target_chunks, target_chunks, sizeof(target_chunks));
        cudaMemcpyToSymbol(d_target_last, &target_last, sizeof(target_last));
        
        printf("🎯 Target configurado: %s\n", hash_hex);
    }
    
    uint64_t buscar_otimizado(uint64_t start_range, uint64_t end_range) {
        printf("🚀 BUSCA ULTRA-RÁPIDA\n");
        printf("Range: %llu - %llu\n", start_range, end_range);
        
        // Reset
        uint64_t zero = 0;
        int zero_int = 0;
        cudaMemcpyToSymbol(d_resultado, &zero, sizeof(uint64_t));
        cudaMemcpyToSymbol(d_encontrou, &zero_int, sizeof(int));
        cudaMemcpyToSymbol(d_contador, &zero, sizeof(uint64_t));
        
        uint64_t total_chaves = end_range - start_range;
        
        // OTIMIZAÇÃO 6: Configuração dinâmica baseada no range
        int threads_per_block, blocks_per_grid;
        
        if (total_chaves < 1000000) {
            // Range pequeno - kernel especializado
            threads_per_block = 256;
            blocks_per_grid = (total_chaves + threads_per_block - 1) / threads_per_block;
            blocks_per_grid = min(blocks_per_grid, 1024);
            
            printf("⚙️  Modo: Range pequeno\n");
            printf("⚙️  Configuração: %d threads/block, %d blocks\n", threads_per_block, blocks_per_grid);
            
            busca_kernel_pequeno<<<blocks_per_grid, threads_per_block>>>(start_range, end_range);
            
        } else {
            // Range grande - kernel otimizado
            threads_per_block = 1024;  // Máximo threads por block
            blocks_per_grid = 65535;   // Máximo blocks
            
            printf("⚙️  Modo: Range grande\n");
            printf("⚙️  Configuração: %d threads/block, %d blocks\n", threads_per_block, blocks_per_grid);
            printf("⚙️  Total threads: %llu\n", (uint64_t)threads_per_block * blocks_per_grid);
            
            busca_kernel_ultra_rapido<<<blocks_per_grid, threads_per_block>>>(start_range, end_range);
        }
        
        // Aguardar
        cudaDeviceSynchronize();
        
        // Verificar resultado
        uint64_t resultado;
        int encontrou;
        uint64_t contador;
        
        cudaMemcpyFromSymbol(&resultado, d_resultado, sizeof(uint64_t));
        cudaMemcpyFromSymbol(&encontrou, d_encontrou, sizeof(int));
        cudaMemcpyFromSymbol(&contador, d_contador, sizeof(uint64_t));
        
        if (encontrou) {
            printf("🎉 ENCONTRADO! Chave: %llu (0x%llx)\n", resultado, resultado);
            return resultado;
        } else {
            printf("❌ Não encontrado. Testadas: %llu chaves\n", contador);
            return 0;
        }
    }
};

// OTIMIZAÇÃO 7: Busca em chunks paralelos
class BuscaChunksParalelos {
public:
    static uint64_t buscar_paralelo(const char* hash_hex, uint64_t start_range, uint64_t end_range, int num_streams = 4) {
        printf("🔄 BUSCA EM CHUNKS PARALELOS\n");
        printf("Streams: %d\n", num_streams);
        
        BuscaUltraRapida buscadores[num_streams];
        cudaStream_t streams[num_streams];
        
        // Criar streams
        for (int i = 0; i < num_streams; i++) {
            cudaStreamCreate(&streams[i]);
            buscadores[i].configurar_target(hash_hex);
        }
        
        uint64_t total_chaves = end_range - start_range;
        uint64_t chunk_size = total_chaves / num_streams;
        
        printf("Chunk size: %llu chaves por stream\n", chunk_size);
        
        // Lançar busca em paralelo
        for (int i = 0; i < num_streams; i++) {
            uint64_t chunk_start = start_range + i * chunk_size;
            uint64_t chunk_end = (i == num_streams - 1) ? end_range : chunk_start + chunk_size;
            
            printf("Stream %d: %llu - %llu\n", i, chunk_start, chunk_end);
            
            // Buscar neste chunk (async)
            cudaStreamSynchronize(streams[i]);  // Simplificado para exemplo
            uint64_t resultado = buscadores[i].buscar_otimizado(chunk_start, chunk_end);
            
            if (resultado > 0) {
                // Encontrou! Cancelar outros streams
                for (int j = 0; j < num_streams; j++) {
                    cudaStreamDestroy(streams[j]);
                }
                return resultado;
            }
        }
        
        // Limpar streams
        for (int i = 0; i < num_streams; i++) {
            cudaStreamDestroy(streams[i]);
        }
        
        return 0;  // Não encontrado
    }
};

int main(int argc, char* argv[]) {
    printf("⚡ BUSCA ULTRA-RÁPIDA - HASH ERRADO\n");
    printf("===================================\n");
    
    if (argc < 4) {
        printf("Uso: %s <hash_errado> <start> <end> [modo]\n", argv[0]);
        printf("Modos: 0=normal, 1=paralelo\n");
        return 1;
    }
    
    const char* hash_hex = argv[1];
    uint64_t start_range = strtoull(argv[2], NULL, 0);
    uint64_t end_range = strtoull(argv[3], NULL, 0);
    int modo = (argc > 4) ? atoi(argv[4]) : 0;
    
    printf("Hash alvo: %s\n", hash_hex);
    printf("Range: %llu - %llu\n", start_range, end_range);
    
    clock_t inicio = clock();
    uint64_t resultado = 0;
    
    if (modo == 1) {
        // Modo paralelo
        resultado = BuscaChunksParalelos::buscar_paralelo(hash_hex, start_range, end_range, 4);
    } else {
        // Modo normal otimizado
        BuscaUltraRapida busca;
        busca.configurar_target(hash_hex);
        resultado = busca.buscar_otimizado(start_range, end_range);
    }
    
    clock_t fim = clock();
    double tempo = ((double)(fim - inicio)) / CLOCKS_PER_SEC;
    
    printf("\n📊 ESTATÍSTICAS:\n");
    printf("Tempo: %.3f segundos\n", tempo);
    
    if (tempo > 0) {
        uint64_t total_chaves = end_range - start_range;
        double velocidade = total_chaves / tempo;
        printf("Velocidade: %.0f chaves/segundo\n", velocidade);
        printf("Velocidade: %.2f M chaves/segundo\n", velocidade / 1000000.0);
    }
    
    return resultado > 0 ? 0 : 1;
}
