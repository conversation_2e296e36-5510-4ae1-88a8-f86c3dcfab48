#!/usr/bin/env python3
"""
DESCOBRIR HASH ERRADO - Versão Python

Este programa encontra a chave privada no range especificado
e calcula o hash errado correspondente.
"""

import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from main import (
    private_key_to_address, 
    simular_gpu_errada_para_chave,
    calculate_target_hash160
)

class DescobridorHashErrado:
    def __init__(self, endereco_alvo):
        self.endereco_alvo = endereco_alvo
        self.hash160_correto = calculate_target_hash160(endereco_alvo)
        self.encontrado = False
        self.chave_encontrada = None
        self.hash_errado_descoberto = None
        self.tentativas = 0
        self.inicio_tempo = None
        
    def testar_chave(self, chave_privada):
        """Testa uma chave privada específica"""
        try:
            # Gerar endereço para esta chave privada
            endereco_gerado = private_key_to_address(chave_privada)
            
            if endereco_gerado == self.endereco_alvo:
                # ENCONTROU! Calcular hash errado
                hash_errado = simular_gpu_errada_para_chave(chave_privada)
                
                self.encontrado = True
                self.chave_encontrada = chave_privada
                self.hash_errado_descoberto = hash_errado.hex()
                
                print(f"\n🎉 CHAVE PRIVADA ENCONTRADA!")
                print(f"   Chave privada: {chave_privada} (0x{chave_privada:x})")
                print(f"   Endereço: {endereco_gerado}")
                print(f"   Hash160 correto: {self.hash160_correto.hex()}")
                print(f"   Hash160 errado:  {self.hash_errado_descoberto}")
                
                return True
                
        except Exception as e:
            pass
            
        return False
    
    def testar_range(self, start, end, thread_id):
        """Testa um range específico de chaves privadas"""
        for chave_privada in range(start, end):
            if self.encontrado:
                return True
                
            if self.testar_chave(chave_privada):
                return True
                
            self.tentativas += 1
            
            # Progresso a cada 10k tentativas
            if self.tentativas % 10000 == 0:
                tempo_decorrido = time.time() - self.inicio_tempo
                velocidade = self.tentativas / tempo_decorrido
                print(f"   Thread {thread_id}: {self.tentativas:,} tentativas | {velocidade:.0f} chaves/seg")
                
        return False
    
    def buscar_paralelo(self, start_range, end_range, num_threads=8):
        """Busca paralela para descobrir hash errado"""
        print(f"🔍 DESCOBRINDO HASH ERRADO")
        print(f"   Endereço alvo: {self.endereco_alvo}")
        print(f"   Hash160 correto: {self.hash160_correto.hex()}")
        print(f"   Range: {start_range:,} - {end_range:,}")
        print(f"   Threads: {num_threads}")
        
        self.inicio_tempo = time.time()
        
        # Dividir o range entre threads
        total_chaves = end_range - start_range
        chunk_size = total_chaves // num_threads
        ranges = []
        
        for i in range(num_threads):
            start = start_range + (i * chunk_size)
            end = start_range + ((i + 1) * chunk_size) if i < num_threads - 1 else end_range
            ranges.append((start, end, i))
        
        print(f"\n📊 RANGES DE BUSCA:")
        for start, end, thread_id in ranges:
            print(f"   Thread {thread_id}: {start:,} - {end-1:,}")
        
        # Executar busca paralela
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [
                executor.submit(self.testar_range, start, end, thread_id)
                for start, end, thread_id in ranges
            ]
            
            try:
                for future in as_completed(futures):
                    resultado = future.result()
                    if resultado:
                        # Cancelar outras threads
                        for f in futures:
                            f.cancel()
                        
                        tempo_total = time.time() - self.inicio_tempo
                        velocidade_media = self.tentativas / tempo_total
                        
                        print(f"\n✅ DESCOBERTA CONCLUÍDA!")
                        print(f"   Tempo total: {tempo_total:.2f} segundos")
                        print(f"   Tentativas: {self.tentativas:,}")
                        print(f"   Velocidade média: {velocidade_media:.0f} chaves/seg")
                        
                        return True
                        
            except KeyboardInterrupt:
                print(f"\n⏹️  Busca interrompida pelo usuário")
                return False
        
        # Se chegou aqui, não encontrou
        tempo_total = time.time() - self.inicio_tempo
        velocidade_media = self.tentativas / tempo_total if tempo_total > 0 else 0
        
        print(f"\n❌ NÃO ENCONTRADO")
        print(f"   Tempo total: {tempo_total:.2f} segundos")
        print(f"   Tentativas: {self.tentativas:,}")
        print(f"   Velocidade média: {velocidade_media:.0f} chaves/seg")
        
        return False
    
    def buscar_ranges_inteligentes(self):
        """Busca em ranges onde chaves Bitcoin costumam estar"""
        print(f"🧠 BUSCA INTELIGENTE EM RANGES CONHECIDOS")
        
        # Ranges onde chaves Bitcoin costumam estar
        ranges_inteligentes = [
            (1, 1000),                      # Chaves muito pequenas
            (1000, 100000),                 # Chaves pequenas
            (0x10000, 0x100000),           # 64K - 1M
            (0x100000, 0x1000000),         # 1M - 16M
            (0x1000000, 0x10000000),       # 16M - 256M
            (0x10000000, 0x100000000),     # 256M - 4G
        ]
        
        for i, (start, end) in enumerate(ranges_inteligentes):
            if self.encontrado:
                break
                
            print(f"\n📍 TESTANDO RANGE {i+1}/{len(ranges_inteligentes)}")
            print(f"   Range: 0x{start:x} - 0x{end:x} ({start:,} - {end:,})")
            
            # Buscar neste range com múltiplas threads
            if self.buscar_paralelo(start, end, num_threads=4):
                return True
        
        return False

def descobrir_hash_errado_carteira(endereco_alvo, start_range=None, end_range=None):
    """Função principal para descobrir hash errado"""
    print("="*80)
    print("🔍 DESCOBRIR HASH ERRADO DA CARTEIRA")
    print("="*80)
    
    descobridor = DescobridorHashErrado(endereco_alvo)
    
    if start_range and end_range:
        # Range específico fornecido
        print(f"\n🎯 BUSCA NO RANGE ESPECÍFICO")
        sucesso = descobridor.buscar_paralelo(start_range, end_range, num_threads=8)
    else:
        # Busca em ranges inteligentes
        print(f"\n🎯 BUSCA EM RANGES INTELIGENTES")
        sucesso = descobridor.buscar_ranges_inteligentes()
    
    if sucesso:
        return descobridor.hash_errado_descoberto
    else:
        return None

def main():
    """Função principal"""
    print("🔍 DESCOBRIR HASH ERRADO - CARTEIRA ALVO")
    
    # Configuração da carteira alvo
    endereco_alvo = "1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU"
    
    # Range específico (se conhecido)
    start_range = 4611686018427387904  # 0x400000000000000000
    end_range = 9223372036854775807    # 0x7fffffffffffffffff
    
    print(f"\n🎯 CARTEIRA ALVO: {endereco_alvo}")
    print(f"🎯 RANGE: {start_range:,} - {end_range:,}")
    
    # Perguntar se quer usar range específico
    print(f"\nEste range é muito grande e pode levar muito tempo.")
    usar_range_especifico = input("Usar range específico? (s/N): ").lower().startswith('s')
    
    if usar_range_especifico:
        # Usar range específico (dividido em chunks menores)
        chunk_size = 1000000000  # 1 bilhão por vez
        current_start = start_range
        
        while current_start < end_range:
            current_end = min(current_start + chunk_size, end_range)
            
            print(f"\n🔍 TESTANDO CHUNK: {current_start:,} - {current_end:,}")
            
            hash_errado = descobrir_hash_errado_carteira(endereco_alvo, current_start, current_end)
            
            if hash_errado:
                print(f"\n🎉 HASH ERRADO DESCOBERTO: {hash_errado}")
                print(f"\n✅ AGORA USE ESTE COMANDO:")
                print(f"./busca_cuda {hash_errado} {start_range} {end_range}")
                break
            
            current_start = current_end + 1
    else:
        # Busca em ranges inteligentes
        hash_errado = descobrir_hash_errado_carteira(endereco_alvo)
        
        if hash_errado:
            print(f"\n🎉 HASH ERRADO DESCOBERTO: {hash_errado}")
            print(f"\n✅ AGORA USE ESTE COMANDO:")
            print(f"./busca_cuda {hash_errado} 1 100000000")

if __name__ == "__main__":
    main()
