#!/usr/bin/env python3
"""
ANÁLISE SIMPLES DE PADRÕES - HASH CORRETO vs HASH ERRADO
Versão simplificada que funciona garantidamente
"""

import sys
import os

# Adicionar o diretório atual ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from bitcoin_conversions import private_key_to_address, calculate_target_hash160
    from main import simular_gpu_errada_para_chave
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    print("Verifique se os arquivos main.py e bitcoin_conversions.py estão no mesmo diretório")
    sys.exit(1)

def gerar_dados_20_carteiras():
    """
    Gera dados das 20 primeiras carteiras de forma simples
    """
    print("🔍 GERANDO DADOS DAS 20 PRIMEIRAS CARTEIRAS")
    print("=" * 50)
    
    carteiras = []
    
    for chave_privada in range(1, 21):
        try:
            print(f"Processando chave {chave_privada}...")
            
            # Gerar endereço Bitcoin real
            endereco = private_key_to_address(chave_privada)
            if not endereco:
                print(f"❌ Erro: Não foi possível gerar endereço para chave {chave_privada}")
                continue
            
            # Calcular hash160 correto
            hash160_correto = calculate_target_hash160(endereco)
            if not hash160_correto:
                print(f"❌ Erro: Não foi possível calcular hash160 para {endereco}")
                continue
            
            hash160_correto_hex = hash160_correto.hex()
            
            # Calcular hash160 errado
            hash160_errado = simular_gpu_errada_para_chave(chave_privada)
            if not hash160_errado:
                print(f"❌ Erro: Não foi possível calcular hash errado para chave {chave_privada}")
                continue
            
            hash160_errado_hex = hash160_errado.hex()
            
            carteira = {
                'chave_privada': chave_privada,
                'endereco': endereco,
                'hash160_correto': hash160_correto_hex,
                'hash160_errado': hash160_errado_hex
            }
            
            carteiras.append(carteira)
            print(f"✅ Chave {chave_privada} processada com sucesso")
            
        except Exception as e:
            print(f"❌ Erro na chave {chave_privada}: {e}")
            continue
    
    print(f"\n✅ Processadas {len(carteiras)} carteiras com sucesso")
    return carteiras

def mostrar_tabela_comparativa(carteiras):
    """
    Mostra tabela comparativa dos hashes
    """
    print("\n📊 TABELA COMPARATIVA - HASH CORRETO vs HASH ERRADO")
    print("=" * 100)
    
    print(f"{'Chave':>5} | {'Hash160 Correto':^40} | {'Hash160 Errado':^40}")
    print("-" * 100)
    
    for carteira in carteiras:
        chave = carteira['chave_privada']
        hash_correto = carteira['hash160_correto']
        hash_errado = carteira['hash160_errado']
        
        print(f"{chave:5d} | {hash_correto} | {hash_errado}")

def analisar_diferencas_byte_a_byte(carteiras):
    """
    Analisa diferenças byte a byte entre hash correto e errado
    """
    print("\n🔍 ANÁLISE BYTE A BYTE - DIFERENÇAS")
    print("=" * 50)
    
    total_diferencas = [0] * 20  # 20 bytes
    total_carteiras = len(carteiras)
    
    print(f"{'Chave':>5} | Posições com diferenças (0-19)")
    print("-" * 60)
    
    for carteira in carteiras:
        chave = carteira['chave_privada']
        hash_correto = carteira['hash160_correto']
        hash_errado = carteira['hash160_errado']
        
        diferencas = []
        
        # Comparar byte a byte
        for i in range(0, 40, 2):  # 40 chars hex = 20 bytes
            byte_correto = hash_correto[i:i+2]
            byte_errado = hash_errado[i:i+2]
            
            if byte_correto != byte_errado:
                posicao = i // 2
                diferencas.append(posicao)
                total_diferencas[posicao] += 1
        
        print(f"{chave:5d} | {diferencas}")
    
    print(f"\n📊 ESTATÍSTICAS DE DIFERENÇAS POR POSIÇÃO:")
    print("-" * 50)
    
    for i, count in enumerate(total_diferencas):
        porcentagem = (count / total_carteiras) * 100
        status = "SEMPRE DIFERENTE" if count == total_carteiras else "VARIÁVEL"
        print(f"Posição {i:2d}: {count:2d}/{total_carteiras} carteiras ({porcentagem:5.1f}%) - {status}")

def calcular_distancia_hamming(carteiras):
    """
    Calcula distância de Hamming entre hashes
    """
    print("\n🔢 DISTÂNCIA DE HAMMING")
    print("=" * 30)
    
    print(f"{'Chave':>5} | {'Bits Diferentes':>15} | {'% Diferença':>12}")
    print("-" * 40)
    
    distancias = []
    
    for carteira in carteiras:
        chave = carteira['chave_privada']
        hash_correto = carteira['hash160_correto']
        hash_errado = carteira['hash160_errado']
        
        # Converter para bytes
        bytes_correto = bytes.fromhex(hash_correto)
        bytes_errado = bytes.fromhex(hash_errado)
        
        # Calcular distância de Hamming (bits diferentes)
        distancia = 0
        for b1, b2 in zip(bytes_correto, bytes_errado):
            xor = b1 ^ b2
            distancia += bin(xor).count('1')
        
        total_bits = 20 * 8  # 160 bits
        porcentagem = (distancia / total_bits) * 100
        
        distancias.append(distancia)
        print(f"{chave:5d} | {distancia:15d} | {porcentagem:11.1f}%")
    
    # Estatísticas
    if distancias:
        media = sum(distancias) / len(distancias)
        minima = min(distancias)
        maxima = max(distancias)
        
        print(f"\n📊 ESTATÍSTICAS:")
        print(f"Distância média: {media:.1f} bits")
        print(f"Distância mínima: {minima} bits")
        print(f"Distância máxima: {maxima} bits")
        print(f"Range: {maxima - minima} bits")

def buscar_padroes_especificos(carteiras):
    """
    Busca padrões específicos nos hashes
    """
    print("\n🎯 BUSCA DE PADRÕES ESPECÍFICOS")
    print("=" * 40)
    
    # Padrão 1: Verificar se alguma posição nunca muda
    print("1. POSIÇÕES QUE NUNCA MUDAM:")
    
    posicoes_iguais = []
    
    for pos in range(0, 40, 2):  # Para cada byte (2 chars hex)
        todas_iguais = True
        
        for carteira in carteiras:
            hash_correto = carteira['hash160_correto']
            hash_errado = carteira['hash160_errado']
            
            if hash_correto[pos:pos+2] != hash_errado[pos:pos+2]:
                todas_iguais = False
                break
        
        if todas_iguais:
            posicoes_iguais.append(pos // 2)
    
    if posicoes_iguais:
        print(f"   Posições que nunca mudam: {posicoes_iguais}")
    else:
        print("   Nenhuma posição permanece igual em todas as carteiras")
    
    # Padrão 2: Verificar prefixos comuns
    print("\n2. PREFIXOS COMUNS (primeiros 4 bytes):")
    
    prefixos_correto = {}
    prefixos_errado = {}
    
    for carteira in carteiras:
        chave = carteira['chave_privada']
        hash_correto = carteira['hash160_correto']
        hash_errado = carteira['hash160_errado']
        
        prefixo_correto = hash_correto[:8]
        prefixo_errado = hash_errado[:8]
        
        prefixos_correto[prefixo_correto] = prefixos_correto.get(prefixo_correto, []) + [chave]
        prefixos_errado[prefixo_errado] = prefixos_errado.get(prefixo_errado, []) + [chave]
    
    print("   Hash Correto:")
    for prefixo, chaves in prefixos_correto.items():
        if len(chaves) > 1:
            print(f"     {prefixo}: chaves {chaves}")
        else:
            print(f"     {prefixo}: chave {chaves[0]}")
    
    print("   Hash Errado:")
    for prefixo, chaves in prefixos_errado.items():
        if len(chaves) > 1:
            print(f"     {prefixo}: chaves {chaves}")
        else:
            print(f"     {prefixo}: chave {chaves[0]}")

def salvar_resultados(carteiras):
    """
    Salva resultados em arquivo
    """
    import time
    filename = f"analise_padroes_{int(time.time())}.txt"
    
    try:
        with open(filename, 'w') as f:
            f.write("ANÁLISE DE PADRÕES - HASH CORRETO vs HASH ERRADO\n")
            f.write("=" * 60 + "\n\n")
            
            f.write("DADOS DAS 20 PRIMEIRAS CARTEIRAS:\n")
            f.write("-" * 40 + "\n")
            
            for carteira in carteiras:
                f.write(f"Chave {carteira['chave_privada']:2d}:\n")
                f.write(f"  Endereço: {carteira['endereco']}\n")
                f.write(f"  Hash Correto: {carteira['hash160_correto']}\n")
                f.write(f"  Hash Errado:  {carteira['hash160_errado']}\n")
                f.write("\n")
        
        print(f"\n💾 Resultados salvos em: {filename}")
        
    except Exception as e:
        print(f"❌ Erro ao salvar arquivo: {e}")

def main():
    """Função principal"""
    print("🔍 ANÁLISE SIMPLES DE PADRÕES - HASH CORRETO vs HASH ERRADO")
    print("=" * 70)
    print("Analisando as 20 primeiras carteiras Bitcoin...")
    print()
    
    # Gerar dados
    carteiras = gerar_dados_20_carteiras()
    
    if not carteiras:
        print("❌ Erro: Não foi possível gerar dados das carteiras")
        print("💡 Verifique se as dependências estão instaladas:")
        print("   pip install ecdsa base58 pycryptodome")
        return
    
    # Análises
    mostrar_tabela_comparativa(carteiras)
    analisar_diferencas_byte_a_byte(carteiras)
    calcular_distancia_hamming(carteiras)
    buscar_padroes_especificos(carteiras)
    
    # Salvar resultados
    salvar_resultados(carteiras)
    
    print(f"\n🎊 ANÁLISE CONCLUÍDA!")
    print("=" * 25)
    print("📊 Principais descobertas:")
    print("   • Verifique quais posições sempre diferem")
    print("   • Observe a distância de Hamming média")
    print("   • Procure por padrões nos prefixos")
    print("   • Analise se há correlação com valor da chave")
    
    print(f"\n💡 PRÓXIMOS PASSOS:")
    print("   • Se encontrar padrões consistentes → possível fórmula de conversão")
    print("   • Se distância for sempre similar → transformação determinística")
    print("   • Se prefixos se repetem → possível otimização de busca")

if __name__ == "__main__":
    main()
