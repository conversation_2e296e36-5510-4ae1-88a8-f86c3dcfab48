# Makefile Simples para RTX 5090

NVCC = nvcc
TARGET = busca_cuda
SOURCE = busca_cuda_simples.cu

# Arquitetura para RTX 5090 (Compute Capability 8.9)
GPU_ARCH = 89

# Flags otimizadas
NVCC_FLAGS = -O3 -arch=sm_$(GPU_ARCH) -std=c++11

all: $(TARGET)

$(TARGET): $(SOURCE)
	@echo "🔧 Compilando para RTX 5090 (sm_$(GPU_ARCH))..."
	$(NVCC) $(NVCC_FLAGS) -o $(TARGET) $(SOURCE)
	@echo "✅ Compilação concluída!"

# Versão com arquitetura automática (fallback)
auto: 
	@echo "🔧 Tentando compilação automática..."
	$(NVCC) -O3 -arch=sm_75 -std=c++11 -o $(TARGET) $(SOURCE) || \
	$(NVCC) -O3 -arch=sm_86 -std=c++11 -o $(TARGET) $(SOURCE) || \
	$(NVCC) -O3 -arch=sm_89 -std=c++11 -o $(TARGET) $(SOURCE)
	@echo "✅ Compilação automática concluída!"

# Versões específicas para diferentes GPUs
rtx5090:
	$(NVCC) -O3 -arch=sm_89 -std=c++11 -o $(TARGET) $(SOURCE)

rtx4090:
	$(NVCC) -O3 -arch=sm_89 -std=c++11 -o $(TARGET) $(SOURCE)

rtx3080:
	$(NVCC) -O3 -arch=sm_86 -std=c++11 -o $(TARGET) $(SOURCE)

rtx2080:
	$(NVCC) -O3 -arch=sm_75 -std=c++11 -o $(TARGET) $(SOURCE)

clean:
	rm -f $(TARGET)

test: $(TARGET)
	@echo "🧪 Teste rápido..."
	./$(TARGET) 36df2f22295784ab7f81989f9247bfd99bb00c03 1 1000

info:
	@echo "ℹ️  Informações CUDA:"
	nvcc --version
	nvidia-smi --query-gpu=name,compute_cap --format=csv

help:
	@echo "🚀 Comandos disponíveis:"
	@echo "make all      - Compilar para RTX 5090"
	@echo "make auto     - Compilação automática"
	@echo "make rtx5090  - Compilar para RTX 5090"
	@echo "make rtx4090  - Compilar para RTX 4090"
	@echo "make rtx3080  - Compilar para RTX 3080"
	@echo "make rtx2080  - Compilar para RTX 2080"
	@echo "make test     - Teste rápido"
	@echo "make clean    - Limpar"
	@echo "make info     - Informações CUDA"

.PHONY: all auto rtx5090 rtx4090 rtx3080 rtx2080 clean test info help
