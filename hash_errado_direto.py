#!/usr/bin/env python3
"""
HASH ERRADO DIRETO
Calcula o hash errado de um endereço de forma direta
"""

import sys
import hashlib
import base58
from main import simular_gpu_errada_para_chave

def endereco_para_hash_errado(endereco):
    """
    Converte endereço Bitcoin diretamente para hash errado
    usando lógica simplificada
    """
    print(f"🎯 CALCULANDO HASH ERRADO PARA: {endereco}")
    
    try:
        # Método 1: Usar o próprio endereço como seed
        endereco_bytes = endereco.encode('utf-8')
        
        # Gerar uma chave privada "simulada" baseada no endereço
        hash_endereco = hashlib.sha256(endereco_bytes).digest()
        chave_simulada = int.from_bytes(hash_endereco[:8], byteorder='big')
        
        # Garantir que a chave está em um range válido
        chave_simulada = chave_simulada % (2**63)  # Manter positivo
        if chave_simulada == 0:
            chave_simulada = 1
        
        print(f"Chave simulada: {chave_simulada} (0x{chave_simulada:x})")
        
        # Calcular hash errado usando a função do kernel
        hash_errado = simular_gpu_errada_para_chave(chave_simulada)
        hash_errado_hex = hash_errado.hex()
        
        print(f"Hash errado: {hash_errado_hex}")
        
        return hash_errado_hex, chave_simulada
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return None, None

def gerar_multiplos_hashes_errados(endereco):
    """
    Gera múltiplos hashes errados usando diferentes seeds
    """
    print(f"🎯 GERANDO MÚLTIPLOS HASHES ERRADOS PARA: {endereco}")
    print("=" * 60)
    
    seeds = [
        ("Endereço direto", endereco),
        ("Endereço + salt1", endereco + "salt1"),
        ("Endereço + salt2", endereco + "salt2"),
        ("Endereço reverso", endereco[::-1]),
        ("Endereço + timestamp", endereco + "2025"),
    ]
    
    resultados = []
    
    for i, (nome, seed) in enumerate(seeds, 1):
        print(f"\n{i}. {nome}:")
        
        # Gerar hash da seed
        seed_bytes = seed.encode('utf-8')
        hash_seed = hashlib.sha256(seed_bytes).digest()
        
        # Diferentes formas de extrair chave privada
        chave1 = int.from_bytes(hash_seed[:8], byteorder='big') % (2**63)
        chave2 = int.from_bytes(hash_seed[8:16], byteorder='big') % (2**63)
        chave3 = int.from_bytes(hash_seed[16:24], byteorder='big') % (2**63)
        
        for j, chave in enumerate([chave1, chave2, chave3], 1):
            if chave == 0:
                chave = 1
            
            hash_errado = simular_gpu_errada_para_chave(chave).hex()
            
            print(f"   Variação {j}: Chave {chave} → Hash {hash_errado}")
            resultados.append((f"{nome} v{j}", chave, hash_errado))
    
    return resultados

def main():
    """Função principal"""
    if len(sys.argv) > 1:
        # Modo com argumento
        endereco = sys.argv[1]
        
        print("🎯 HASH ERRADO DIRETO")
        print("=" * 30)
        
        hash_errado, chave = endereco_para_hash_errado(endereco)
        
        if hash_errado:
            print(f"\n✅ RESULTADO:")
            print(f"Endereço: {endereco}")
            print(f"Hash errado: {hash_errado}")
            print(f"Chave simulada: {chave}")
            
            print(f"\n🚀 COMANDO PARA BUSCAR:")
            print(f"./buscar_chave_por_hash_errado {hash_errado} 1:1000000000")
            
            # Salvar resultado
            with open(f'hash_errado_{endereco[:10]}.txt', 'w') as f:
                f.write(f"Endereço: {endereco}\n")
                f.write(f"Hash errado: {hash_errado}\n")
                f.write(f"Chave simulada: {chave}\n")
                f.write(f"Comando: ./buscar_chave_por_hash_errado {hash_errado} 1:1000000000\n")
            
            print(f"💾 Salvo em: hash_errado_{endereco[:10]}.txt")
        
    else:
        # Modo interativo
        print("🎯 CALCULADOR DE HASH ERRADO")
        print("=" * 40)
        
        endereco = input("Digite o endereço Bitcoin: ").strip()
        
        if not endereco:
            print("❌ Endereço não fornecido!")
            return
        
        print("\nEscolha o método:")
        print("1. 🎯 Hash errado simples")
        print("2. 📊 Múltiplos hashes errados")
        
        opcao = input("Opção (1/2): ").strip()
        
        if opcao == "1":
            hash_errado, chave = endereco_para_hash_errado(endereco)
            
            if hash_errado:
                print(f"\n✅ HASH ERRADO CALCULADO:")
                print(f"Hash errado: {hash_errado}")
                print(f"\n🚀 COMANDO PARA BUSCAR:")
                print(f"./buscar_chave_por_hash_errado {hash_errado} 1:1000000000")
        
        elif opcao == "2":
            resultados = gerar_multiplos_hashes_errados(endereco)
            
            print(f"\n📊 TODOS OS HASHES ERRADOS GERADOS:")
            print("=" * 50)
            
            for i, (nome, chave, hash_errado) in enumerate(resultados, 1):
                print(f"{i:2d}. {hash_errado} ({nome})")
            
            print(f"\n💡 TESTE CADA UM DESTES HASHES:")
            for i, (nome, chave, hash_errado) in enumerate(resultados, 1):
                print(f"./buscar_chave_por_hash_errado {hash_errado} 1:1000000000")
        
        else:
            print("❌ Opção inválida!")

if __name__ == "__main__":
    main()
