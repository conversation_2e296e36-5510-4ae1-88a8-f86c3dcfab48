# Makefile para Buscar Chave por Hash Errado

NVCC = nvcc
TARGET = buscar_chave_por_hash_errado
SOURCE = buscar_chave_por_hash_errado.cu

# Detectar arquitetura automaticamente
GPU_ARCH_RAW := $(shell nvidia-smi --query-gpu=compute_cap --format=csv,noheader,nounits 2>/dev/null | head -1)
GPU_ARCH_MAJOR := $(shell echo $(GPU_ARCH_RAW) | cut -d'.' -f1)

# Mapear arquiteturas
ifeq ($(GPU_ARCH_MAJOR),12)
    GPU_ARCH = 89  # RTX 5090
else ifeq ($(GPU_ARCH_MAJOR),8)
    GPU_ARCH = 86  # RTX 30xx/40xx
else ifeq ($(GPU_ARCH_MAJOR),7)
    GPU_ARCH = 75  # RTX 20xx
else
    GPU_ARCH = 75  # Default
endif

# Flags otimizadas
NVCC_FLAGS = -O3 -arch=sm_$(GPU_ARCH) -std=c++11 --use_fast_math

all: $(TARGET)

$(TARGET): $(SOURCE)
	@echo "🔧 Compilando buscar chave por hash errado..."
	@echo "GPU Arch detectada: sm_$(GPU_ARCH) (Compute $(GPU_ARCH_RAW))"
	$(NVCC) $(NVCC_FLAGS) -o $(TARGET) $(SOURCE)
	@echo "✅ Compilação concluída!"

# Compilação automática com fallback
auto: 
	@echo "🔧 Compilação automática com fallback..."
	$(NVCC) -O3 -arch=sm_89 -std=c++11 --use_fast_math -o $(TARGET) $(SOURCE) 2>/dev/null || \
	$(NVCC) -O3 -arch=sm_86 -std=c++11 --use_fast_math -o $(TARGET) $(SOURCE) 2>/dev/null || \
	$(NVCC) -O3 -arch=sm_75 -std=c++11 --use_fast_math -o $(TARGET) $(SOURCE) 2>/dev/null || \
	$(NVCC) -O3 -std=c++11 -o $(TARGET) $(SOURCE)
	@echo "✅ Compilação automática concluída!"

# Teste com chave conhecida (chave privada 1)
test-chave1: $(TARGET)
	@echo "🧪 TESTE: Buscar chave privada 1"
	@echo "Hash errado da chave 1: 36df2f22295784ab7f81989f9247bfd99bb00c03"
	@echo "Deve encontrar chave privada: 1"
	./$(TARGET) 36df2f22295784ab7f81989f9247bfd99bb00c03 1:1000000

# Teste com chave conhecida (chave privada 2)
test-chave2: $(TARGET)
	@echo "🧪 TESTE: Buscar chave privada 2"
	@echo "Hash errado da chave 2: 5fed51813a4b0353320dbee6fc24a63c5f695181"
	@echo "Deve encontrar chave privada: 2"
	./$(TARGET) 5fed51813a4b0353320dbee6fc24a63c5f695181 1:1000000

# Teste com chave conhecida (chave privada 3)
test-chave3: $(TARGET)
	@echo "🧪 TESTE: Buscar chave privada 3"
	@echo "Hash errado da chave 3: b0548c85212204a8a9555adbbdb6dab85b77afa4"
	@echo "Deve encontrar chave privada: 3"
	./$(TARGET) b0548c85212204a8a9555adbbdb6dab85b77afa4 1:1000000

# Teste interativo
test-interactive: $(TARGET)
	@echo "🧪 TESTE INTERATIVO"
	@echo "=================="
	@echo "O programa vai perguntar o hash errado e range"
	@echo "Sugestão: 36df2f22295784ab7f81989f9247bfd99bb00c03 e range 1:1000000"
	./$(TARGET)

# Teste com range hex
test-hex: $(TARGET)
	@echo "🧪 TESTE RANGE HEX"
	@echo "=================="
	./$(TARGET) 36df2f22295784ab7f81989f9247bfd99bb00c03 1:ffffff

# Teste todos
test-all: test-chave1 test-chave2 test-chave3

clean:
	rm -f $(TARGET)

info:
	@echo "ℹ️  Informações do sistema:"
	@echo "NVCC: $(shell nvcc --version | grep release)"
	@echo "GPU: $(shell nvidia-smi --query-gpu=name --format=csv,noheader)"
	@echo "Compute Cap: $(GPU_ARCH_RAW)"
	@echo "Arquitetura: sm_$(GPU_ARCH)"

help:
	@echo "🎯 BUSCAR CHAVE PRIVADA POR HASH ERRADO"
	@echo "======================================="
	@echo ""
	@echo "📋 COMANDOS:"
	@echo "make all              - Compilar programa"
	@echo "make auto             - Compilação automática"
	@echo "make test-chave1      - Testar com chave privada 1"
	@echo "make test-chave2      - Testar com chave privada 2"
	@echo "make test-chave3      - Testar com chave privada 3"
	@echo "make test-all         - Testar todas as chaves conhecidas"
	@echo "make test-interactive - Teste interativo"
	@echo "make test-hex         - Teste com range hex"
	@echo "make clean            - Limpar"
	@echo "make info             - Informações do sistema"
	@echo ""
	@echo "🚀 USO DIRETO:"
	@echo "./buscar_chave_por_hash_errado <hash_errado> <range>"
	@echo ""
	@echo "📝 EXEMPLOS:"
	@echo "./buscar_chave_por_hash_errado 36df2f22295784ab7f81989f9247bfd99bb00c03 1:1000000"
	@echo "./buscar_chave_por_hash_errado 5fed51813a4b0353320dbee6fc24a63c5f695181 1:ffffff"
	@echo ""
	@echo "🎯 LÓGICA DO PROGRAMA:"
	@echo "1. Recebe hash errado alvo"
	@echo "2. Testa chaves privadas no range"
	@echo "3. Para cada chave: calcula hash errado do kernel"
	@echo "4. Quando hash errado bater = encontrou a chave privada!"
	@echo ""
	@echo "💡 HASHES ERRADOS CONHECIDOS:"
	@echo "Chave 1: 36df2f22295784ab7f81989f9247bfd99bb00c03"
	@echo "Chave 2: 5fed51813a4b0353320dbee6fc24a63c5f695181"
	@echo "Chave 3: b0548c85212204a8a9555adbbdb6dab85b77afa4"

.PHONY: all auto test-chave1 test-chave2 test-chave3 test-all test-interactive test-hex clean info help
