# Makefile para Sistema Completo

NVCC = nvcc
TARGET_CUDA = descobrir_cuda_completo
SOURCE_CUDA = descobrir_cuda_completo.cu

# Arquitetura GPU
GPU_ARCH = 89

# Flags
NVCC_FLAGS = -O3 -arch=sm_$(GPU_ARCH) -std=c++11

all: $(TARGET_CUDA) setup-python

$(TARGET_CUDA): $(SOURCE_CUDA)
	@echo "🔧 Compilando sistema CUDA completo..."
	$(NVCC) $(NVCC_FLAGS) -o $(TARGET_CUDA) $(SOURCE_CUDA)
	@echo "✅ Compilação CUDA concluída!"

# Compilação automática
auto: 
	@echo "🔧 Compilação automática..."
	$(NVCC) -O3 -arch=sm_75 -std=c++11 -o $(TARGET_CUDA) $(SOURCE_CUDA) || \
	$(NVCC) -O3 -arch=sm_86 -std=c++11 -o $(TARGET_CUDA) $(SOURCE_CUDA) || \
	$(NVCC) -O3 -arch=sm_89 -std=c++11 -o $(TARGET_CUDA) $(SOURCE_CUDA) || \
	$(NVCC) -O3 -std=c++11 -o $(TARGET_CUDA) $(SOURCE_CUDA)
	@echo "✅ Compilação automática concluída!"

# Setup Python
setup-python:
	@echo "🐍 Verificando dependências Python..."
	@python3 -c "import requests" 2>/dev/null || (echo "❌ Instale requests: pip3 install requests" && exit 1)
	@python3 -c "import base58" 2>/dev/null || (echo "❌ Instale base58: pip3 install base58" && exit 1)
	@echo "✅ Dependências Python OK"

# Instalar dependências Python
install-deps:
	@echo "📦 Instalando dependências Python..."
	pip3 install requests base58
	@echo "✅ Dependências instaladas!"

# Configurar Telegram
setup-telegram:
	@echo "📱 Configurando Telegram..."
	python3 configurar_telegram.py

# Teste completo
test: $(TARGET_CUDA)
	@echo "🧪 Teste do sistema completo..."
	./$(TARGET_CUDA) f6f5431d25bbf7b12e8add9af5e3475c44a0a5b8 1 1000 **********************************

# Teste Python
test-python:
	@echo "🧪 Teste Python..."
	python3 descobrir_completo.py

# Executar busca completa
run-full: $(TARGET_CUDA)
	@echo "🚀 Executando busca completa..."
	./$(TARGET_CUDA) f6f5431d25bbf7b12e8add9af5e3475c44a0a5b8 4611686018427387904 9223372036854775807 **********************************

clean:
	rm -f $(TARGET_CUDA)
	rm -f *.json

info:
	@echo "ℹ️  Informações do sistema:"
	nvcc --version
	nvidia-smi --query-gpu=name,compute_cap,memory.total --format=csv
	python3 --version

help:
	@echo "🎯 SISTEMA COMPLETO - DESCOBRIR CHAVE BITCOIN"
	@echo "============================================="
	@echo ""
	@echo "📋 COMANDOS PRINCIPAIS:"
	@echo "make all           - Compilar tudo"
	@echo "make auto          - Compilação automática"
	@echo "make setup-telegram - Configurar Telegram"
	@echo "make install-deps  - Instalar dependências Python"
	@echo "make test          - Teste rápido"
	@echo "make test-python   - Teste Python"
	@echo "make run-full      - Busca completa"
	@echo "make clean         - Limpar"
	@echo "make info          - Informações do sistema"
	@echo ""
	@echo "🎯 FLUXO RECOMENDADO:"
	@echo "1. make install-deps    # Instalar dependências"
	@echo "2. make auto           # Compilar programa"
	@echo "3. make setup-telegram # Configurar Telegram"
	@echo "4. make test           # Teste rápido"
	@echo "5. make run-full       # Busca completa"
	@echo ""
	@echo "📱 RECURSOS:"
	@echo "• Busca CUDA ultra-rápida"
	@echo "• Validação completa (endereço, WIF, hashes)"
	@echo "• Notificação automática no Telegram"
	@echo "• Salvamento local dos resultados"
	@echo "• Progresso em tempo real"
	@echo ""
	@echo "🎊 Quando encontrar a chave, você receberá:"
	@echo "• Notificação no Telegram"
	@echo "• Arquivo JSON com todos os dados"
	@echo "• Chave privada, WIF, endereço, hashes"

.PHONY: all auto setup-python install-deps setup-telegram test test-python run-full clean info help
