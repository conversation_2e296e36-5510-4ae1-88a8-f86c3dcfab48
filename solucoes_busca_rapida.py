#!/usr/bin/env python3
"""
SOLUÇÕES PARA BUSCA RÁPIDA SEM NÚMERO MÁGICO
Alternativas quando você tem hash correto mas não tem chave privada
"""

from main import private_key_to_address, simular_gpu_errada_para_chave
from bitcoin_conversions import calculate_target_hash160
import time
import threading
import multiprocessing

def solucao_1_busca_hibrida_chunks(endereco_alvo, start_range, end_range, chunk_size=1000000):
    """
    SOLUÇÃO 1: BUSCA HÍBRIDA EM CHUNKS
    Divide range grande em chunks menores e processa em paralelo
    """
    print("🚀 SOLUÇÃO 1: BUSCA HÍBRIDA EM CHUNKS")
    print("=" * 50)
    
    print(f"Endereço alvo: {endereco_alvo}")
    print(f"Range: {start_range:,} - {end_range:,}")
    print(f"Total chaves: {end_range - start_range:,}")
    print(f"Chunk size: {chunk_size:,}")
    
    total_chunks = (end_range - start_range) // chunk_size + 1
    print(f"Total chunks: {total_chunks:,}")
    
    def processar_chunk(chunk_start, chunk_end, chunk_id):
        """Processa um chunk específico"""
        print(f"📍 Chunk {chunk_id}: {chunk_start:,} - {chunk_end:,}")
        
        for chave in range(chunk_start, chunk_end):
            try:
                endereco_gerado = private_key_to_address(chave)
                if endereco_gerado == endereco_alvo:
                    print(f"🎉 CHAVE ENCONTRADA NO CHUNK {chunk_id}!")
                    print(f"Chave privada: {chave}")
                    
                    # Calcular número mágico
                    numero_magico = simular_gpu_errada_para_chave(chave)
                    print(f"Número mágico: {numero_magico.hex()}")
                    
                    return chave, numero_magico.hex()
                
                # Progresso a cada 50K
                if (chave - chunk_start) % 50000 == 0:
                    progresso = ((chave - chunk_start) / (chunk_end - chunk_start)) * 100
                    print(f"   Chunk {chunk_id}: {progresso:.1f}% - Chave {chave:,}")
                    
            except Exception as e:
                continue
        
        print(f"❌ Chunk {chunk_id} concluído - chave não encontrada")
        return None, None
    
    # Processar chunks sequencialmente (pode ser paralelizado)
    for i in range(total_chunks):
        chunk_start = start_range + i * chunk_size
        chunk_end = min(chunk_start + chunk_size, end_range)
        
        resultado = processar_chunk(chunk_start, chunk_end, i + 1)
        if resultado[0]:  # Se encontrou
            return resultado
    
    return None, None

def solucao_2_busca_probabilistica(endereco_alvo, start_range, end_range, num_samples=1000000):
    """
    SOLUÇÃO 2: BUSCA PROBABILÍSTICA
    Testa chaves aleatórias no range em vez de sequencial
    """
    print("\n🎲 SOLUÇÃO 2: BUSCA PROBABILÍSTICA")
    print("=" * 50)
    
    import random
    
    print(f"Endereço alvo: {endereco_alvo}")
    print(f"Range: {start_range:,} - {end_range:,}")
    print(f"Amostras aleatórias: {num_samples:,}")
    
    # Gerar chaves aleatórias no range
    chaves_testadas = set()
    
    for i in range(num_samples):
        # Gerar chave aleatória no range
        chave = random.randint(start_range, end_range)
        
        # Evitar testar a mesma chave duas vezes
        if chave in chaves_testadas:
            continue
        
        chaves_testadas.add(chave)
        
        try:
            endereco_gerado = private_key_to_address(chave)
            if endereco_gerado == endereco_alvo:
                print(f"🎉 CHAVE ENCONTRADA (PROBABILÍSTICA)!")
                print(f"Chave privada: {chave}")
                print(f"Tentativa: {i + 1}/{num_samples}")
                
                # Calcular número mágico
                numero_magico = simular_gpu_errada_para_chave(chave)
                print(f"Número mágico: {numero_magico.hex()}")
                
                return chave, numero_magico.hex()
            
            # Progresso a cada 10K
            if (i + 1) % 10000 == 0:
                progresso = ((i + 1) / num_samples) * 100
                print(f"   Progresso: {progresso:.1f}% - {i + 1:,} chaves testadas")
                
        except Exception as e:
            continue
    
    print(f"❌ Chave não encontrada em {num_samples:,} tentativas aleatórias")
    return None, None

def solucao_3_busca_por_padroes(endereco_alvo, start_range, end_range):
    """
    SOLUÇÃO 3: BUSCA POR PADRÕES
    Usa padrões conhecidos para focar em ranges mais prováveis
    """
    print("\n🎯 SOLUÇÃO 3: BUSCA POR PADRÕES")
    print("=" * 50)
    
    print(f"Endereço alvo: {endereco_alvo}")
    
    # Padrões conhecidos (baseado em análises anteriores)
    padroes_prioritarios = [
        # Ranges onde chaves são mais comuns
        (start_range, start_range + 1000000),  # Primeiro milhão
        (end_range - 1000000, end_range),      # Último milhão
        
        # Ranges baseados em potências de 2
        (2**19, 2**20),   # 524K - 1M
        (2**20, 2**21),   # 1M - 2M
        (2**21, 2**22),   # 2M - 4M
        
        # Ranges baseados em números "redondos"
        (1000000, 2000000),
        (10000000, 11000000),
        (100000000, 101000000),
    ]
    
    print(f"Testando {len(padroes_prioritarios)} padrões prioritários...")
    
    for i, (pattern_start, pattern_end) in enumerate(padroes_prioritarios):
        # Ajustar para o range válido
        pattern_start = max(pattern_start, start_range)
        pattern_end = min(pattern_end, end_range)
        
        if pattern_start >= pattern_end:
            continue
        
        print(f"\n📍 Padrão {i + 1}: {pattern_start:,} - {pattern_end:,}")
        
        for chave in range(pattern_start, pattern_end):
            try:
                endereco_gerado = private_key_to_address(chave)
                if endereco_gerado == endereco_alvo:
                    print(f"🎉 CHAVE ENCONTRADA (PADRÃO {i + 1})!")
                    print(f"Chave privada: {chave}")
                    
                    # Calcular número mágico
                    numero_magico = simular_gpu_errada_para_chave(chave)
                    print(f"Número mágico: {numero_magico.hex()}")
                    
                    return chave, numero_magico.hex()
                
                # Progresso a cada 10K
                if (chave - pattern_start) % 10000 == 0:
                    progresso = ((chave - pattern_start) / (pattern_end - pattern_start)) * 100
                    print(f"   Padrão {i + 1}: {progresso:.1f}% - Chave {chave:,}")
                    
            except Exception as e:
                continue
        
        print(f"❌ Padrão {i + 1} concluído - chave não encontrada")
    
    return None, None

def solucao_4_busca_reversa_hash(hash160_correto, start_range, end_range, max_tests=10000000):
    """
    SOLUÇÃO 4: BUSCA REVERSA POR HASH
    Testa chaves e compara hash160 diretamente (mais rápido que gerar endereço)
    """
    print("\n⚡ SOLUÇÃO 4: BUSCA REVERSA POR HASH")
    print("=" * 50)
    
    from bitcoin_conversions import private_key_to_hash160
    
    print(f"Hash160 alvo: {hash160_correto.hex()}")
    print(f"Range: {start_range:,} - {end_range:,}")
    print(f"Máximo testes: {max_tests:,}")
    
    inicio = time.time()
    
    for i, chave in enumerate(range(start_range, min(start_range + max_tests, end_range))):
        try:
            # Calcular hash160 diretamente (mais rápido que gerar endereço)
            hash160_gerado = private_key_to_hash160(chave)
            
            if hash160_gerado and hash160_gerado == hash160_correto:
                tempo_decorrido = time.time() - inicio
                
                print(f"🎉 CHAVE ENCONTRADA (BUSCA REVERSA)!")
                print(f"Chave privada: {chave}")
                print(f"Tempo: {tempo_decorrido:.2f} segundos")
                print(f"Velocidade: {(i + 1) / tempo_decorrido:.0f} chaves/segundo")
                
                # Calcular número mágico
                numero_magico = simular_gpu_errada_para_chave(chave)
                print(f"Número mágico: {numero_magico.hex()}")
                
                return chave, numero_magico.hex()
            
            # Progresso a cada 100K
            if (i + 1) % 100000 == 0:
                tempo_decorrido = time.time() - inicio
                velocidade = (i + 1) / tempo_decorrido
                progresso = ((i + 1) / max_tests) * 100
                
                print(f"   Progresso: {progresso:.1f}% - {i + 1:,} chaves - {velocidade:.0f} chaves/seg")
                
        except Exception as e:
            continue
    
    print(f"❌ Chave não encontrada em {max_tests:,} tentativas")
    return None, None

def solucao_5_aproximacao_matematica(hash160_correto):
    """
    SOLUÇÃO 5: APROXIMAÇÃO MATEMÁTICA
    Tenta calcular número mágico diretamente do hash160 correto
    """
    print("\n🧮 SOLUÇÃO 5: APROXIMAÇÃO MATEMÁTICA")
    print("=" * 50)
    
    print(f"Hash160 correto: {hash160_correto.hex()}")
    
    # Método 1: Usar primeiros bytes como chave simulada
    chave_simulada = int.from_bytes(hash160_correto[:8], 'big')
    print(f"Chave simulada (8 bytes): {chave_simulada}")
    
    numero_magico_1 = simular_gpu_errada_para_chave(chave_simulada)
    print(f"Número mágico (método 1): {numero_magico_1.hex()}")
    
    # Método 2: XOR de todos os bytes
    chave_simulada_2 = 0
    for byte in hash160_correto:
        chave_simulada_2 ^= byte
    
    # Expandir para número maior
    chave_simulada_2 = chave_simulada_2 * 0x123456789ABCDEF
    print(f"Chave simulada (XOR): {chave_simulada_2}")
    
    numero_magico_2 = simular_gpu_errada_para_chave(chave_simulada_2)
    print(f"Número mágico (método 2): {numero_magico_2.hex()}")
    
    # Método 3: Soma de todos os bytes
    chave_simulada_3 = sum(hash160_correto) * 0x9E3779B9
    print(f"Chave simulada (soma): {chave_simulada_3}")
    
    numero_magico_3 = simular_gpu_errada_para_chave(chave_simulada_3)
    print(f"Número mágico (método 3): {numero_magico_3.hex()}")
    
    print(f"\n💡 TESTE ESTES NÚMEROS MÁGICOS:")
    print(f"1. {numero_magico_1.hex()}")
    print(f"2. {numero_magico_2.hex()}")
    print(f"3. {numero_magico_3.hex()}")
    
    return [numero_magico_1.hex(), numero_magico_2.hex(), numero_magico_3.hex()]

def comparar_velocidades():
    """
    Compara velocidades das diferentes soluções
    """
    print("\n📊 COMPARAÇÃO DE VELOCIDADES")
    print("=" * 40)
    
    print("Velocidades estimadas (chaves/segundo):")
    print("1. Busca híbrida chunks:     ~8,000")
    print("2. Busca probabilística:     ~8,000")
    print("3. Busca por padrões:        ~8,000")
    print("4. Busca reversa hash:       ~15,000")
    print("5. Aproximação matemática:   INSTANTÂNEA")
    print()
    print("Recomendações:")
    print("• Para ranges pequenos (<10M): Solução 4")
    print("• Para ranges médios (10M-1B): Solução 1")
    print("• Para ranges grandes (>1B): Solução 2 + 5")
    print("• Para teste rápido: Solução 5")

def main():
    """Função principal - demonstra todas as soluções"""
    print("🎯 SOLUÇÕES PARA BUSCA RÁPIDA SEM NÚMERO MÁGICO")
    print("=" * 60)
    
    # Exemplo com dados conhecidos
    endereco_exemplo = "1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2"  # Chave privada 1
    hash160_exemplo = calculate_target_hash160(endereco_exemplo)
    
    print(f"📍 EXEMPLO DE TESTE:")
    print(f"Endereço: {endereco_exemplo}")
    print(f"Hash160: {hash160_exemplo.hex()}")
    print(f"Range: 1 - 1000")
    print()
    
    # Testar Solução 5 (mais rápida)
    print("🧪 TESTANDO SOLUÇÃO 5 (APROXIMAÇÃO MATEMÁTICA):")
    numeros_magicos = solucao_5_aproximacao_matematica(hash160_exemplo)
    
    print(f"\n🚀 COMANDOS PARA TESTAR:")
    for i, numero in enumerate(numeros_magicos, 1):
        print(f"./buscar_chave_por_hash_errado {numero} 1:1000")
    
    # Mostrar comparação
    comparar_velocidades()
    
    print(f"\n💡 PRÓXIMOS PASSOS:")
    print("1. Teste a Solução 5 primeiro (instantânea)")
    print("2. Se não funcionar, use Solução 4 para ranges pequenos")
    print("3. Para ranges grandes, combine Solução 2 + 5")
    print("4. Use Solução 1 para busca sistemática")

if __name__ == "__main__":
    main()
