#!/usr/bin/env python3
"""
VERIFICADOR DE RESULTADO CUDA

Este programa verifica se o resultado encontrado pela busca CUDA
está correto, calculando o endereço correspondente.
"""

import sys
from main import private_key_to_address, simular_gpu_errada_para_chave, calculate_target_hash160

def verificar_chave_encontrada(chave_privada, hash_errado_esperado):
    """Verifica se a chave privada encontrada está correta"""
    print(f"🔍 VERIFICANDO CHAVE PRIVADA: {chave_privada}")
    print("="*60)
    
    try:
        # Gerar endereço da chave privada
        endereco_gerado = private_key_to_address(chave_privada)
        print(f"Endereço gerado: {endereco_gerado}")
        
        # Calcular hash160 correto
        hash160_correto = calculate_target_hash160(endereco_gerado)
        print(f"Hash160 correto: {hash160_correto.hex()}")
        
        # Calcular hash160 errado usando nossa função
        hash160_errado = simular_gpu_errada_para_chave(chave_privada)
        print(f"Hash160 errado:  {hash160_errado.hex()}")
        
        # Verificar se o hash errado bate com o esperado
        if hash160_errado.hex() == hash_errado_esperado.lower():
            print(f"✅ VERIFICAÇÃO PASSOU!")
            print(f"   A chave privada {chave_privada} realmente gera o hash errado esperado")
            return True
        else:
            print(f"❌ VERIFICAÇÃO FALHOU!")
            print(f"   Hash errado esperado: {hash_errado_esperado}")
            print(f"   Hash errado calculado: {hash160_errado.hex()}")
            return False
            
    except Exception as e:
        print(f"❌ Erro na verificação: {e}")
        return False

def testar_chaves_conhecidas():
    """Testa as chaves conhecidas para validar o algoritmo"""
    print("🧪 TESTANDO CHAVES CONHECIDAS")
    print("="*60)
    
    chaves_conhecidas = [
        (1, "36df2f22295784ab7f81989f9247bfd99bb00c03"),
        (2, "5fed51813a4b0353320dbee6fc24a63c5f695181"),
        (3, "b0548c85212204a8a9555adbbdb6dab85b77afa4")
    ]
    
    sucessos = 0
    
    for chave, hash_esperado in chaves_conhecidas:
        print(f"\n🔍 Testando chave {chave}:")
        if verificar_chave_encontrada(chave, hash_esperado):
            sucessos += 1
        print()
    
    print(f"📊 RESULTADO DOS TESTES:")
    print(f"Sucessos: {sucessos}/{len(chaves_conhecidas)}")
    
    if sucessos == len(chaves_conhecidas):
        print(f"✅ Todos os testes passaram! Algoritmo está correto.")
        return True
    else:
        print(f"❌ Alguns testes falharam! Verificar algoritmo.")
        return False

def benchmark_velocidade_python():
    """Faz benchmark da velocidade do Python para comparação"""
    print("📊 BENCHMARK VELOCIDADE PYTHON")
    print("="*60)
    
    import time
    
    # Testar 10,000 chaves
    num_testes = 10000
    print(f"Testando {num_testes:,} chaves...")
    
    inicio = time.time()
    
    for chave in range(1, num_testes + 1):
        try:
            endereco = private_key_to_address(chave)
            hash_errado = simular_gpu_errada_para_chave(chave)
        except:
            continue
    
    fim = time.time()
    tempo_total = fim - inicio
    velocidade = num_testes / tempo_total
    
    print(f"Tempo total: {tempo_total:.2f} segundos")
    print(f"Velocidade: {velocidade:.0f} chaves/segundo")
    print(f"Velocidade: {velocidade/1000:.1f}K chaves/segundo")
    
    return velocidade

def comparar_com_cuda(velocidade_python, velocidade_cuda_estimada=100000000):
    """Compara velocidades Python vs CUDA"""
    print("⚖️  COMPARAÇÃO PYTHON vs CUDA")
    print("="*60)
    
    speedup = velocidade_cuda_estimada / velocidade_python
    
    print(f"Velocidade Python: {velocidade_python:.0f} chaves/seg")
    print(f"Velocidade CUDA (estimada): {velocidade_cuda_estimada:,} chaves/seg")
    print(f"Speedup: {speedup:.0f}x mais rápido")
    
    # Calcular tempo para encontrar chaves em diferentes ranges
    ranges = [
        ("100K", 100000),
        ("1M", 1000000),
        ("10M", 10000000),
        ("100M", 100000000),
        ("1B", 1000000000)
    ]
    
    print(f"\n📈 TEMPO PARA ENCONTRAR CHAVE EM DIFERENTES RANGES:")
    print(f"{'Range':<10} {'Python':<15} {'CUDA':<15} {'Speedup':<10}")
    print("-" * 55)
    
    for nome, tamanho in ranges:
        tempo_python = tamanho / velocidade_python
        tempo_cuda = tamanho / velocidade_cuda_estimada
        speedup_range = tempo_python / tempo_cuda
        
        # Formatar tempos
        def formatar_tempo(segundos):
            if segundos < 60:
                return f"{segundos:.1f}s"
            elif segundos < 3600:
                return f"{segundos/60:.1f}m"
            elif segundos < 86400:
                return f"{segundos/3600:.1f}h"
            else:
                return f"{segundos/86400:.1f}d"
        
        print(f"{nome:<10} {formatar_tempo(tempo_python):<15} {formatar_tempo(tempo_cuda):<15} {speedup_range:.0f}x")

def main():
    """Função principal"""
    print("🔍 VERIFICADOR DE RESULTADO CUDA")
    print("Este programa verifica resultados da busca CUDA")
    print("="*60)
    
    if len(sys.argv) > 1:
        # Verificar chave específica fornecida como argumento
        try:
            chave_privada = int(sys.argv[1])
            hash_esperado = sys.argv[2] if len(sys.argv) > 2 else ""
            
            print(f"Verificando chave fornecida: {chave_privada}")
            if hash_esperado:
                verificar_chave_encontrada(chave_privada, hash_esperado)
            else:
                # Apenas mostrar informações da chave
                endereco = private_key_to_address(chave_privada)
                hash_errado = simular_gpu_errada_para_chave(chave_privada)
                print(f"Endereço: {endereco}")
                print(f"Hash errado: {hash_errado.hex()}")
        
        except ValueError:
            print("❌ Chave privada deve ser um número")
            sys.exit(1)
        except Exception as e:
            print(f"❌ Erro: {e}")
            sys.exit(1)
    
    else:
        # Executar testes completos
        print("Executando verificação completa...\n")
        
        # Teste 1: Validar chaves conhecidas
        if not testar_chaves_conhecidas():
            print("❌ Testes de validação falharam!")
            sys.exit(1)
        
        print()
        
        # Teste 2: Benchmark Python
        velocidade_python = benchmark_velocidade_python()
        
        print()
        
        # Teste 3: Comparação com CUDA
        comparar_com_cuda(velocidade_python)
        
        print()
        print("✅ Verificação completa concluída!")
        print()
        print("🎯 COMO USAR ESTE VERIFICADOR:")
        print("python3 verificar_resultado_cuda.py <chave_privada> [hash_esperado]")
        print()
        print("📝 EXEMPLOS:")
        print("python3 verificar_resultado_cuda.py 1")
        print("python3 verificar_resultado_cuda.py 12345 36df2f22295784ab7f81989f9247bfd99bb00c03")

if __name__ == "__main__":
    main()
