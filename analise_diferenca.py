#!/usr/bin/env python3
"""
Análise detalhada da diferença entre CPU e GPU
Para descobrir exatamente onde está o problema
"""

import sys
import hashlib
from bitcoin_conversions import private_key_to_public_key

def ripemd160(data):
    """Implementação manual do RIPEMD160"""
    def rol(n, b):
        return ((n << b) | (n >> (32 - b))) & 0xffffffff
    
    def f(j, x, y, z):
        if j < 16:
            return x ^ y ^ z
        elif j < 32:
            return (x & y) | (~x & z)
        elif j < 48:
            return (x | ~y) ^ z
        elif j < 64:
            return (x & z) | (y & ~z)
        else:
            return x ^ (y | ~z)
    
    def K(j):
        if j < 16:
            return 0x00000000
        elif j < 32:
            return 0x5A827999
        elif j < 48:
            return 0x6ED9EBA1
        elif j < 64:
            return 0x8F1BBCDC
        else:
            return 0xA953FD4E
    
    def Kh(j):
        if j < 16:
            return 0x50A28BE6
        elif j < 32:
            return 0x5C4DD124
        elif j < 48:
            return 0x6D703EF3
        elif j < 64:
            return 0x7A6D76E9
        else:
            return 0x00000000
    
    # Padding
    msg = bytearray(data)
    msg_len = len(data)
    msg.append(0x80)
    
    while len(msg) % 64 != 56:
        msg.append(0x00)
    
    msg.extend((msg_len * 8).to_bytes(8, 'little'))
    
    # Initialize hash values
    h0 = 0x67452301
    h1 = 0xEFCDAB89
    h2 = 0x98BADCFE
    h3 = 0x10325476
    h4 = 0xC3D2E1F0
    
    # Process message in 512-bit chunks
    for chunk_start in range(0, len(msg), 64):
        chunk = msg[chunk_start:chunk_start + 64]
        w = [int.from_bytes(chunk[i:i+4], 'little') for i in range(0, 64, 4)]
        
        # Initialize hash value for this chunk
        al, bl, cl, dl, el = h0, h1, h2, h3, h4
        ar, br, cr, dr, er = h0, h1, h2, h3, h4
        
        # Left line
        r = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
             7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8,
             3, 10, 14, 4, 9, 15, 8, 1, 2, 7, 0, 6, 13, 11, 5, 12,
             1, 9, 11, 10, 0, 8, 12, 4, 13, 3, 7, 15, 14, 5, 6, 2,
             4, 0, 5, 9, 7, 12, 2, 10, 14, 1, 3, 8, 11, 6, 15, 13]
        
        s = [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8,
             7, 6, 8, 13, 11, 9, 7, 15, 7, 12, 15, 9, 11, 7, 13, 12,
             11, 13, 6, 7, 14, 9, 13, 15, 14, 8, 13, 6, 5, 12, 7, 5,
             11, 12, 14, 15, 14, 15, 9, 8, 9, 14, 5, 6, 8, 6, 5, 12,
             9, 15, 5, 11, 6, 8, 13, 12, 5, 12, 13, 14, 11, 8, 5, 6]
        
        # Right line
        rh = [5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12,
              6, 11, 3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2,
              15, 5, 1, 3, 7, 14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13,
              8, 6, 4, 1, 3, 11, 15, 0, 5, 12, 2, 13, 9, 7, 10, 14,
              12, 15, 10, 4, 1, 5, 8, 7, 6, 2, 13, 14, 0, 3, 9, 11]
        
        sh = [8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6,
              9, 13, 15, 7, 12, 8, 9, 11, 7, 7, 12, 7, 6, 15, 13, 11,
              9, 7, 15, 11, 8, 6, 6, 14, 12, 13, 5, 14, 13, 13, 7, 5,
              15, 5, 8, 11, 14, 14, 6, 14, 6, 9, 12, 9, 12, 5, 15, 8,
              8, 5, 12, 9, 12, 5, 14, 6, 8, 13, 6, 5, 15, 13, 11, 11]
        
        for j in range(80):
            # Left line
            t = (al + f(j, bl, cl, dl) + w[r[j]] + K(j)) & 0xffffffff
            t = rol(t, s[j]) + el & 0xffffffff
            al, bl, cl, dl, el = el, t, bl, rol(cl, 10), dl
            
            # Right line
            t = (ar + f(79-j, br, cr, dr) + w[rh[j]] + Kh(j)) & 0xffffffff
            t = rol(t, sh[j]) + er & 0xffffffff
            ar, br, cr, dr, er = er, t, br, rol(cr, 10), dr
        
        # Add this chunk's hash to result so far
        t = (h1 + cl + dr) & 0xffffffff
        h1 = (h2 + dl + er) & 0xffffffff
        h2 = (h3 + el + ar) & 0xffffffff
        h3 = (h4 + al + br) & 0xffffffff
        h4 = (h0 + bl + cr) & 0xffffffff
        h0 = t
    
    # Produce the final hash value
    return b''.join(h.to_bytes(4, 'little') for h in [h0, h1, h2, h3, h4])

def analisar_chave_cpu(private_key_int):
    """Analisa como a CPU gera a chave pública"""
    print("🔍 ANÁLISE DETALHADA - CPU (REAL)")
    print("=" * 60)
    
    # Obter chave pública real
    public_key_hex = private_key_to_public_key(private_key_int)
    public_key_bytes = bytes.fromhex(public_key_hex)
    
    print(f"Chave Privada: {private_key_int}")
    print(f"Chave Pública: {public_key_hex}")
    print(f"Tamanho: {len(public_key_bytes)} bytes")
    print(f"Prefixo: 0x{public_key_bytes[0]:02x}")
    
    # Analisar coordenada X
    x_coord_bytes = public_key_bytes[1:]
    x_coord_int = int.from_bytes(x_coord_bytes, 'big')
    print(f"Coordenada X: 0x{x_coord_int:064x}")
    
    # Quebrar em partes de 64 bits para comparar com GPU
    x_parts = []
    for i in range(4):
        part = (x_coord_int >> (192 - i*64)) & 0xFFFFFFFFFFFFFFFF
        x_parts.append(part)
        print(f"  X[{i}]: 0x{part:016x}")
    
    return {
        'public_key': public_key_bytes,
        'x_coord': x_coord_int,
        'x_parts': x_parts,
        'prefix': public_key_bytes[0]
    }

def simular_gpu_original(private_key_int):
    """Simulação original da GPU (que está errada)"""
    print("\n🎮 SIMULAÇÃO ORIGINAL GPU (ERRADA)")
    print("=" * 60)
    
    key_lo = private_key_int & 0xFFFFFFFFFFFFFFFF
    key_hi = (private_key_int >> 64) & 0xFFFFFFFFFFFFFFFF
    
    print(f"key_lo: 0x{key_lo:016x}")
    print(f"key_hi: 0x{key_hi:016x}")
    
    # Coordenadas do ponto gerador
    gx = [0x79BE667EF9DCBBAC, 0x55A06295CE870B07, 0x029BFCDB2DCE28D9, 0x59F2815B16F81798]
    gy = [0x483ADA7726A3C465, 0x5DA4FBFC0E1108A8, 0xFD17B448A6855419, 0x9C47D08FFB10D4B8]
    
    print("Ponto Gerador G:")
    for i in range(4):
        print(f"  gx[{i}]: 0x{gx[i]:016x}")
        print(f"  gy[{i}]: 0x{gy[i]:016x}")
    
    # Simulação (errada)
    px = [0] * 4
    py = [0] * 4
    
    for i in range(4):
        px[i] = gx[i]
        py[i] = gy[i]
        
        if i < 2:
            px[i] ^= key_lo if i == 0 else key_hi
            py[i] ^= key_hi if i == 0 else key_lo
        else:
            px[i] ^= (key_lo >> 32) if i == 2 else (key_hi >> 32)
            py[i] ^= (key_hi >> 32) if i == 2 else (key_lo >> 32)
        
        px[i] = ((px[i] << 1) ^ (px[i] >> 63) ^ gy[i]) & 0xFFFFFFFFFFFFFFFF
        py[i] = ((py[i] << 1) ^ (py[i] >> 63) ^ gx[i]) & 0xFFFFFFFFFFFFFFFF
    
    print("Resultado da simulação:")
    for i in range(4):
        print(f"  px[{i}]: 0x{px[i]:016x}")
        print(f"  py[{i}]: 0x{py[i]:016x}")
    
    return px, py

def main():
    """Função principal"""
    print("ANÁLISE DA DIFERENÇA ENTRE CPU E GPU")
    print("Objetivo: Descobrir por que os hash160 são diferentes")
    print("=" * 80)
    
    private_key = 1
    
    # Analisar CPU
    cpu_data = analisar_chave_cpu(private_key)
    
    # Analisar GPU
    gpu_px, gpu_py = simular_gpu_original(private_key)
    
    # Comparar coordenadas X
    print("\n🔍 COMPARAÇÃO DAS COORDENADAS X:")
    print("=" * 60)
    print("CPU (real) vs GPU (simulado):")
    
    for i in range(4):
        cpu_x = cpu_data['x_parts'][i]
        gpu_x = gpu_px[i]
        igual = cpu_x == gpu_x
        
        print(f"X[{i}]:")
        print(f"  CPU: 0x{cpu_x:016x}")
        print(f"  GPU: 0x{gpu_x:016x}")
        print(f"  Igual: {'✅ SIM' if igual else '❌ NÃO'}")
        print()
    
    print("🎯 DIAGNÓSTICO:")
    print("=" * 60)
    print("PROBLEMA IDENTIFICADO:")
    print("❌ A GPU está usando simulação XOR em vez de multiplicação escalar real")
    print("❌ As coordenadas X são completamente diferentes")
    print("❌ Isso resulta em chaves públicas diferentes")
    print("❌ Consequentemente, hash160 diferentes")
    print()
    print("SOLUÇÃO NECESSÁRIA:")
    print("✅ Implementar multiplicação escalar real na GPU")
    print("✅ Usar as mesmas operações matemáticas da curva elíptica")
    print("✅ Garantir que GPU calcule a mesma chave pública que a CPU")

if __name__ == "__main__":
    main()
