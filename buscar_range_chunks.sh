#!/bin/bash

# BUSCA EM CHUNKS - Range Específico
# Divide o range em pedaços menores para melhor controle

echo "🎯 BUSCA EM CHUNKS - RANGE ESPECÍFICO"
echo "====================================="
echo "Carteira alvo: 1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU"
echo "Hash errado:   15dcad75ce214766086340311434d412874c7e77"
echo ""

# Configurações
TARGET_HASH="15dcad75ce214766086340311434d412874c7e77"
START_RANGE=4611686018427387904  # 0x400000000000000000
END_RANGE=9223372036854775807    # 0x7fffffffffffffffff

# Calcular chunks
TOTAL_KEYS=$((END_RANGE - START_RANGE))
CHUNK_SIZE=100000000000  # 100 bilhões por chunk
NUM_CHUNKS=$((TOTAL_KEYS / CHUNK_SIZE + 1))

echo "📊 CONFIGURAÇÃO DOS CHUNKS:"
echo "Range total: $START_RANGE - $END_RANGE"
echo "Total de chaves: $TOTAL_KEYS"
echo "Tamanho do chunk: $CHUNK_SIZE chaves"
echo "Número de chunks: $NUM_CHUNKS"
echo ""

# Verificar programa
if [ ! -f "./busca_cuda" ]; then
    echo "❌ Programa busca_cuda não encontrado!"
    exit 1
fi

echo "⚠️  Esta busca será dividida em $NUM_CHUNKS chunks"
echo "Cada chunk levará aproximadamente 1-2 minutos"
echo "Tempo total estimado: $((NUM_CHUNKS * 2)) minutos"
echo ""

read -p "Continuar? (s/N): " continuar
if [[ ! $continuar =~ ^[Ss]$ ]]; then
    exit 0
fi

echo ""
echo "🚀 INICIANDO BUSCA EM CHUNKS..."
echo "==============================="

# Função para executar um chunk
executar_chunk() {
    local chunk_num=$1
    local chunk_start=$2
    local chunk_end=$3
    
    echo ""
    echo "🔍 CHUNK $chunk_num/$NUM_CHUNKS"
    echo "Range: $chunk_start - $chunk_end"
    echo "Progresso: $(( (chunk_num - 1) * 100 / NUM_CHUNKS ))%"
    
    start_time=$(date +%s)
    
    ./busca_cuda "$TARGET_HASH" "$chunk_start" "$chunk_end"
    result=$?
    
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    if [ $result -eq 0 ]; then
        echo "🎉 CHAVE ENCONTRADA NO CHUNK $chunk_num!"
        return 0
    else
        echo "❌ Chunk $chunk_num concluído em ${duration}s - não encontrada"
        return 1
    fi
}

# Executar chunks sequencialmente
chunk_num=1
current_start=$START_RANGE

while [ $current_start -lt $END_RANGE ]; do
    current_end=$((current_start + CHUNK_SIZE))
    
    # Ajustar último chunk
    if [ $current_end -gt $END_RANGE ]; then
        current_end=$END_RANGE
    fi
    
    # Executar chunk
    if executar_chunk $chunk_num $current_start $current_end; then
        echo ""
        echo "🎉 BUSCA CONCLUÍDA COM SUCESSO!"
        echo "Chave encontrada no chunk $chunk_num"
        exit 0
    fi
    
    # Próximo chunk
    current_start=$((current_end + 1))
    chunk_num=$((chunk_num + 1))
    
    # Pausa entre chunks (opcional)
    sleep 1
done

echo ""
echo "❌ BUSCA CONCLUÍDA - CHAVE NÃO ENCONTRADA"
echo "Todos os $NUM_CHUNKS chunks foram testados"
