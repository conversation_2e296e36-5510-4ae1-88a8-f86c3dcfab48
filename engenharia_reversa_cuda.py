#!/usr/bin/env python3
"""
ENGENHARIA REVERSA CUDA - Descobrir Função Inversa

Este programa faz engenharia reversa das operações CUDA usando os dados
das carteiras conhecidas para descobrir como transformar:
Hash <PERSON>do → Hash Correto

Objetivo: Criar função inversa que funcione para qualquer hash errado.
"""

import numpy as np
from main import calculate_target_hash160

class EngenhariaCUDA:
    def __init__(self):
        # Dados das carteiras conhecidas
        self.carteiras_conhecidas = [
            {
                'chave_privada': 1,
                'endereco': '1BgGZ9tcN4rm9KBzDn7KprQz87SZ26SAMH',
                'hash_correto': '751e76e8199196d454941c45d1b3a323f1433bd6',
                'hash_errado': '36df2f22295784ab7f81989f9247bfd99bb00c03'
            },
            {
                'chave_privada': 2,
                'endereco': '1cMh228HTCiwS8ZsaakH8A8wze1JR5ZsP',
                'hash_correto': '06afd46bcdfd22ef94ac122aa11f241244a37ecc',
                'hash_errado': '5fed51813a4b0353320dbee6fc24a63c5f695181'
            },
            {
                'chave_privada': 3,
                'endereco': '1CUNEBjYrCn2y1SdiUMohaKUi4wpP326Lb',
                'hash_correto': '7dd65592d0ab2fe0d0257d571abf032cd9db93dc',
                'hash_errado': 'b0548c85212204a8a9555adbbdb6dab85b77afa4'
            }
        ]
        
        self.transformacoes_descobertas = []
    
    def analisar_transformacoes_byte_a_byte(self):
        """Analisa as transformações byte por byte para descobrir padrões"""
        print("🔍 ANÁLISE BYTE A BYTE DAS TRANSFORMAÇÕES")
        print("="*80)
        
        for pos in range(20):
            print(f"\n📍 POSIÇÃO {pos}:")
            
            transformacoes_pos = []
            
            for carteira in self.carteiras_conhecidas:
                hash_correto_bytes = bytes.fromhex(carteira['hash_correto'])
                hash_errado_bytes = bytes.fromhex(carteira['hash_errado'])
                chave = carteira['chave_privada']
                
                byte_correto = hash_correto_bytes[pos]
                byte_errado = hash_errado_bytes[pos]
                
                # Calcular transformação
                diferenca = (byte_correto - byte_errado) & 0xFF
                xor_result = byte_correto ^ byte_errado
                
                transformacoes_pos.append({
                    'chave': chave,
                    'byte_correto': byte_correto,
                    'byte_errado': byte_errado,
                    'diferenca': diferenca,
                    'xor': xor_result
                })
                
                print(f"   Chave {chave}: {byte_errado:02x} → {byte_correto:02x} | Diff: {diferenca:3d} | XOR: {xor_result:02x}")
            
            # Procurar padrões
            self.descobrir_padroes_posicao(pos, transformacoes_pos)
    
    def descobrir_padroes_posicao(self, pos, transformacoes):
        """Descobre padrões matemáticos para uma posição específica"""
        print(f"   🔍 Procurando padrões na posição {pos}:")
        
        # Testar diferentes fórmulas matemáticas
        formulas_testadas = []
        
        # Fórmula 1: Transformação linear baseada na chave privada
        for t in transformacoes:
            # Teste: byte_correto = byte_errado + (chave * pos) & 0xFF
            resultado_formula1 = (t['byte_errado'] + (t['chave'] * pos)) & 0xFF
            if resultado_formula1 == t['byte_correto']:
                formulas_testadas.append(f"Chave {t['chave']}: byte_correto = (byte_errado + chave*pos) & 0xFF")
        
        # Fórmula 2: XOR com função da chave e posição
        for t in transformacoes:
            # Teste: byte_correto = byte_errado ^ (chave + pos) & 0xFF
            resultado_formula2 = t['byte_errado'] ^ ((t['chave'] + pos) & 0xFF)
            if resultado_formula2 == t['byte_correto']:
                formulas_testadas.append(f"Chave {t['chave']}: byte_correto = byte_errado ^ (chave+pos)")
        
        # Fórmula 3: Operações baseadas no kernel CUDA
        for t in transformacoes:
            # Simular operações inversas do kernel
            resultado = t['byte_errado']
            
            # Reverter transformação final do kernel: ((x * 0x9E) ^ (x >> 4)) & 0xFF
            # Isso é complexo, vamos tentar aproximação
            for tentativa in range(256):
                if ((tentativa * 0x9E) ^ (tentativa >> 4)) & 0xFF == t['byte_errado']:
                    # Reverter outras operações
                    resultado_kernel = tentativa
                    resultado_kernel ^= ((t['chave'] + pos) * 0x9E3779B9) & 0xFF
                    resultado_kernel ^= (t['chave'] * (pos + 1)) & 0xFF
                    resultado_kernel ^= (t['chave'] >> ((pos + 8) % 16)) & 0xFF
                    resultado_kernel ^= (t['chave'] >> (pos % 8)) & 0xFF
                    
                    if resultado_kernel == t['byte_correto']:
                        formulas_testadas.append(f"Chave {t['chave']}: KERNEL REVERSO funcionou!")
                    break
        
        if formulas_testadas:
            print(f"     ✅ PADRÕES ENCONTRADOS:")
            for formula in formulas_testadas:
                print(f"       {formula}")
        else:
            print(f"     ❌ Nenhum padrão simples encontrado")
        
        return formulas_testadas
    
    def criar_funcao_inversa_universal(self):
        """Cria função inversa universal baseada nos padrões descobertos"""
        print(f"\n🎯 CRIANDO FUNÇÃO INVERSA UNIVERSAL")
        print("="*80)
        
        # Analisar todos os padrões descobertos
        padroes_universais = []
        
        for pos in range(20):
            print(f"\n📍 Analisando posição {pos} para função universal:")
            
            # Testar se existe uma fórmula que funciona para todas as chaves
            for formula_tipo in ['linear', 'xor', 'kernel_reverso']:
                funciona_para_todas = True
                
                for carteira in self.carteiras_conhecidas:
                    hash_correto_bytes = bytes.fromhex(carteira['hash_correto'])
                    hash_errado_bytes = bytes.fromhex(carteira['hash_errado'])
                    chave = carteira['chave_privada']
                    
                    byte_correto_real = hash_correto_bytes[pos]
                    byte_errado = hash_errado_bytes[pos]
                    
                    # Aplicar fórmula
                    if formula_tipo == 'linear':
                        byte_correto_calculado = (byte_errado + (chave * pos)) & 0xFF
                    elif formula_tipo == 'xor':
                        byte_correto_calculado = byte_errado ^ ((chave + pos) & 0xFF)
                    elif formula_tipo == 'kernel_reverso':
                        byte_correto_calculado = self.aplicar_kernel_reverso(byte_errado, chave, pos)
                    
                    if byte_correto_calculado != byte_correto_real:
                        funciona_para_todas = False
                        break
                
                if funciona_para_todas:
                    padroes_universais.append({
                        'posicao': pos,
                        'formula': formula_tipo
                    })
                    print(f"   ✅ Fórmula {formula_tipo} funciona para todas as chaves!")
                    break
            else:
                print(f"   ❌ Nenhuma fórmula universal encontrada para posição {pos}")
        
        return padroes_universais
    
    def aplicar_kernel_reverso(self, byte_errado, chave, pos):
        """Aplica operações inversas do kernel CUDA"""
        # Tentar reverter a transformação final do kernel
        for tentativa in range(256):
            if ((tentativa * 0x9E) ^ (tentativa >> 4)) & 0xFF == byte_errado:
                resultado = tentativa
                
                # Reverter outras operações (ordem inversa)
                resultado ^= ((chave + pos) * 0x9E3779B9) & 0xFF
                resultado ^= (chave * (pos + 1)) & 0xFF
                resultado ^= (chave >> ((pos + 8) % 16)) & 0xFF
                resultado ^= (chave >> (pos % 8)) & 0xFF
                
                return resultado & 0xFF
        
        return byte_errado  # Fallback
    
    def funcao_hash_errado_para_correto(self, hash_errado_hex, chave_privada_estimada=None):
        """
        FUNÇÃO INVERSA: Transforma hash errado em hash correto
        usando os padrões descobertos
        """
        print(f"\n🎯 APLICANDO FUNÇÃO INVERSA")
        print(f"   Hash errado: {hash_errado_hex}")
        
        hash_errado_bytes = bytes.fromhex(hash_errado_hex)
        hash_correto_calculado = bytearray(20)
        
        for pos in range(20):
            byte_errado = hash_errado_bytes[pos]
            
            # Se temos chave privada estimada, usar ela
            if chave_privada_estimada:
                chave = chave_privada_estimada
            else:
                # Tentar estimar chave privada baseada nos padrões
                chave = self.estimar_chave_privada(hash_errado_bytes, pos)
            
            # Aplicar transformação inversa baseada nos padrões descobertos
            byte_correto = self.aplicar_kernel_reverso(byte_errado, chave, pos)
            hash_correto_calculado[pos] = byte_correto
        
        resultado_hex = bytes(hash_correto_calculado).hex()
        print(f"   Hash correto calculado: {resultado_hex}")
        
        return bytes(hash_correto_calculado)
    
    def estimar_chave_privada(self, hash_errado_bytes, pos):
        """Estima a chave privada baseada nos padrões dos dados conhecidos"""
        # Método simples: usar média das chaves conhecidas
        # Em implementação real, seria mais sofisticado
        return 2  # Chave média das conhecidas (1, 2, 3)
    
    def testar_funcao_inversa(self):
        """Testa a função inversa com as carteiras conhecidas"""
        print(f"\n🧪 TESTANDO FUNÇÃO INVERSA COM CARTEIRAS CONHECIDAS")
        print("="*80)
        
        sucessos = 0
        
        for carteira in self.carteiras_conhecidas:
            print(f"\n🔍 Testando carteira - Chave {carteira['chave_privada']}")
            
            hash_correto_real = carteira['hash_correto']
            hash_errado = carteira['hash_errado']
            chave_privada = carteira['chave_privada']
            
            # Aplicar função inversa
            hash_correto_calculado = self.funcao_hash_errado_para_correto(hash_errado, chave_privada)
            hash_correto_calculado_hex = hash_correto_calculado.hex()
            
            print(f"   Hash correto real:      {hash_correto_real}")
            print(f"   Hash correto calculado: {hash_correto_calculado_hex}")
            
            if hash_correto_calculado_hex == hash_correto_real:
                print(f"   ✅ SUCESSO!")
                sucessos += 1
            else:
                print(f"   ❌ FALHOU!")
                # Mostrar diferenças
                for i in range(20):
                    real_byte = int(hash_correto_real[i*2:i*2+2], 16)
                    calc_byte = hash_correto_calculado[i]
                    if real_byte != calc_byte:
                        print(f"     Byte {i}: real {real_byte:02x}, calculado {calc_byte:02x}")
        
        taxa_sucesso = (sucessos / len(self.carteiras_conhecidas)) * 100
        print(f"\n📊 RESULTADO DO TESTE:")
        print(f"   Sucessos: {sucessos}/{len(self.carteiras_conhecidas)}")
        print(f"   Taxa de sucesso: {taxa_sucesso:.1f}%")
        
        return taxa_sucesso >= 100.0

def main():
    """Função principal"""
    print("🔬 ENGENHARIA REVERSA CUDA - DESCOBRIR FUNÇÃO INVERSA")
    print("Objetivo: Hash Errado → Hash Correto")
    print("="*80)
    
    engenharia = EngenhariaCUDA()
    
    # Passo 1: Analisar transformações byte a byte
    engenharia.analisar_transformacoes_byte_a_byte()
    
    # Passo 2: Criar função inversa universal
    padroes = engenharia.criar_funcao_inversa_universal()
    
    # Passo 3: Testar função inversa
    sucesso = engenharia.testar_funcao_inversa()
    
    print(f"\n" + "="*80)
    print(f"📋 CONCLUSÃO")
    print(f"="*80)
    
    if sucesso:
        print(f"🎉 SUCESSO! Função inversa descoberta e validada!")
        print(f"   Agora podemos transformar hash errado → hash correto")
        print(f"   Isso permite descobrir endereços a partir de hashes errados!")
    else:
        print(f"⚠️  Função inversa parcialmente descoberta")
        print(f"   Precisa de mais refinamento para funcionar perfeitamente")
        print(f"   Padrões descobertos: {len(padroes)}")

if __name__ == "__main__":
    main()
