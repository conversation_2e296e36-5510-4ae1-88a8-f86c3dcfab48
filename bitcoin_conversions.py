import hashlib
import base58

# Flags globais para disponibilidade de bibliotecas
ECDSA_AVAILABLE = False
RIPEMD160_AVAILABLE = False

# Tentar importar bibliotecas necessárias
try:
    from ecdsa import Signing<PERSON>ey, Verifying<PERSON><PERSON>, SECP256k1
    ECDSA_AVAILABLE = True
    print("✓ ECDSA disponível")
except ImportError:
    ECDSA_AVAILABLE = False
    print("❌ ECDSA não disponível")

try:
    from Crypto.Hash import RIPEMD160 as CryptoRIPEMD160
    RIPEMD160_AVAILABLE = True
    print("✓ RIPEMD160 disponível")
except ImportError:
    RIPEMD160_AVAILABLE = False
    print("❌ RIPEMD160 não disponível - usando implementação manual")

# Implementação manual do RIPEMD160
def ripemd160_manual(data):
    """Implementação manual do RIPEMD160"""
    def rol(n, b):
        return ((n << b) | (n >> (32 - b))) & 0xffffffff

    def f(j, x, y, z):
        if j < 16:
            return x ^ y ^ z
        elif j < 32:
            return (x & y) | (~x & z)
        elif j < 48:
            return (x | ~y) ^ z
        elif j < 64:
            return (x & z) | (y & ~z)
        else:
            return x ^ (y | ~z)

    def K(j):
        if j < 16:
            return 0x00000000
        elif j < 32:
            return 0x5A827999
        elif j < 48:
            return 0x6ED9EBA1
        elif j < 64:
            return 0x8F1BBCDC
        else:
            return 0xA953FD4E

    def Kh(j):
        if j < 16:
            return 0x50A28BE6
        elif j < 32:
            return 0x5C4DD124
        elif j < 48:
            return 0x6D703EF3
        elif j < 64:
            return 0x7A6D76E9
        else:
            return 0x00000000

    # Padding
    msg = bytearray(data)
    msg_len = len(data)
    msg.append(0x80)

    while len(msg) % 64 != 56:
        msg.append(0x00)

    msg.extend((msg_len * 8).to_bytes(8, 'little'))

    # Initialize hash values
    h0 = 0x67452301
    h1 = 0xEFCDAB89
    h2 = 0x98BADCFE
    h3 = 0x10325476
    h4 = 0xC3D2E1F0

    # Process message in 512-bit chunks
    for chunk_start in range(0, len(msg), 64):
        chunk = msg[chunk_start:chunk_start + 64]
        w = [int.from_bytes(chunk[i:i+4], 'little') for i in range(0, 64, 4)]

        # Initialize hash value for this chunk
        al, bl, cl, dl, el = h0, h1, h2, h3, h4
        ar, br, cr, dr, er = h0, h1, h2, h3, h4

        # Left line
        r = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
             7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8,
             3, 10, 14, 4, 9, 15, 8, 1, 2, 7, 0, 6, 13, 11, 5, 12,
             1, 9, 11, 10, 0, 8, 12, 4, 13, 3, 7, 15, 14, 5, 6, 2,
             4, 0, 5, 9, 7, 12, 2, 10, 14, 1, 3, 8, 11, 6, 15, 13]

        s = [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8,
             7, 6, 8, 13, 11, 9, 7, 15, 7, 12, 15, 9, 11, 7, 13, 12,
             11, 13, 6, 7, 14, 9, 13, 15, 14, 8, 13, 6, 5, 12, 7, 5,
             11, 12, 14, 15, 14, 15, 9, 8, 9, 14, 5, 6, 8, 6, 5, 12,
             9, 15, 5, 11, 6, 8, 13, 12, 5, 12, 13, 14, 11, 8, 5, 6]

        # Right line
        rh = [5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12,
              6, 11, 3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2,
              15, 5, 1, 3, 7, 14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13,
              8, 6, 4, 1, 3, 11, 15, 0, 5, 12, 2, 13, 9, 7, 10, 14,
              12, 15, 10, 4, 1, 5, 8, 7, 6, 2, 13, 14, 0, 3, 9, 11]

        sh = [8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6,
              9, 13, 15, 7, 12, 8, 9, 11, 7, 7, 12, 7, 6, 15, 13, 11,
              9, 7, 15, 11, 8, 6, 6, 14, 12, 13, 5, 14, 13, 13, 7, 5,
              15, 5, 8, 11, 14, 14, 6, 14, 6, 9, 12, 9, 12, 5, 15, 8,
              8, 5, 12, 9, 12, 5, 14, 6, 8, 13, 6, 5, 15, 13, 11, 11]

        for j in range(80):
            # Left line
            t = (al + f(j, bl, cl, dl) + w[r[j]] + K(j)) & 0xffffffff
            t = rol(t, s[j]) + el & 0xffffffff
            al, bl, cl, dl, el = el, t, bl, rol(cl, 10), dl

            # Right line
            t = (ar + f(79-j, br, cr, dr) + w[rh[j]] + Kh(j)) & 0xffffffff
            t = rol(t, sh[j]) + er & 0xffffffff
            ar, br, cr, dr, er = er, t, br, rol(cr, 10), dr

        # Add this chunk's hash to result so far
        t = (h1 + cl + dr) & 0xffffffff
        h1 = (h2 + dl + er) & 0xffffffff
        h2 = (h3 + el + ar) & 0xffffffff
        h3 = (h4 + al + br) & 0xffffffff
        h4 = (h0 + bl + cr) & 0xffffffff
        h0 = t

    # Produce the final hash value
    return b''.join(h.to_bytes(4, 'little') for h in [h0, h1, h2, h3, h4])

def private_key_to_hash160(private_key_int):
    """Converte uma chave privada (inteiro) para hash160 (RIPEMD160(SHA256(chave pública comprimida)))"""
    if not ECDSA_AVAILABLE:
        print("ERRO: Biblioteca ECDSA não disponível")
        return None
    
    try:
        # Converter o inteiro para bytes de 32 bytes (256 bits)
        private_key_bytes = private_key_int.to_bytes(32, byteorder='big')
        
        # Gerar a chave pública a partir da chave privada usando a curva secp256k1
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        # Formato comprimido da chave pública
        # 0x02 + x (se y é par) ou 0x03 + x (se y é ímpar)
        x_coord = vk.pubkey.point.x()
        y_coord = vk.pubkey.point.y()
        prefix = b'\x02' if y_coord % 2 == 0 else b'\x03'
        
        # Converter x para bytes (32 bytes)
        x_bytes = x_coord.to_bytes(32, byteorder='big')
        
        # Chave pública comprimida
        public_key = prefix + x_bytes
        
        # SHA-256 da chave pública
        sha256_hash = hashlib.sha256(public_key).digest()
        
        # RIPEMD-160 do hash SHA-256
        if RIPEMD160_AVAILABLE:
            ripemd160_hash = CryptoRIPEMD160.new(sha256_hash).digest()
        else:
            ripemd160_hash = ripemd160_manual(sha256_hash)
        
        return ripemd160_hash
    except Exception as e:
        print(f"Erro ao converter chave privada para hash160: {e}")
        return None

def private_key_to_public_key(private_key_int):
    """Converte uma chave privada (inteiro) para chave pública em formato hexadecimal"""
    if not ECDSA_AVAILABLE:
        print("ERRO: Biblioteca ECDSA não disponível")
        return None

    try:
        # Converter o inteiro para bytes de 32 bytes (256 bits)
        private_key_bytes = private_key_int.to_bytes(32, byteorder='big')

        # Gerar a chave pública a partir da chave privada usando a curva secp256k1
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        vk = sk.get_verifying_key()

        # Formato comprimido da chave pública
        # 0x02 + x (se y é par) ou 0x03 + x (se y é ímpar)
        x_coord = vk.pubkey.point.x()
        y_coord = vk.pubkey.point.y()
        prefix = b'\x02' if y_coord % 2 == 0 else b'\x03'

        # Converter x para bytes (32 bytes)
        x_bytes = x_coord.to_bytes(32, byteorder='big')

        # Chave pública comprimida
        public_key = prefix + x_bytes

        # Retornar como string hexadecimal
        return public_key.hex()
    except Exception as e:
        print(f"Erro ao converter chave privada para chave pública: {e}")
        return None

def private_key_to_public_key(private_key_int):
    """Converte uma chave privada (inteiro) para chave pública em formato hexadecimal"""
    if not ECDSA_AVAILABLE:
        print("ERRO: Biblioteca ECDSA não disponível")
        return None

    try:
        # Converter o inteiro para bytes de 32 bytes (256 bits)
        private_key_bytes = private_key_int.to_bytes(32, byteorder='big')

        # Gerar a chave pública a partir da chave privada usando a curva secp256k1
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        vk = sk.get_verifying_key()

        # Formato comprimido da chave pública
        x_coord = vk.pubkey.point.x()
        y_coord = vk.pubkey.point.y()
        prefix = b'\x02' if y_coord % 2 == 0 else b'\x03'

        # Converter x para bytes (32 bytes)
        x_bytes = x_coord.to_bytes(32, byteorder='big')

        # Chave pública comprimida
        public_key = prefix + x_bytes

        return public_key.hex()
    except Exception as e:
        print(f"Erro ao converter chave privada para chave pública: {e}")
        return None

def private_key_to_address(private_key_int, compressed=True):
    """Converte uma chave privada (inteiro) para um endereço Bitcoin P2PKH"""
    if not ECDSA_AVAILABLE:
        print("ERRO: Biblioteca ECDSA não disponível")
        return None
    
    try:
        # Converter o inteiro para bytes de 32 bytes (256 bits)
        private_key_bytes = private_key_int.to_bytes(32, byteorder='big')

        # Gerar a chave pública a partir da chave privada usando a curva secp256k1
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        vk = sk.get_verifying_key()

        # Escolher formato da chave pública
        if compressed:
            # Formato comprimido da chave pública
            x_coord = vk.pubkey.point.x()
            y_coord = vk.pubkey.point.y()
            prefix = b'\x02' if y_coord % 2 == 0 else b'\x03'

            # Converter x para bytes (32 bytes)
            x_bytes = x_coord.to_bytes(32, byteorder='big')

            # Chave pública comprimida
            public_key = prefix + x_bytes
        else:
            # Formato não comprimido da chave pública
            x_coord = vk.pubkey.point.x()
            y_coord = vk.pubkey.point.y()

            # Converter coordenadas para bytes (32 bytes cada)
            x_bytes = x_coord.to_bytes(32, byteorder='big')
            y_bytes = y_coord.to_bytes(32, byteorder='big')

            # Chave pública não comprimida (0x04 + x + y)
            public_key = b'\x04' + x_bytes + y_bytes
        
        # SHA-256 da chave pública
        sha256_hash = hashlib.sha256(public_key).digest()
        
        # RIPEMD-160 do hash SHA-256
        if RIPEMD160_AVAILABLE:
            ripemd160_hash = CryptoRIPEMD160.new(sha256_hash).digest()
        else:
            ripemd160_hash = ripemd160_manual(sha256_hash)
        
        # Adicionar o prefixo de versão (0x00 para endereços P2PKH mainnet)
        versioned_hash = b'\x00' + ripemd160_hash
        
        # Calcular o checksum (primeiros 4 bytes do double SHA-256)
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
        
        # Combinar o hash versionado com o checksum
        address_bytes = versioned_hash + checksum
        
        # Codificar em Base58
        address = base58.b58encode(address_bytes).decode('ascii')
        
        return address
    except Exception as e:
        print(f"Erro ao gerar endereço: {e}")
        return None

def private_key_to_wif(private_key_int):
    """Converte uma chave privada (inteiro) para o formato WIF comprimido"""
    try:
        # Converter o inteiro para bytes de 32 bytes (256 bits)
        private_key_bytes = private_key_int.to_bytes(32, byteorder='big')
        
        # Adicionar prefixo de versão (0x80 para Bitcoin mainnet)
        # e sufixo 0x01 para indicar formato comprimido
        versioned_key = b'\x80' + private_key_bytes + b'\x01'
        
        # Calcular o checksum (primeiros 4 bytes do double SHA-256)
        checksum = hashlib.sha256(hashlib.sha256(versioned_key).digest()).digest()[:4]
        
        # Combinar a chave versionada com o checksum
        wif_bytes = versioned_key + checksum
        
        # Codificar em Base58
        wif = base58.b58encode(wif_bytes).decode('ascii')
        
        return wif
    except Exception as e:
        print(f"Erro ao gerar WIF: {e}")
        return None

def calculate_target_hash160(address):
    """Calcula o hash160 de um endereço Bitcoin"""
    try:
        # Decodificar o endereço Base58
        address_bytes = base58.b58decode(address)
        
        # Verificar se o endereço tem o formato correto
        if len(address_bytes) != 25:
            print("Erro: Comprimento do endereço inválido")
            return None
        
        # Verificar o checksum
        versioned_hash = address_bytes[:-4]
        checksum = address_bytes[-4:]
        calculated_checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
        
        if checksum != calculated_checksum:
            print("Erro: Checksum do endereço inválido")
            return None
        
        # Verificar a versão (0x00 para P2PKH mainnet)
        version = address_bytes[0]
        if version != 0:
            print(f"Aviso: Versão do endereço não é P2PKH mainnet (versão = {version})")
        
        # Extrair o hash160 (sem o prefixo de versão)
        hash160 = address_bytes[1:-4]
        
        return hash160
    except Exception as e:
        print(f"Erro ao calcular hash160 do endereço: {e}")
        return None

def test_key_to_address(hex_key):
    """Função para testar a conversão de uma chave privada para endereço Bitcoin"""
    try:
        # Converter a string hexadecimal para um inteiro
        if hex_key.startswith('0x'):
            hex_key = hex_key[2:]
        private_key_int = int(hex_key, 16)
        
        # Gerar o endereço
        address = private_key_to_address(private_key_int)
        
        print(f"Chave privada (hex): 0x{hex_key}")
        print(f"Chave privada (int): {private_key_int}")
        print(f"Endereço Bitcoin: {address}")
        
        return address
    except Exception as e:
        print(f"Erro ao testar chave: {e}")
        return None
