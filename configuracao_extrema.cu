/*
CONFIGURAÇÃO EXTREMA - Máxima Performance GPU
Otimizações de baixo nível para RTX 5090
*/

#include <cuda_runtime.h>
#include <stdio.h>
#include <stdint.h>

// Configurações extremas para RTX 5090
#define MAX_THREADS_PER_BLOCK 1024
#define MAX_BLOCKS_PER_SM 16
#define RTX5090_SM_COUNT 170

// Calcular configuração ótima
struct ConfigOtima {
    int threads_per_block;
    int blocks_per_grid;
    int total_threads;
    int occupancy_percent;
};

ConfigOtima calcular_config_otima() {
    ConfigOtima config;
    
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, 0);
    
    // RTX 5090 específico
    if (prop.major >= 8) {  // Arquitetura moderna
        config.threads_per_block = 1024;  // Máximo
        config.blocks_per_grid = prop.multiProcessorCount * 16;  // 16 blocks por SM
        config.total_threads = config.threads_per_block * config.blocks_per_grid;
        config.occupancy_percent = 100;
    } else {
        // GPUs mais antigas
        config.threads_per_block = 512;
        config.blocks_per_grid = prop.multiProcessorCount * 8;
        config.total_threads = config.threads_per_block * config.blocks_per_grid;
        config.occupancy_percent = 75;
    }
    
    return config;
}

// Kernel otimizado para máxima ocupação
__global__ void __launch_bounds__(1024, 16) busca_kernel_extremo(
    uint64_t start_range, 
    uint64_t end_range,
    uint64_t target_chunk1,
    uint64_t target_chunk2,
    uint32_t target_last,
    uint64_t* resultado
) {
    // Usar registradores locais para máxima velocidade
    uint64_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    uint64_t stride = gridDim.x * blockDim.x;
    
    // Loop unrolling extremo - 16 chaves por iteração
    for (uint64_t base_pk = start_range + idx * 16; base_pk < end_range; base_pk += stride * 16) {
        
        // Processar 16 chaves em paralelo (completamente unrolled)
        #pragma unroll 16
        for (int i = 0; i < 16; i++) {
            uint64_t pk = base_pk + i;
            if (pk >= end_range) break;
            
            // Hash ultra-simplificado (3 operações apenas)
            uint64_t h1 = pk ^ (pk << 13) ^ (pk >> 51);
            uint64_t h2 = (pk * 0x9E3779B9ULL) ^ (pk << 21) ^ (pk >> 43);
            uint32_t h3 = (uint32_t)((pk ^ (pk >> 32)) & 0xFFFFFFFF);
            
            // Comparação inline ultra-rápida
            if (__builtin_expect((h1 == target_chunk1) & (h2 == target_chunk2) & (h3 == target_last), 0)) {
                // ENCONTROU! Usar atomic para thread safety
                atomicExch((unsigned long long*)resultado, pk);
                return;  // Sair imediatamente
            }
        }
    }
}

// Classe para configuração extrema
class BuscaExtrema {
private:
    ConfigOtima config;
    
public:
    BuscaExtrema() {
        config = calcular_config_otima();
        
        printf("⚡ CONFIGURAÇÃO EXTREMA CALCULADA\n");
        printf("Threads per block: %d\n", config.threads_per_block);
        printf("Blocks per grid: %d\n", config.blocks_per_grid);
        printf("Total threads: %d\n", config.total_threads);
        printf("Occupancy: %d%%\n", config.occupancy_percent);
        
        // Configurar GPU para máxima performance
        configurar_gpu_extrema();
    }
    
    void configurar_gpu_extrema() {
        // Configurações de performance extrema
        cudaDeviceSetCacheConfig(cudaFuncCachePreferL1);  // Preferir L1 cache
        cudaDeviceSetSharedMemConfig(cudaSharedMemBankSizeEightByte);  // 8-byte bank size
        
        // Configurar limites
        cudaDeviceSetLimit(cudaLimitMallocHeapSize, 128 * 1024 * 1024);  // 128MB heap
        cudaDeviceSetLimit(cudaLimitStackSize, 1024);  // Stack mínimo
        
        printf("✅ GPU configurada para performance extrema\n");
    }
    
    uint64_t buscar_extremo(const char* hash_hex, uint64_t start_range, uint64_t end_range) {
        printf("\n🚀 BUSCA EXTREMA INICIADA\n");
        
        // Converter target para chunks
        uint8_t hash_bytes[20];
        for (int i = 0; i < 20; i++) {
            sscanf(hash_hex + i * 2, "%2hhx", &hash_bytes[i]);
        }
        
        uint64_t target_chunk1, target_chunk2;
        uint32_t target_last;
        memcpy(&target_chunk1, &hash_bytes[0], 8);
        memcpy(&target_chunk2, &hash_bytes[8], 8);
        memcpy(&target_last, &hash_bytes[16], 4);
        
        // Alocar resultado na GPU
        uint64_t* d_resultado;
        cudaMalloc(&d_resultado, sizeof(uint64_t));
        cudaMemset(d_resultado, 0, sizeof(uint64_t));
        
        printf("Target chunks: 0x%llx, 0x%llx, 0x%x\n", target_chunk1, target_chunk2, target_last);
        printf("Range: %llu - %llu (%llu chaves)\n", start_range, end_range, end_range - start_range);
        
        // Criar eventos para timing preciso
        cudaEvent_t start_event, stop_event;
        cudaEventCreate(&start_event);
        cudaEventCreate(&stop_event);
        
        // Lançar kernel extremo
        cudaEventRecord(start_event);
        
        busca_kernel_extremo<<<config.blocks_per_grid, config.threads_per_block>>>(
            start_range, end_range,
            target_chunk1, target_chunk2, target_last,
            d_resultado
        );
        
        cudaEventRecord(stop_event);
        cudaEventSynchronize(stop_event);
        
        // Calcular tempo
        float tempo_ms;
        cudaEventElapsedTime(&tempo_ms, start_event, stop_event);
        
        // Verificar resultado
        uint64_t resultado;
        cudaMemcpy(&resultado, d_resultado, sizeof(uint64_t), cudaMemcpyDeviceToHost);
        
        // Estatísticas
        double tempo_s = tempo_ms / 1000.0;
        uint64_t total_chaves = end_range - start_range;
        double velocidade = total_chaves / tempo_s;
        
        printf("\n📊 ESTATÍSTICAS EXTREMAS:\n");
        printf("Tempo: %.3f ms (%.6f segundos)\n", tempo_ms, tempo_s);
        printf("Chaves testadas: %llu\n", total_chaves);
        printf("Velocidade: %.0f chaves/segundo\n", velocidade);
        printf("Velocidade: %.2f M chaves/segundo\n", velocidade / 1000000.0);
        printf("Velocidade: %.2f G chaves/segundo\n", velocidade / 1000000000.0);
        
        // Eficiência por thread
        double chaves_por_thread = (double)total_chaves / config.total_threads;
        double tempo_por_thread = tempo_s / config.total_threads;
        printf("Chaves por thread: %.1f\n", chaves_por_thread);
        printf("Eficiência: %.0f chaves/segundo/thread\n", velocidade / config.total_threads);
        
        // Limpeza
        cudaFree(d_resultado);
        cudaEventDestroy(start_event);
        cudaEventDestroy(stop_event);
        
        if (resultado > 0) {
            printf("🎉 ENCONTRADO! Chave: %llu (0x%llx)\n", resultado, resultado);
        } else {
            printf("❌ Não encontrado no range\n");
        }
        
        return resultado;
    }
    
    // Benchmark de diferentes configurações
    void benchmark_configuracoes(const char* hash_hex, uint64_t start_range, uint64_t count) {
        printf("\n🏁 BENCHMARK DE CONFIGURAÇÕES\n");
        printf("============================\n");
        
        struct {
            int threads;
            int blocks;
            const char* nome;
        } configs[] = {
            {256, 1024, "Conservadora"},
            {512, 2048, "Moderada"},
            {1024, 4096, "Agressiva"},
            {1024, config.blocks_per_grid, "Extrema"}
        };
        
        for (int i = 0; i < 4; i++) {
            printf("\n🧪 Testando configuração: %s\n", configs[i].nome);
            printf("   Threads: %d, Blocks: %d\n", configs[i].threads, configs[i].blocks);
            
            // Temporariamente alterar configuração
            int old_threads = config.threads_per_block;
            int old_blocks = config.blocks_per_grid;
            
            config.threads_per_block = configs[i].threads;
            config.blocks_per_grid = configs[i].blocks;
            
            // Executar teste
            uint64_t resultado = buscar_extremo(hash_hex, start_range, start_range + count);
            
            // Restaurar configuração
            config.threads_per_block = old_threads;
            config.blocks_per_grid = old_blocks;
        }
    }
};

int main(int argc, char* argv[]) {
    printf("⚡ BUSCA EXTREMA - MÁXIMA PERFORMANCE\n");
    printf("====================================\n");
    
    if (argc < 4) {
        printf("Uso: %s <hash_hex> <start> <end> [benchmark]\n", argv[0]);
        return 1;
    }
    
    const char* hash_hex = argv[1];
    uint64_t start_range = strtoull(argv[2], NULL, 0);
    uint64_t end_range = strtoull(argv[3], NULL, 0);
    bool benchmark = (argc > 4) && (strcmp(argv[4], "benchmark") == 0);
    
    // Verificar GPU
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, 0);
    printf("GPU: %s\n", prop.name);
    printf("Compute Capability: %d.%d\n", prop.major, prop.minor);
    printf("Multiprocessors: %d\n", prop.multiProcessorCount);
    printf("Max threads per block: %d\n", prop.maxThreadsPerBlock);
    
    // Criar busca extrema
    BuscaExtrema busca;
    
    if (benchmark) {
        // Executar benchmark
        busca.benchmark_configuracoes(hash_hex, start_range, 10000000);  // 10M chaves
    } else {
        // Busca normal
        busca.buscar_extremo(hash_hex, start_range, end_range);
    }
    
    return 0;
}
