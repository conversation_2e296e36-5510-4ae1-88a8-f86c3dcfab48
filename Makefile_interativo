# Makefile para Programa Interativo

NVCC = nvcc
TARGET = descobrir_interativo
SOURCE = descobrir_interativo.cu

# Detectar arquitetura automaticamente
GPU_ARCH_RAW := $(shell nvidia-smi --query-gpu=compute_cap --format=csv,noheader,nounits 2>/dev/null | head -1)
GPU_ARCH_MAJOR := $(shell echo $(GPU_ARCH_RAW) | cut -d'.' -f1)

# Mapear arquiteturas
ifeq ($(GPU_ARCH_MAJOR),12)
    GPU_ARCH = 89  # RTX 5090
else ifeq ($(GPU_ARCH_MAJOR),8)
    GPU_ARCH = 86  # RTX 30xx/40xx
else ifeq ($(GPU_ARCH_MAJOR),7)
    GPU_ARCH = 75  # RTX 20xx
else
    GPU_ARCH = 75  # Default
endif

# Flags otimizadas
NVCC_FLAGS = -O3 -arch=sm_$(GPU_ARCH) -std=c++11 --use_fast_math

all: $(TARGET)

$(TARGET): $(SOURCE)
	@echo "🔧 Compilando programa interativo..."
	@echo "GPU Arch detectada: sm_$(GPU_ARCH) (Compute $(GPU_ARCH_RAW))"
	$(NVCC) $(NVCC_FLAGS) -o $(TARGET) $(SOURCE)
	@echo "✅ Compilação concluída!"

# Compilação automática com fallback
auto: 
	@echo "🔧 Compilação automática com fallback..."
	$(NVCC) -O3 -arch=sm_89 -std=c++11 --use_fast_math -o $(TARGET) $(SOURCE) 2>/dev/null || \
	$(NVCC) -O3 -arch=sm_86 -std=c++11 --use_fast_math -o $(TARGET) $(SOURCE) 2>/dev/null || \
	$(NVCC) -O3 -arch=sm_75 -std=c++11 --use_fast_math -o $(TARGET) $(SOURCE) 2>/dev/null || \
	$(NVCC) -O3 -std=c++11 -o $(TARGET) $(SOURCE)
	@echo "✅ Compilação automática concluída!"

# Teste interativo
test: $(TARGET)
	@echo "🧪 TESTE INTERATIVO"
	@echo "=================="
	@echo "Testando com endereço conhecido..."
	echo -e "1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2\n1:1000000" | ./$(TARGET)

# Teste com argumentos
test-args: $(TARGET)
	@echo "🧪 TESTE COM ARGUMENTOS"
	@echo "======================"
	./$(TARGET) 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2 1:1000000

# Teste range hex
test-hex: $(TARGET)
	@echo "🧪 TESTE RANGE HEX"
	@echo "=================="
	./$(TARGET) 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2 1000000:1ffffff

# Teste carteira alvo
test-target: $(TARGET)
	@echo "🧪 TESTE CARTEIRA ALVO"
	@echo "======================"
	./$(TARGET) 1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU 400000000000000000:400000000001000000

clean:
	rm -f $(TARGET)

info:
	@echo "ℹ️  Informações do sistema:"
	@echo "NVCC: $(shell nvcc --version | grep release)"
	@echo "GPU: $(shell nvidia-smi --query-gpu=name --format=csv,noheader)"
	@echo "Compute Cap: $(GPU_ARCH_RAW)"
	@echo "Arquitetura: sm_$(GPU_ARCH)"

help:
	@echo "🔍 DESCOBRIR HASH ERRADO - PROGRAMA INTERATIVO"
	@echo "=============================================="
	@echo ""
	@echo "📋 COMANDOS:"
	@echo "make all         - Compilar programa"
	@echo "make auto        - Compilação automática"
	@echo "make test        - Teste interativo"
	@echo "make test-args   - Teste com argumentos"
	@echo "make test-hex    - Teste range hexadecimal"
	@echo "make test-target - Teste carteira alvo"
	@echo "make clean       - Limpar"
	@echo "make info        - Informações do sistema"
	@echo ""
	@echo "🚀 USO INTERATIVO:"
	@echo "./descobrir_interativo"
	@echo "  Digite endereço: 1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU"
	@echo "  Digite range: 1000000:1ffffff"
	@echo ""
	@echo "🚀 USO COM ARGUMENTOS:"
	@echo "./descobrir_interativo <endereco> <range>"
	@echo ""
	@echo "📝 EXEMPLOS DE RANGES:"
	@echo "  1:1000000                    # Decimal simples"
	@echo "  1000000:1ffffff              # Hex sem 0x"
	@echo "  0x1000000:0x1ffffff          # Hex com 0x"
	@echo "  400000000000000000:7fffffffffffffffff  # Range alvo"
	@echo ""
	@echo "🎯 ENDEREÇOS CONHECIDOS:"
	@echo "  1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2  # Chave privada 1"
	@echo "  1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU  # Carteira alvo"
	@echo "  1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa  # Genesis block"

.PHONY: all auto test test-args test-hex test-target clean info help
