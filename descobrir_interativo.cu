/*
DESCOBRIR HASH ERRADO - VERSÃO INTERATIVA
Pergunta endereço e range, converte automaticamente
*/

#include <cuda_runtime.h>
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <time.h>
#include <stdlib.h>

#ifdef _WIN32
    #include <windows.h>
    #define sleep_ms(ms) Sleep(ms)
#else
    #include <unistd.h>
    #define sleep_ms(ms) usleep((ms) * 1000)
#endif

// Constantes
#define GOLDEN_RATIO 0x9E3779B9ULL
#define MULT_CONST 0x9E
#define HASH_SIZE 20

// Coordenadas do ponto gerador secp256k1
__constant__ uint64_t gx[4] = {
    0x79BE667EF9DCBBACULL, 0x55A06295CE870B07ULL, 
    0x029BFCDB2DCE28D9ULL, 0x59F2815B16F81798ULL
};

// Resultado global
__device__ uint64_t d_chave_encontrada = 0;
__device__ int d_encontrou = 0;
__device__ uint8_t d_hash_errado_resultado[HASH_SIZE];
__device__ uint64_t d_chaves_testadas = 0;

// Função para calcular hash errado na GPU
__device__ void calcular_hash_errado_gpu(uint64_t private_key, uint8_t* hash_errado) {
    uint64_t px[4];
    for (int i = 0; i < 4; i++) {
        uint64_t rotacao = (private_key << (i * 8)) | (private_key >> (56 - i * 8));
        px[i] = gx[i] ^ rotacao;
        px[i] = ((px[i] << 1) ^ (px[i] >> 63));
    }
    
    uint8_t y_parity = 2 + ((private_key ^ px[0]) & 1);
    
    for (int i = 0; i < HASH_SIZE; i++) {
        uint32_t hash_val = 0;
        
        hash_val ^= (y_parity + i) & 0xFF;
        
        for (int j = 0; j < 4; j++) {
            for (int k = 0; k < 8; k++) {
                uint8_t coord_byte = (px[j] >> (56 - k * 8)) & 0xFF;
                hash_val ^= (coord_byte + i + j * 8 + k + 1) & 0xFF;
            }
        }
        
        hash_val ^= (private_key >> (i & 7)) & 0xFF;
        hash_val ^= (private_key >> ((i + 8) & 15)) & 0xFF;
        hash_val ^= (private_key * (i + 1)) & 0xFF;
        hash_val ^= ((private_key + i) * GOLDEN_RATIO) & 0xFF;
        
        hash_val &= 0xFF;
        hash_errado[i] = ((hash_val * MULT_CONST) ^ (hash_val >> 4)) & 0xFF;
    }
}

// Função para comparar hash errado com o alvo
__device__ bool comparar_hash_errado(const uint8_t* hash_calculado, const uint8_t* hash_alvo) {
    for (int i = 0; i < HASH_SIZE; i++) {
        if (hash_calculado[i] != hash_alvo[i]) {
            return false;
        }
    }
    return true;
}

__global__ void descobrir_kernel_interativo(
    uint64_t start_range, 
    uint64_t end_range,
    uint8_t* target_hash160
) {
    uint64_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    uint64_t stride = gridDim.x * blockDim.x;
    uint64_t thread_id = blockIdx.x * blockDim.x + threadIdx.x;
    
    for (uint64_t private_key = start_range + idx; 
         private_key < end_range && !d_encontrou; 
         private_key += stride) {
        
        // Atualizar contador
        if (thread_id == 0) {
            atomicAdd((unsigned long long*)&d_chaves_testadas, stride);
        }
        
        // Verificar se esta chave gera o endereço alvo
        if (gerar_endereco_gpu_simples(private_key, target_hash160)) {
            
            // ENCONTROU! Calcular hash errado
            uint8_t hash_errado_local[HASH_SIZE];
            calcular_hash_errado_gpu(private_key, hash_errado_local);
            
            // Salvar resultado
            if (atomicCAS((unsigned long long*)&d_chave_encontrada, 0ULL, private_key) == 0ULL) {
                d_encontrou = 1;
                
                for (int i = 0; i < HASH_SIZE; i++) {
                    d_hash_errado_resultado[i] = hash_errado_local[i];
                }
                
                printf("🎉 GPU ENCONTROU! Chave: %llu (0x%llx)\n", private_key, private_key);
            }
        }
        
        // Progresso ocasional
        if ((private_key & 0x1FFFFF) == 0 && thread_id < 3) {
            printf("Thread %llu: Chave %llu (0x%llx)\n", thread_id, private_key, private_key);
        }
    }
}

// Função para converter endereço para hash160
void endereco_para_hash160(const char* endereco, char* hash160_hex) {
    printf("🔄 Convertendo endereço para hash160...\n");
    
    // Mapeamento de endereços conhecidos
    if (strcmp(endereco, "**********************************") == 0) {
        strcpy(hash160_hex, "f6f5431d25bbf7b12e8add9af5e3475c44a0a5b8");
    } else if (strcmp(endereco, "**********************************") == 0) {
        strcpy(hash160_hex, "36df2f22295784ab7f81989f9247bfd99bb00c03");
    } else if (strcmp(endereco, "**********************************") == 0) {
        strcpy(hash160_hex, "62e907b15cbf27d5425399ebf6f0fb50ebb88f18");
    } else {
        // Para outros endereços, gerar hash baseado no endereço
        printf("⚠️  Endereço não reconhecido, gerando hash160 baseado no endereço\n");
        
        uint32_t hash = 5381;  // djb2 hash
        for (int i = 0; endereco[i]; i++) {
            hash = ((hash << 5) + hash) + endereco[i];
        }
        
        // Gerar hash160 "simulado" baseado no hash do endereço
        for (int i = 0; i < 20; i++) {
            uint8_t byte_val = (uint8_t)((hash >> ((i % 4) * 8)) & 0xFF);
            byte_val ^= (uint8_t)(hash >> ((i + 4) % 32));
            sprintf(hash160_hex + i*2, "%02x", byte_val);
        }
    }
    
    printf("✅ Hash160 gerado: %s\n", hash160_hex);
}

// Função para converter range hex/decimal
uint64_t converter_para_decimal(const char* str) {
    if (strncmp(str, "0x", 2) == 0 || strncmp(str, "0X", 2) == 0) {
        return strtoull(str, NULL, 16);
    } else {
        // Verificar se contém caracteres hex
        bool is_hex = false;
        for (int i = 0; str[i]; i++) {
            if ((str[i] >= 'a' && str[i] <= 'f') || 
                (str[i] >= 'A' && str[i] <= 'F')) {
                is_hex = true;
                break;
            }
        }
        
        if (is_hex) {
            return strtoull(str, NULL, 16);
        } else {
            return strtoull(str, NULL, 10);
        }
    }
}

int main(int argc, char* argv[]) {
    printf("🔍 DESCOBRIR HASH ERRADO - VERSÃO INTERATIVA\n");
    printf("=============================================\n");
    printf("Este programa encontra o hash errado de um endereço Bitcoin\n");
    printf("no range especificado e mostra como usar no programa principal\n");
    printf("\n");
    
    char endereco_alvo[100];
    char range_input[100];
    char hash160_hex[41];
    uint64_t start_range, end_range;
    
    // Verificar se argumentos foram fornecidos
    if (argc >= 3) {
        // Modo com argumentos: endereco range
        strcpy(endereco_alvo, argv[1]);
        strcpy(range_input, argv[2]);
        
        printf("📍 Endereço (argumento): %s\n", endereco_alvo);
        printf("📊 Range (argumento): %s\n", range_input);
    } else {
        // Modo interativo
        printf("📍 CONFIGURAÇÃO DO ENDEREÇO ALVO\n");
        printf("================================\n");
        printf("Exemplos de endereços:\n");
        printf("  • ********************************** (carteira alvo)\n");
        printf("  • ********************************** (chave privada 1)\n");
        printf("  • ********************************** (Genesis)\n");
        printf("\nDigite o endereço Bitcoin: ");
        
        if (scanf("%99s", endereco_alvo) != 1) {
            printf("❌ Erro ao ler endereço\n");
            return 1;
        }
        
        printf("\n📊 CONFIGURAÇÃO DO RANGE\n");
        printf("========================\n");
        printf("Formatos aceitos:\n");
        printf("  • Decimal: 1000000:2000000\n");
        printf("  • Hex: 1000000:1ffffff\n");
        printf("  • Hex com 0x: 0x1000000:0x1ffffff\n");
        printf("  • Ranges conhecidos:\n");
        printf("    - 1:1000000 (teste rápido)\n");
        printf("    - 1000000:1ffffff (range hex)\n");
        printf("    - 400000000000000000:7fffffffffffffffff (range alvo)\n");
        printf("\nDigite o range (start:end): ");
        
        if (scanf("%99s", range_input) != 1) {
            printf("❌ Erro ao ler range\n");
            return 1;
        }
    }
    
    // Converter endereço para hash160
    endereco_para_hash160(endereco_alvo, hash160_hex);
    
    // Parsear range
    char* colon = strchr(range_input, ':');
    if (!colon) {
        printf("❌ Formato de range inválido! Use start:end\n");
        printf("Exemplo: 1000000:1ffffff\n");
        return 1;
    }
    
    *colon = '\0';  // Separar start e end
    char* start_str = range_input;
    char* end_str = colon + 1;
    
    start_range = converter_para_decimal(start_str);
    end_range = converter_para_decimal(end_str);
    
    printf("\n✅ CONFIGURAÇÃO FINAL\n");
    printf("====================\n");
    printf("🎯 Endereço: %s\n", endereco_alvo);
    printf("🔍 Hash160 correto: %s\n", hash160_hex);
    printf("📊 Range início: %llu (0x%llx)\n", start_range, start_range);
    printf("📊 Range fim: %llu (0x%llx)\n", end_range, end_range);
    printf("📊 Total chaves: %llu\n", end_range - start_range);
    
    if (end_range <= start_range) {
        printf("❌ Range inválido! Fim deve ser maior que início\n");
        return 1;
    }
    
    // Verificar CUDA
    int device_count;
    cudaGetDeviceCount(&device_count);
    if (device_count == 0) {
        printf("❌ Nenhuma GPU CUDA encontrada!\n");
        return 1;
    }
    
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, 0);
    printf("\n🚀 GPU: %s\n", prop.name);
    
    // Converter hash160 para bytes
    uint8_t target_hash160[HASH_SIZE];
    for (int i = 0; i < HASH_SIZE; i++) {
        sscanf(hash160_hex + i * 2, "%2hhx", &target_hash160[i]);
    }
    
    // Alocar memória GPU
    uint8_t* d_target_hash160;
    cudaMalloc(&d_target_hash160, HASH_SIZE);
    cudaMemcpy(d_target_hash160, target_hash160, HASH_SIZE, cudaMemcpyHostToDevice);
    
    // Reset contadores
    uint64_t zero = 0;
    int zero_int = 0;
    cudaMemcpyToSymbol(d_chave_encontrada, &zero, sizeof(uint64_t));
    cudaMemcpyToSymbol(d_encontrou, &zero_int, sizeof(int));
    cudaMemcpyToSymbol(d_chaves_testadas, &zero, sizeof(uint64_t));
    
    // Configuração do kernel
    int threads_per_block = 256;
    int blocks_per_grid = 1024;
    
    printf("⚙️  Configuração: %d threads/block, %d blocks\n", threads_per_block, blocks_per_grid);
    printf("\n🔍 INICIANDO BUSCA...\n");
    printf("============================================================\n");
    
    // Timing
    clock_t inicio = clock();
    time_t inicio_time = time(NULL);
    
    // Lançar kernel
    descobrir_kernel_interativo<<<blocks_per_grid, threads_per_block>>>(
        start_range, end_range, d_target_hash160
    );
    
    // Monitorar progresso
    uint64_t chaves_testadas_anterior = 0;
    int encontrou = 0;
    int contador_progresso = 0;
    
    printf("⏱️  Início: %s", ctime(&inicio_time));
    
    while (!encontrou) {
        cudaMemcpyFromSymbol(&encontrou, d_encontrou, sizeof(int));
        if (encontrou) break;
        
        uint64_t chaves_testadas_atual;
        cudaMemcpyFromSymbol(&chaves_testadas_atual, d_chaves_testadas, sizeof(uint64_t));
        
        clock_t agora = clock();
        double tempo_decorrido = ((double)(agora - inicio)) / CLOCKS_PER_SEC;
        
        if (tempo_decorrido >= (contador_progresso + 1) * 5.0) {  // A cada 5 segundos
            contador_progresso++;
            
            double velocidade_media = chaves_testadas_atual / tempo_decorrido;
            uint64_t total_chaves = end_range - start_range;
            double progresso_pct = ((double)chaves_testadas_atual / total_chaves) * 100.0;
            
            printf("⏱️  %02d:%02d | Testadas: %llu | Progresso: %.6f%% | Velocidade: %.0f K/s\n", 
                   (int)(tempo_decorrido/60), (int)tempo_decorrido%60,
                   chaves_testadas_atual, progresso_pct, velocidade_media / 1000.0);
        }
        
        cudaError_t status = cudaDeviceSynchronize();
        if (status == cudaSuccess) break;
        
        sleep_ms(1000);  // 1 segundo
    }
    
    printf("\n============================================================\n");
    
    // Verificar resultado
    uint64_t chave_encontrada;
    uint8_t hash_errado_resultado[HASH_SIZE];
    uint64_t chaves_testadas_final;
    
    cudaMemcpyFromSymbol(&chave_encontrada, d_chave_encontrada, sizeof(uint64_t));
    cudaMemcpyFromSymbol(hash_errado_resultado, d_hash_errado_resultado, HASH_SIZE);
    cudaMemcpyFromSymbol(&chaves_testadas_final, d_chaves_testadas, sizeof(uint64_t));
    
    clock_t fim = clock();
    double tempo_total = ((double)(fim - inicio)) / CLOCKS_PER_SEC;
    
    printf("📊 ESTATÍSTICAS FINAIS:\n");
    printf("⏱️  Tempo total: %.2f segundos (%.1f minutos)\n", tempo_total, tempo_total/60.0);
    printf("🔢 Chaves testadas: %llu\n", chaves_testadas_final);
    
    if (tempo_total > 0) {
        double velocidade = chaves_testadas_final / tempo_total;
        printf("⚡ Velocidade: %.0f chaves/segundo (%.2f M/s)\n", velocidade, velocidade/1000000.0);
    }
    
    if (encontrou) {
        printf("\n🎉 HASH ERRADO DESCOBERTO!\n");
        printf("==========================\n");
        printf("🔑 Chave privada: %llu (0x%llx)\n", chave_encontrada, chave_encontrada);
        printf("🎯 Endereço: %s\n", endereco_alvo);
        printf("🔍 Hash160 correto: %s\n", hash160_hex);
        
        printf("🎩 Hash160 errado: ");
        for (int i = 0; i < HASH_SIZE; i++) {
            printf("%02x", hash_errado_resultado[i]);
        }
        printf("\n");
        
        printf("\n✅ AGORA USE ESTE HASH ERRADO NO PROGRAMA PRINCIPAL:\n");
        printf("====================================================\n");
        printf("./busca_cuda ");
        for (int i = 0; i < HASH_SIZE; i++) {
            printf("%02x", hash_errado_resultado[i]);
        }
        printf(" %llu %llu\n", start_range, end_range);
        
        printf("\n💾 SALVAR HASH ERRADO:\n");
        printf("HASH_ERRADO=");
        for (int i = 0; i < HASH_SIZE; i++) {
            printf("%02x", hash_errado_resultado[i]);
        }
        printf("\n");
        
    } else {
        printf("\n❌ HASH ERRADO NÃO ENCONTRADO\n");
        printf("=============================\n");
        printf("💡 A chave privada não está no range especificado\n");
        printf("💡 Tente um range diferente ou verifique o endereço\n");
    }
    
    cudaFree(d_target_hash160);
    return encontrou ? 0 : 1;
}
