#!/usr/bin/env python3
"""
CONFIGURAR TELEGRAM - Setup do Bot para Notificações
"""

import requests
import json
import time

def criar_bot_telegram():
    """Guia para criar bot no Telegram"""
    print("🤖 COMO CRIAR UM BOT NO TELEGRAM")
    print("="*40)
    print("1. Abra o Telegram e procure por @BotFather")
    print("2. Envie o comando: /newbot")
    print("3. Escolha um nome para seu bot (ex: Bitcoin Finder Bot)")
    print("4. Escolha um username (ex: bitcoin_finder_123_bot)")
    print("5. O BotFather vai te dar um TOKEN")
    print("6. Copie e cole o token aqui")
    print()

def obter_chat_id():
    """Guia para obter Chat ID"""
    print("💬 COMO OBTER SEU CHAT ID")
    print("="*30)
    print("1. Envie uma mensagem para seu bot")
    print("2. Acesse: https://api.telegram.org/bot<TOKEN>/getUpdates")
    print("3. Procure por 'chat':{'id': NUMERO}")
    print("4. Esse NUMERO é seu chat_id")
    print()

def testar_bot(bot_token, chat_id):
    """Testa se o bot funciona"""
    try:
        # Testar bot
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        response = requests.get(url, timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Token inválido: {response.status_code}")
            return False
        
        bot_info = response.json()
        print(f"✅ Bot válido: {bot_info['result']['first_name']}")
        
        # Testar envio de mensagem
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        mensagem = f"🤖 Teste do Bitcoin Finder Bot\n⏰ {time.strftime('%Y-%m-%d %H:%M:%S')}\n✅ Configuração funcionando!"
        
        payload = {
            'chat_id': chat_id,
            'text': mensagem
        }
        
        response = requests.post(url, json=payload, timeout=10)
        
        if response.status_code == 200:
            print(f"✅ Mensagem de teste enviada com sucesso!")
            return True
        else:
            print(f"❌ Erro ao enviar mensagem: {response.status_code}")
            print(f"   Resposta: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def salvar_configuracao(bot_token, chat_id):
    """Salva configuração em arquivo"""
    config = {
        'bot_token': bot_token,
        'chat_id': chat_id,
        'configurado_em': time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    try:
        with open('telegram_config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"💾 Configuração salva em: telegram_config.json")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao salvar: {e}")
        return False

def carregar_configuracao():
    """Carrega configuração existente"""
    try:
        with open('telegram_config.json', 'r') as f:
            config = json.load(f)
        
        print(f"📱 Configuração encontrada:")
        print(f"   Bot Token: {config['bot_token'][:10]}...")
        print(f"   Chat ID: {config['chat_id']}")
        print(f"   Configurado em: {config.get('configurado_em', 'N/A')}")
        
        return config
        
    except FileNotFoundError:
        print("📱 Nenhuma configuração encontrada")
        return None
    except Exception as e:
        print(f"❌ Erro ao carregar configuração: {e}")
        return None

def main():
    """Função principal"""
    print("📱 CONFIGURADOR DO TELEGRAM - BITCOIN FINDER")
    print("="*50)
    
    # Verificar se já existe configuração
    config_existente = carregar_configuracao()
    
    if config_existente:
        print()
        usar_existente = input("Usar configuração existente? (S/n): ").lower()
        
        if usar_existente != 'n':
            # Testar configuração existente
            if testar_bot(config_existente['bot_token'], config_existente['chat_id']):
                print("✅ Configuração existente está funcionando!")
                return config_existente
            else:
                print("❌ Configuração existente não funciona, criando nova...")
    
    print()
    print("🔧 CONFIGURANDO NOVO BOT")
    print("="*30)
    
    # Guias
    criar_bot_telegram()
    
    # Obter token
    bot_token = input("Cole o TOKEN do bot: ").strip()
    
    if not bot_token:
        print("❌ Token não fornecido")
        return None
    
    print()
    obter_chat_id()
    
    # Obter chat_id
    chat_id = input("Digite seu CHAT_ID: ").strip()
    
    if not chat_id:
        print("❌ Chat ID não fornecido")
        return None
    
    print()
    print("🧪 TESTANDO CONFIGURAÇÃO...")
    
    # Testar
    if testar_bot(bot_token, chat_id):
        print("✅ Configuração válida!")
        
        # Salvar
        if salvar_configuracao(bot_token, chat_id):
            print("✅ Configuração salva com sucesso!")
            
            config = {
                'bot_token': bot_token,
                'chat_id': chat_id
            }
            
            return config
    
    print("❌ Configuração falhou")
    return None

if __name__ == "__main__":
    config = main()
    
    if config:
        print()
        print("🎉 TELEGRAM CONFIGURADO COM SUCESSO!")
        print("="*40)
        print("Agora você pode usar:")
        print("• python3 descobrir_completo.py")
        print("• ./descobrir_cuda_completo")
        print()
        print("E receberá notificações no Telegram quando encontrar chaves!")
    else:
        print()
        print("❌ Configuração não concluída")
        print("Você ainda pode usar os programas, mas sem notificações Telegram")
