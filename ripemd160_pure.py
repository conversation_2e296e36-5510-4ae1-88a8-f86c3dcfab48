# Implementação pura de RIPEMD-160 para Python
# Fornece uma alternativa à biblioteca pycryptodome caso não esteja disponível

# Constantes para RIPEMD-160
_K1 = [0x00000000, 0x5A827999, 0x6ED9EBA1, 0x8F1BBCDC, 0xA953FD4E]
_K2 = [0x50A28BE6, 0x5C4DD124, 0x6D703EF3, 0x7A6D76E9, 0x00000000]

_r1 = [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
    7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8,
    3, 10, 14, 4, 9, 15, 8, 1, 2, 7, 0, 6, 13, 11, 5, 12,
    1, 9, 11, 10, 0, 8, 12, 4, 13, 3, 7, 15, 14, 5, 6, 2,
    4, 0, 5, 9, 7, 12, 2, 10, 14, 1, 3, 8, 11, 6, 15, 13
]

_r2 = [
    5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12,
    6, 11, 3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2,
    15, 5, 1, 3, 7, 14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13,
    8, 6, 4, 1, 3, 11, 15, 0, 5, 12, 2, 13, 9, 7, 10, 14,
    12, 15, 10, 4, 1, 5, 8, 7, 6, 2, 13, 14, 0, 3, 9, 11
]

_s1 = [
    11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8,
    7, 6, 8, 13, 11, 9, 7, 15, 7, 12, 15, 9, 11, 7, 13, 12,
    11, 13, 6, 7, 14, 9, 13, 15, 14, 8, 13, 6, 5, 12, 7, 5,
    11, 12, 14, 15, 14, 15, 9, 8, 9, 14, 5, 6, 8, 6, 5, 12,
    9, 15, 5, 11, 6, 8, 13, 12, 5, 12, 13, 14, 11, 8, 5, 6
]

_s2 = [
    8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6,
    9, 13, 15, 7, 12, 8, 9, 11, 7, 7, 12, 7, 6, 15, 13, 11,
    9, 7, 15, 11, 8, 6, 6, 14, 12, 13, 5, 14, 13, 13, 7, 5,
    15, 5, 8, 11, 14, 14, 6, 14, 6, 9, 12, 9, 12, 5, 15, 8,
    8, 5, 12, 9, 12, 5, 14, 6, 8, 13, 6, 5, 15, 13, 11, 11
]

# Funções auxiliares
def _rotl(x, n):
    """Rotação à esquerda de x por n bits."""
    return ((x << n) | (x >> (32 - n))) & 0xffffffff

def _f1(x, y, z):
    return x ^ y ^ z

def _f2(x, y, z):
    return (x & y) | (~x & z)

def _f3(x, y, z):
    return (x | ~y) ^ z

def _f4(x, y, z):
    return (x & z) | (y & ~z)

def _f5(x, y, z):
    return x ^ (y | ~z)

# Lista de funções f para as diferentes rodadas
_functions = [
    [_f1, _f2, _f3, _f4, _f5],
    [_f5, _f4, _f3, _f2, _f1]
]

def ripemd160(data):
    """Calcula o hash RIPEMD-160 dos dados."""
    if not isinstance(data, bytes):
        data = str(data).encode('utf-8')
        
    # Inicialização do estado
    h = [0x67452301, 0xEFCDAB89, 0x98BADCFE, 0x10325476, 0xC3D2E1F0]
    
    # Preparação dos dados (padding)
    length = len(data)
    data_list = list(data)
    
    # Adiciona o bit 1 no final
    data_list.append(0x80)
    
    # Preenche com zeros até um múltiplo de 64 bytes menos 8 bytes (para o tamanho)
    while (len(data_list) % 64) != 56:
        data_list.append(0)
    
    # Converte de volta para bytes
    padded_data = bytes(data_list)
    
    # Adiciona o tamanho original em bits (little-endian, 64 bits)
    bit_length = (length * 8) & 0xffffffffffffffff
    padded_data += bit_length.to_bytes(8, byteorder='little')
    
    # Processa cada bloco de 64 bytes
    for i in range(0, len(padded_data), 64):
        block = padded_data[i:i+64]
        process_block(h, block)
    
    # Converte o estado final para bytes em little-endian
    result = bytearray(20)
    for i in range(5):
        result[i*4:i*4+4] = h[i].to_bytes(4, byteorder='little')
    
    return bytes(result)

def process_block(h, block):
    """Processa um bloco de 64 bytes usando o algoritmo RIPEMD-160."""
    # Converte o bloco em 16 palavras de 32 bits (little-endian)
    x = []
    for i in range(16):
        x.append(int.from_bytes(block[i*4:i*4+4], byteorder='little'))
    
    # Cópia do estado atual
    a, b, c, d, e = h
    aa, bb, cc, dd, ee = h
    
    # 80 rounds
    for j in range(80):
        # Determina a rodada (0-15, 16-31, 32-47, 48-63, 64-79)
        round_idx = j // 16
        
        # Primeiro caminho
        t = (_rotl(a + _functions[0][round_idx](b, c, d) + x[_r1[j]] + _K1[round_idx], _s1[j]) + e) & 0xffffffff
        a, b, c, d, e = e, t, _rotl(b, 10), c, d
        
        # Segundo caminho
        t = (_rotl(aa + _functions[1][round_idx](bb, cc, dd) + x[_r2[j]] + _K2[round_idx], _s2[j]) + ee) & 0xffffffff
        aa, bb, cc, dd, ee = ee, t, _rotl(bb, 10), cc, dd
    
    # Combinação dos resultados
    t = (h[1] + c + dd) & 0xffffffff
    h[1] = (h[2] + d + ee) & 0xffffffff
    h[2] = (h[3] + e + aa) & 0xffffffff
    h[3] = (h[4] + a + bb) & 0xffffffff
    h[4] = (h[0] + b + cc) & 0xffffffff
    h[0] = t

# Classe para compatibilidade com a interface da biblioteca Crypto.Hash
class RIPEMD160:
    def __init__(self, data=None):
        self._buffer = bytes()
        if data:
            self.update(data)
    
    def update(self, data):
        if not isinstance(data, bytes):
            data = str(data).encode('utf-8')
        self._buffer += data
    
    def digest(self):
        return ripemd160(self._buffer)
    
    def hexdigest(self):
        return self.digest().hex()
    
    @classmethod
    def new(cls, data=None):
        return cls(data)
