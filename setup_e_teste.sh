#!/bin/bash

# Setup e Teste da Busca CUDA Ultra-Rápida
# Este script configura e testa o programa de busca CUDA

echo "🚀 SETUP E TESTE - BUSCA CUDA ULTRA-RÁPIDA"
echo "=========================================="

# Função para verificar se comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Verificar dependências
echo "📦 Verificando dependências..."

if ! command_exists nvcc; then
    echo "❌ NVCC não encontrado!"
    echo "💡 Instale o CUDA Toolkit:"
    echo "   Ubuntu: sudo apt install nvidia-cuda-toolkit"
    echo "   CentOS: sudo yum install cuda-toolkit"
    echo "   Ou baixe de: https://developer.nvidia.com/cuda-downloads"
    exit 1
fi

if ! command_exists nvidia-smi; then
    echo "❌ nvidia-smi não encontrado!"
    echo "💡 Instale os drivers NVIDIA:"
    echo "   Ubuntu: sudo apt install nvidia-driver-xxx"
    echo "   Ou baixe de: https://www.nvidia.com/drivers"
    exit 1
fi

echo "✅ NVCC encontrado: $(nvcc --version | grep release)"
echo "✅ nvidia-smi encontrado"

# Verificar GPU
echo ""
echo "🔍 Verificando GPU..."
GPU_COUNT=$(nvidia-smi --list-gpus | wc -l)
if [ $GPU_COUNT -eq 0 ]; then
    echo "❌ Nenhuma GPU NVIDIA detectada!"
    exit 1
fi

echo "✅ GPUs detectadas: $GPU_COUNT"
nvidia-smi --query-gpu=name,compute_cap,memory.total --format=csv,noheader

# Compilar programa
echo ""
echo "🔧 Compilando programa CUDA..."
make clean
make

if [ $? -ne 0 ]; then
    echo "❌ Erro na compilação!"
    exit 1
fi

echo "✅ Compilação bem-sucedida!"

# Função para executar teste
executar_teste() {
    local chave=$1
    local hash_errado=$2
    local range_end=$3
    
    echo ""
    echo "🧪 TESTE $chave - Range: 1 a $range_end"
    echo "Hash errado alvo: $hash_errado"
    echo "Iniciando busca..."
    
    start_time=$(date +%s.%N)
    ./busca_cuda "$hash_errado" 1 $range_end
    end_time=$(date +%s.%N)
    
    duration=$(echo "$end_time - $start_time" | bc)
    echo "⏱️  Tempo total do teste: ${duration}s"
}

# Testes com as chaves conhecidas
echo ""
echo "🎯 EXECUTANDO TESTES COM CHAVES CONHECIDAS"
echo "=========================================="

# Teste 1: Chave privada 1
executar_teste "1" "36df2f22295784ab7f81989f9247bfd99bb00c03" 1000000

# Teste 2: Chave privada 2  
executar_teste "2" "5fed51813a4b0353320dbee6fc24a63c5f695181" 1000000

# Teste 3: Chave privada 3
executar_teste "3" "b0548c85212204a8a9555adbbdb6dab85b77afa4" 1000000

# Benchmark de velocidade
echo ""
echo "📊 BENCHMARK DE VELOCIDADE"
echo "=========================="
echo "Testando velocidade com 10 milhões de chaves..."

start_time=$(date +%s.%N)
./busca_cuda "36df2f22295784ab7f81989f9247bfd99bb00c03" 1 10000000
end_time=$(date +%s.%N)

duration=$(echo "$end_time - $start_time" | bc)
chaves_testadas=10000000
velocidade=$(echo "scale=0; $chaves_testadas / $duration" | bc)

echo ""
echo "📈 RESULTADOS DO BENCHMARK:"
echo "Chaves testadas: $chaves_testadas"
echo "Tempo: ${duration}s"
echo "Velocidade: $velocidade chaves/segundo"
echo "Velocidade: $(echo "scale=2; $velocidade / 1000000" | bc)M chaves/segundo"

# Comparar com busca Python
echo ""
echo "⚖️  COMPARAÇÃO COM BUSCA PYTHON"
echo "==============================="

echo "Testando busca Python (main.py) para comparação..."
python_start=$(date +%s.%N)
timeout 30s python3 main.py > /dev/null 2>&1
python_end=$(date +%s.%N)
python_duration=$(echo "$python_end - $python_start" | bc)

echo "Tempo Python (30s max): ${python_duration}s"
echo "Speedup estimado: $(echo "scale=0; $velocidade / 10000" | bc)x mais rápido"

# Teste de stress (opcional)
echo ""
echo "🔥 TESTE DE STRESS (OPCIONAL)"
echo "============================="
read -p "Executar teste de stress com 1 bilhão de chaves? (s/N): " stress_test

if [[ $stress_test =~ ^[Ss]$ ]]; then
    echo "🔥 Iniciando teste de stress..."
    echo "ATENÇÃO: Isso pode levar vários minutos!"
    
    stress_start=$(date +%s.%N)
    ./busca_cuda "36df2f22295784ab7f81989f9247bfd99bb00c03" 1 1000000000
    stress_end=$(date +%s.%N)
    
    stress_duration=$(echo "$stress_end - $stress_start" | bc)
    stress_chaves=1000000000
    stress_velocidade=$(echo "scale=0; $stress_chaves / $stress_duration" | bc)
    
    echo ""
    echo "🔥 RESULTADOS DO TESTE DE STRESS:"
    echo "Chaves testadas: $stress_chaves"
    echo "Tempo: ${stress_duration}s"
    echo "Velocidade: $stress_velocidade chaves/segundo"
    echo "Velocidade: $(echo "scale=2; $stress_velocidade / 1000000" | bc)M chaves/segundo"
fi

# Resumo final
echo ""
echo "📋 RESUMO FINAL"
echo "==============="
echo "✅ Programa CUDA compilado e testado com sucesso"
echo "✅ Velocidade medida: $velocidade chaves/segundo"
echo "✅ Speedup vs Python: ~$(echo "scale=0; $velocidade / 10000" | bc)x"
echo ""
echo "🎯 COMO USAR:"
echo "./busca_cuda <hash_errado_hex> [start_range] [end_range]"
echo ""
echo "📝 EXEMPLOS:"
echo "./busca_cuda 36df2f22295784ab7f81989f9247bfd99bb00c03 1 1000000"
echo "./busca_cuda 5fed51813a4b0353320dbee6fc24a63c5f695181 1000000 2000000"
echo ""
echo "🚀 Programa pronto para uso!"
