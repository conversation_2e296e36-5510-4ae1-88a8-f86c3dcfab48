#!/usr/bin/env python3
"""
TESTE CHAVE CONHECIDA
Calcula o hash160 real da chave 863317 para usar no CUDA
"""

from main import private_key_to_address, endereco_para_hash160, simular_gpu_errada_para_chave

def calcular_hash160_chave_conhecida():
    """
    Calcula o hash160 real da chave privada 863317
    """
    chave_privada = 863317
    
    print("🔍 CALCULANDO HASH160 REAL DA CHAVE CONHECIDA")
    print("=" * 50)
    print(f"Chave privada: {chave_privada}")
    print(f"Chave (hex): 0x{chave_privada:x}")
    
    # Gerar endereço real
    endereco_real = private_key_to_address(chave_privada)
    print(f"Endereço real: {endereco_real}")
    
    # Calcular hash160 real
    hash160_real = endereco_para_hash160(endereco_real)
    hash160_hex = hash160_real.hex()
    print(f"Hash160 real: {hash160_hex}")
    
    # Calcular hash errado
    hash_errado = simular_gpu_errada_para_chave(chave_privada)
    hash_errado_hex = hash_errado.hex()
    print(f"Hash errado: {hash_errado_hex}")
    
    return hash160_hex, hash_errado_hex

def main():
    hash160_real, hash_errado = calcular_hash160_chave_conhecida()
    
    print(f"\n✅ DADOS PARA USAR NO CUDA:")
    print("=" * 30)
    print(f"Hash160 real: {hash160_real}")
    print(f"Hash errado: {hash_errado}")
    
    print(f"\n🔧 MODIFICAÇÃO NECESSÁRIA NO CÓDIGO CUDA:")
    print("=" * 50)
    print(f'const char* hex = "{hash160_real}";')
    
    print(f"\n🚀 COMANDO PARA TESTAR:")
    print("=" * 30)
    print(f"./encontrar_chave_cuda_ultra 1HsMJxNiV7TLxmoF6uJNkydxPFDog4NQum 863000:864000")

if __name__ == "__main__":
    main()
