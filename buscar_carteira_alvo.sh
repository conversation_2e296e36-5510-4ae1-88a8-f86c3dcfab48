#!/bin/bash

# BUSCA DA CARTEIRA ALVO - 1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU
# Script configurado especificamente para encontrar esta carteira

echo "🎯 BUSCA DA CARTEIRA ALVO"
echo "========================="
echo "Endereço: 1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU"
echo "Hash160 correto: f6f5431d25bbf7b12e8add9af5e3475c44a0a5b8"
echo "Hash160 errado:  15dcad75ce214766086340311434d412874c7e77"
echo ""

# Verificar se o programa existe
if [ ! -f "./busca_cuda" ]; then
    echo "❌ Programa busca_cuda não encontrado!"
    echo "Execute: make"
    exit 1
fi

# Configurações da busca
TARGET_HASH="15dcad75ce214766086340311434d412874c7e77"
TARGET_ADDRESS="1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU"

echo "🚀 ESTRATÉGIA DE BUSCA ESCALONADA"
echo "================================="
echo "Testando ranges progressivamente maiores até encontrar a chave"
echo ""

# Função para executar busca em um range
executar_busca() {
    local start=$1
    local end=$2
    local nome=$3
    
    echo "🔍 $nome: Range $start - $end"
    echo "Chaves a testar: $((end - start))"
    
    start_time=$(date +%s)
    
    # Executar busca CUDA
    ./busca_cuda "$TARGET_HASH" "$start" "$end"
    result=$?
    
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    echo "⏱️  Tempo: ${duration}s"
    
    if [ $result -eq 0 ]; then
        echo "🎉 CHAVE ENCONTRADA!"
        echo ""
        echo "🔑 VALIDANDO RESULTADO..."
        python3 verificar_resultado_cuda.py "$chave_encontrada" "$TARGET_HASH"
        return 0
    else
        echo "❌ Não encontrada neste range"
        echo ""
        return 1
    fi
}

# RANGE 1: Chaves muito pequenas (1 - 1M)
echo "📍 RANGE 1: CHAVES MUITO PEQUENAS"
if executar_busca 1 1000000 "Chaves 1-1M"; then
    exit 0
fi

# RANGE 2: Chaves pequenas (1M - 10M)
echo "📍 RANGE 2: CHAVES PEQUENAS"
if executar_busca 1000000 10000000 "Chaves 1M-10M"; then
    exit 0
fi

# RANGE 3: Chaves médias (10M - 100M)
echo "📍 RANGE 3: CHAVES MÉDIAS"
if executar_busca 10000000 100000000 "Chaves 10M-100M"; then
    exit 0
fi

# RANGE 4: Chaves grandes (100M - 1B)
echo "📍 RANGE 4: CHAVES GRANDES"
if executar_busca 100000000 1000000000 "Chaves 100M-1B"; then
    exit 0
fi

# RANGE 5: Chaves muito grandes (1B - 10B)
echo "📍 RANGE 5: CHAVES MUITO GRANDES"
if executar_busca 1000000000 10000000000 "Chaves 1B-10B"; then
    exit 0
fi

# RANGE 6: Ranges hexadecimais específicos
echo "📍 RANGE 6: RANGES HEXADECIMAIS ESPECÍFICOS"

# Range 0x10000 - 0x100000 (64K - 1M)
if executar_busca 65536 1048576 "Hex 0x10000-0x100000"; then
    exit 0
fi

# Range 0x100000 - 0x1000000 (1M - 16M)
if executar_busca 1048576 16777216 "Hex 0x100000-0x1000000"; then
    exit 0
fi

# Range 0x1000000 - 0x10000000 (16M - 256M)
if executar_busca 16777216 268435456 "Hex 0x1000000-0x10000000"; then
    exit 0
fi

# RANGE 7: Ranges baseados em padrões conhecidos
echo "📍 RANGE 7: PADRÕES CONHECIDOS"

# Testar ranges baseados nos padrões das chaves conhecidas
# Chave 1, 2, 3 estão em posições baixas, mas pode haver um padrão

# Range puzzle (potências de 2)
ranges_puzzle=(
    "1 2"
    "2 4" 
    "4 8"
    "8 16"
    "16 32"
    "32 64"
    "64 128"
    "128 256"
    "256 512"
    "512 1024"
    "1024 2048"
    "2048 4096"
    "4096 8192"
    "8192 16384"
    "16384 32768"
    "32768 65536"
)

for range in "${ranges_puzzle[@]}"; do
    start=$(echo $range | cut -d' ' -f1)
    end=$(echo $range | cut -d' ' -f2)
    if executar_busca $start $end "Puzzle 2^n: $start-$end"; then
        exit 0
    fi
done

# RANGE 8: Busca massiva (se chegou até aqui)
echo "📍 RANGE 8: BUSCA MASSIVA"
echo "⚠️  ATENÇÃO: Isso pode levar muito tempo!"
read -p "Continuar com busca massiva? (s/N): " continuar

if [[ $continuar =~ ^[Ss]$ ]]; then
    # Range massivo: 10B - 100B
    if executar_busca 10000000000 100000000000 "Chaves 10B-100B"; then
        exit 0
    fi
    
    # Range extremo: 100B - 1T
    if executar_busca 100000000000 1000000000000 "Chaves 100B-1T"; then
        exit 0
    fi
fi

# Se chegou até aqui, não encontrou
echo "❌ CHAVE NÃO ENCONTRADA"
echo "================================="
echo "A chave privada pode estar em um range muito alto ou"
echo "o hash160 errado pode estar incorreto."
echo ""
echo "💡 SUGESTÕES:"
echo "1. Verificar se o hash160 errado está correto"
echo "2. Testar ranges específicos baseados em informações adicionais"
echo "3. Usar força bruta distribuída em múltiplas máquinas"
echo ""
echo "📊 ESTATÍSTICAS DA BUSCA:"
echo "Ranges testados: 8"
echo "Chaves testadas: Bilhões+"
echo "Tempo total: Vários minutos/horas"
