#!/usr/bin/env python3
"""
ANÁLISE DE PADRÕES - HASH CORRETO vs HASH ERRADO
Compara hash160 correto e errado das 20 primeiras carteiras
para encontrar padrões, semelhanças ou afastamentos
"""

from main import private_key_to_address, simular_gpu_errada_para_chave
from bitcoin_conversions import calculate_target_hash160
import time

def gerar_dados_20_carteiras():
    """
    Gera dados das 20 primeiras carteiras
    """
    print("🔍 GERANDO DADOS DAS 20 PRIMEIRAS CARTEIRAS")
    print("=" * 50)
    
    carteiras = []
    
    for chave_privada in range(1, 21):
        try:
            print(f"Processando chave {chave_privada}...")
            
            # Gerar endereço Bitcoin real
            endereco = private_key_to_address(chave_privada)
            
            # Calcular hash160 correto
            hash160_correto = calculate_target_hash160(endereco)
            hash160_correto_hex = hash160_correto.hex()
            
            # Calcular hash160 errado
            hash160_errado = simular_gpu_errada_para_chave(chave_privada)
            hash160_errado_hex = hash160_errado.hex()
            
            carteira = {
                'chave_privada': chave_privada,
                'endereco': endereco,
                'hash160_correto': hash160_correto_hex,
                'hash160_errado': hash160_errado_hex
            }
            
            carteiras.append(carteira)
            
        except Exception as e:
            print(f"❌ Erro na chave {chave_privada}: {e}")
            continue
    
    print(f"✅ Processadas {len(carteiras)} carteiras")
    return carteiras

def mostrar_tabela_comparativa(carteiras):
    """
    Mostra tabela comparativa dos hashes
    """
    print("\n📊 TABELA COMPARATIVA - HASH CORRETO vs HASH ERRADO")
    print("=" * 100)
    
    print(f"{'Chave':>5} | {'Hash160 Correto':^40} | {'Hash160 Errado':^40}")
    print("-" * 100)
    
    for carteira in carteiras:
        chave = carteira['chave_privada']
        hash_correto = carteira['hash160_correto']
        hash_errado = carteira['hash160_errado']
        
        print(f"{chave:5d} | {hash_correto} | {hash_errado}")

def analisar_diferencas_byte_a_byte(carteiras):
    """
    Analisa diferenças byte a byte entre hash correto e errado
    """
    print("\n🔍 ANÁLISE BYTE A BYTE - DIFERENÇAS")
    print("=" * 50)
    
    total_diferencas = [0] * 20  # 20 bytes
    total_carteiras = len(carteiras)
    
    print(f"{'Chave':>5} | Posições com diferenças (0-19)")
    print("-" * 60)
    
    for carteira in carteiras:
        chave = carteira['chave_privada']
        hash_correto = carteira['hash160_correto']
        hash_errado = carteira['hash160_errado']
        
        diferencas = []
        
        # Comparar byte a byte
        for i in range(0, 40, 2):  # 40 chars hex = 20 bytes
            byte_correto = hash_correto[i:i+2]
            byte_errado = hash_errado[i:i+2]
            
            if byte_correto != byte_errado:
                posicao = i // 2
                diferencas.append(posicao)
                total_diferencas[posicao] += 1
        
        print(f"{chave:5d} | {diferencas}")
    
    print(f"\n📊 ESTATÍSTICAS DE DIFERENÇAS POR POSIÇÃO:")
    print("-" * 50)
    
    for i, count in enumerate(total_diferencas):
        porcentagem = (count / total_carteiras) * 100
        print(f"Posição {i:2d}: {count:2d}/{total_carteiras} carteiras ({porcentagem:5.1f}%)")

def calcular_distancia_hamming(carteiras):
    """
    Calcula distância de Hamming entre hashes
    """
    print("\n🔢 DISTÂNCIA DE HAMMING")
    print("=" * 30)
    
    print(f"{'Chave':>5} | {'Bits Diferentes':>15} | {'% Diferença':>12}")
    print("-" * 40)
    
    distancias = []
    
    for carteira in carteiras:
        chave = carteira['chave_privada']
        hash_correto = carteira['hash160_correto']
        hash_errado = carteira['hash160_errado']
        
        # Converter para bytes
        bytes_correto = bytes.fromhex(hash_correto)
        bytes_errado = bytes.fromhex(hash_errado)
        
        # Calcular distância de Hamming (bits diferentes)
        distancia = 0
        for b1, b2 in zip(bytes_correto, bytes_errado):
            xor = b1 ^ b2
            distancia += bin(xor).count('1')
        
        total_bits = 20 * 8  # 160 bits
        porcentagem = (distancia / total_bits) * 100
        
        distancias.append(distancia)
        print(f"{chave:5d} | {distancia:15d} | {porcentagem:11.1f}%")
    
    # Estatísticas
    media = sum(distancias) / len(distancias)
    minima = min(distancias)
    maxima = max(distancias)
    
    print(f"\n📊 ESTATÍSTICAS:")
    print(f"Distância média: {media:.1f} bits")
    print(f"Distância mínima: {minima} bits")
    print(f"Distância máxima: {maxima} bits")
    print(f"Range: {maxima - minima} bits")

def analisar_padroes_sequenciais(carteiras):
    """
    Analisa padrões sequenciais entre chaves consecutivas
    """
    print("\n🔄 ANÁLISE DE PADRÕES SEQUENCIAIS")
    print("=" * 40)
    
    print("Comparando hashes errados de chaves consecutivas...")
    print(f"{'Chaves':>8} | {'Bits Diferentes':>15} | {'% Diferença':>12}")
    print("-" * 45)
    
    for i in range(len(carteiras) - 1):
        chave1 = carteiras[i]['chave_privada']
        chave2 = carteiras[i + 1]['chave_privada']
        
        hash1 = carteiras[i]['hash160_errado']
        hash2 = carteiras[i + 1]['hash160_errado']
        
        # Calcular diferença
        bytes1 = bytes.fromhex(hash1)
        bytes2 = bytes.fromhex(hash2)
        
        distancia = 0
        for b1, b2 in zip(bytes1, bytes2):
            xor = b1 ^ b2
            distancia += bin(xor).count('1')
        
        total_bits = 160
        porcentagem = (distancia / total_bits) * 100
        
        print(f"{chave1:2d} → {chave2:2d} | {distancia:15d} | {porcentagem:11.1f}%")

def buscar_padroes_especificos(carteiras):
    """
    Busca padrões específicos nos hashes
    """
    print("\n🎯 BUSCA DE PADRÕES ESPECÍFICOS")
    print("=" * 40)
    
    # Padrão 1: Bytes que se repetem
    print("1. BYTES QUE SE REPETEM:")
    for carteira in carteiras:
        chave = carteira['chave_privada']
        hash_correto = carteira['hash160_correto']
        hash_errado = carteira['hash160_errado']
        
        # Verificar bytes repetidos
        bytes_correto = [hash_correto[i:i+2] for i in range(0, 40, 2)]
        bytes_errado = [hash_errado[i:i+2] for i in range(0, 40, 2)]
        
        repeticoes_correto = len(bytes_correto) - len(set(bytes_correto))
        repeticoes_errado = len(bytes_errado) - len(set(bytes_errado))
        
        if repeticoes_correto > 0 or repeticoes_errado > 0:
            print(f"   Chave {chave}: Correto={repeticoes_correto}, Errado={repeticoes_errado}")
    
    # Padrão 2: Prefixos/sufixos comuns
    print("\n2. PREFIXOS COMUNS (primeiros 4 bytes):")
    prefixos_correto = {}
    prefixos_errado = {}
    
    for carteira in carteiras:
        chave = carteira['chave_privada']
        hash_correto = carteira['hash160_correto']
        hash_errado = carteira['hash160_errado']
        
        prefixo_correto = hash_correto[:8]
        prefixo_errado = hash_errado[:8]
        
        prefixos_correto[prefixo_correto] = prefixos_correto.get(prefixo_correto, []) + [chave]
        prefixos_errado[prefixo_errado] = prefixos_errado.get(prefixo_errado, []) + [chave]
    
    print("   Hash Correto:")
    for prefixo, chaves in prefixos_correto.items():
        if len(chaves) > 1:
            print(f"     {prefixo}: chaves {chaves}")
    
    print("   Hash Errado:")
    for prefixo, chaves in prefixos_errado.items():
        if len(chaves) > 1:
            print(f"     {prefixo}: chaves {chaves}")

def analisar_correlacao_chave_hash(carteiras):
    """
    Analisa correlação entre valor da chave privada e hash gerado
    """
    print("\n📈 CORRELAÇÃO CHAVE PRIVADA vs HASH")
    print("=" * 40)
    
    print("Analisando se há correlação entre valor da chave e hash gerado...")
    
    # Converter primeiro byte do hash para decimal
    print(f"{'Chave':>5} | {'1º Byte Correto':>15} | {'1º Byte Errado':>14} | {'Diferença':>10}")
    print("-" * 60)
    
    for carteira in carteiras:
        chave = carteira['chave_privada']
        hash_correto = carteira['hash160_correto']
        hash_errado = carteira['hash160_errado']
        
        primeiro_byte_correto = int(hash_correto[:2], 16)
        primeiro_byte_errado = int(hash_errado[:2], 16)
        diferenca = abs(primeiro_byte_correto - primeiro_byte_errado)
        
        print(f"{chave:5d} | {primeiro_byte_correto:15d} | {primeiro_byte_errado:14d} | {diferenca:10d}")

def salvar_resultados(carteiras):
    """
    Salva resultados em arquivo para análise posterior
    """
    filename = f"analise_padroes_hash_{int(time.time())}.txt"
    
    with open(filename, 'w') as f:
        f.write("ANÁLISE DE PADRÕES - HASH CORRETO vs HASH ERRADO\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("DADOS DAS 20 PRIMEIRAS CARTEIRAS:\n")
        f.write("-" * 40 + "\n")
        
        for carteira in carteiras:
            f.write(f"Chave {carteira['chave_privada']:2d}:\n")
            f.write(f"  Endereço: {carteira['endereco']}\n")
            f.write(f"  Hash Correto: {carteira['hash160_correto']}\n")
            f.write(f"  Hash Errado:  {carteira['hash160_errado']}\n")
            f.write("\n")
    
    print(f"\n💾 Resultados salvos em: {filename}")

def main():
    """Função principal"""
    print("🔍 ANÁLISE DE PADRÕES - HASH CORRETO vs HASH ERRADO")
    print("=" * 60)
    print("Analisando as 20 primeiras carteiras Bitcoin...")
    print()
    
    # Gerar dados
    carteiras = gerar_dados_20_carteiras()
    
    if not carteiras:
        print("❌ Erro: Não foi possível gerar dados das carteiras")
        return
    
    # Análises
    mostrar_tabela_comparativa(carteiras)
    analisar_diferencas_byte_a_byte(carteiras)
    calcular_distancia_hamming(carteiras)
    analisar_padroes_sequenciais(carteiras)
    buscar_padroes_especificos(carteiras)
    analisar_correlacao_chave_hash(carteiras)
    
    # Salvar resultados
    salvar_resultados(carteiras)
    
    print(f"\n🎊 ANÁLISE CONCLUÍDA!")
    print("=" * 25)
    print("📊 Verifique os resultados acima para identificar padrões")
    print("💡 Procure por:")
    print("   • Posições que sempre diferem")
    print("   • Distâncias de Hamming consistentes")
    print("   • Padrões sequenciais")
    print("   • Correlações com valor da chave")

if __name__ == "__main__":
    main()
