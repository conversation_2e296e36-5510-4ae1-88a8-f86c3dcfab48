#!/usr/bin/env python3
"""
REDE NEURAL MELHORADA PARA HASH PREDICTOR
Versão com melhorias significativas para aumentar acurácia
"""

import numpy as np
import time
import json
import random
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed

# Tentar importar CuPy
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("✅ CuPy disponível - GPU habilitada")
except ImportError:
    import numpy as cp
    GPU_AVAILABLE = False
    print("⚠️  CuPy não encontrado - usando CPU")

# Importar funções necessárias
try:
    from test_magic_number import simular_gpu_errada_para_chave
    from main import private_key_to_address
    from bitcoin_conversions import calculate_target_hash160
    print("✅ Módulos Bitcoin importados")
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    exit(1)

def hex_to_vector_improved(hex_string):
    """Converte hex para vetor com normalização melhorada"""
    hex_string = hex_string.ljust(40, '0')[:40]
    
    # Usar one-hot encoding para cada caractere hex
    vector = np.zeros(40 * 16, dtype=np.float32)  # 40 chars * 16 possibilidades
    
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            char_idx = int(char, 16)
            vector[i * 16 + char_idx] = 1.0
        # Se caractere inválido, deixar todos zeros (representa "desconhecido")
    
    return vector

def vector_to_hex_improved(vector):
    """Converte vetor one-hot de volta para hex"""
    hex_chars = '0123456789abcdef'
    hex_string = ''
    
    # Reshape para (40, 16)
    vector_reshaped = vector.reshape(40, 16)
    
    for char_probs in vector_reshaped:
        # Pegar o índice com maior probabilidade
        char_idx = np.argmax(char_probs)
        hex_string += hex_chars[char_idx]
    
    return hex_string

def generate_sample_batch_improved(key_batch):
    """Gera batch de amostras com encoding melhorado"""
    inputs = []
    outputs = []
    successful_keys = []
    
    for private_key in key_batch:
        try:
            endereco = private_key_to_address(private_key)
            if not endereco:
                continue
            
            hash160_correto = calculate_target_hash160(endereco)
            if not hash160_correto:
                continue
            
            numero_magico = simular_gpu_errada_para_chave(private_key)
            if not numero_magico:
                continue
            
            input_vector = hex_to_vector_improved(hash160_correto.hex())
            output_vector = hex_to_vector_improved(numero_magico.hex())
            
            inputs.append(input_vector)
            outputs.append(output_vector)
            successful_keys.append(private_key)
            
        except Exception:
            continue
    
    return inputs, outputs, successful_keys

class ImprovedNeuralNetwork:
    """Rede neural melhorada com arquitetura otimizada"""
    
    def __init__(self, input_size=640, hidden_sizes=[1024, 2048, 4096, 2048, 1024], output_size=640, use_gpu=True):
        self.input_size = input_size  # 40 * 16 = 640
        self.output_size = output_size  # 40 * 16 = 640
        self.layers = [input_size] + hidden_sizes + [output_size]
        self.use_gpu = use_gpu and GPU_AVAILABLE
        self.xp = cp if self.use_gpu else np
        
        print(f"🧠 Rede Neural Melhorada")
        print(f"🔧 Dispositivo: {'GPU' if self.use_gpu else 'CPU'}")
        print(f"📊 Arquitetura: {' → '.join(map(str, self.layers))}")
        print(f"📊 Encoding: One-hot (40 chars × 16 valores = 640 dims)")
        
        # Inicializar pesos com He initialization
        self.weights = []
        self.biases = []
        
        for i in range(len(self.layers) - 1):
            fan_in = self.layers[i]
            
            # He initialization para ReLU
            std = self.xp.sqrt(2.0 / fan_in)
            w = self.xp.random.normal(0, std, (fan_in, self.layers[i+1])).astype(self.xp.float32)
            b = self.xp.zeros((1, self.layers[i+1]), dtype=self.xp.float32)
            
            self.weights.append(w)
            self.biases.append(b)
        
        total_params = sum(w.size for w in self.weights) + sum(b.size for b in self.biases)
        print(f"📊 Parâmetros: {total_params:,}")
    
    def leaky_relu(self, x, alpha=0.01):
        """Leaky ReLU para evitar neurônios mortos"""
        return self.xp.where(x > 0, x, alpha * x)
    
    def leaky_relu_derivative(self, x, alpha=0.01):
        """Derivada do Leaky ReLU"""
        return self.xp.where(x > 0, 1, alpha)
    
    def softmax(self, x):
        """Softmax para cada grupo de 16 valores (cada caractere hex)"""
        # Reshape para (batch_size, 40, 16)
        batch_size = x.shape[0]
        x_reshaped = x.reshape(batch_size, 40, 16)
        
        # Aplicar softmax em cada grupo de 16
        exp_x = self.xp.exp(x_reshaped - self.xp.max(x_reshaped, axis=2, keepdims=True))
        softmax_x = exp_x / self.xp.sum(exp_x, axis=2, keepdims=True)
        
        # Reshape de volta para (batch_size, 640)
        return softmax_x.reshape(batch_size, 640)
    
    def forward(self, X):
        """Forward pass melhorado"""
        if self.use_gpu and not isinstance(X, cp.ndarray):
            X = cp.asarray(X)
        elif not self.use_gpu and isinstance(X, cp.ndarray):
            X = cp.asnumpy(X)
        
        activations = [X]
        z_values = []
        
        for i, (w, b) in enumerate(zip(self.weights, self.biases)):
            z = self.xp.dot(activations[-1], w) + b
            z_values.append(z)
            
            # Leaky ReLU nas camadas ocultas, softmax na saída
            if i < len(self.weights) - 1:
                a = self.leaky_relu(z)
            else:
                a = self.softmax(z)
            
            activations.append(a)
        
        return activations, z_values
    
    def cross_entropy_loss(self, y_pred, y_true):
        """Cross-entropy loss para classificação multi-classe"""
        # Evitar log(0)
        y_pred_clipped = self.xp.clip(y_pred, 1e-15, 1 - 1e-15)
        
        # Cross-entropy
        loss = -self.xp.sum(y_true * self.xp.log(y_pred_clipped))
        return loss / y_pred.shape[0]
    
    def backward_improved(self, X, y, activations, z_values, learning_rate, l2_reg=0.0001):
        """Backward pass melhorado com cross-entropy"""
        m = X.shape[0]
        
        # Erro da saída (cross-entropy + softmax)
        output_error = activations[-1] - y
        
        # Backpropagation
        errors = [output_error]
        
        for i in range(len(self.weights) - 1, 0, -1):
            error = self.xp.dot(errors[-1], self.weights[i].T)
            error *= self.leaky_relu_derivative(z_values[i-1])
            errors.append(error)
        
        errors.reverse()
        
        # Atualizar pesos com regularização
        for i in range(len(self.weights)):
            dw = self.xp.dot(activations[i].T, errors[i]) / m
            db = self.xp.mean(errors[i], axis=0, keepdims=True)
            
            # Regularização L2
            dw += l2_reg * self.weights[i]
            
            # Gradient clipping
            dw = self.xp.clip(dw, -1.0, 1.0)
            db = self.xp.clip(db, -1.0, 1.0)
            
            self.weights[i] -= learning_rate * dw
            self.biases[i] -= learning_rate * db
        
        # Calcular loss
        ce_loss = self.cross_entropy_loss(activations[-1], y)
        l2_loss = sum(self.xp.sum(w ** 2) for w in self.weights) * l2_reg
        
        return float(ce_loss + l2_loss)
    
    def train_improved(self, X, y, epochs=3000, initial_lr=0.001, batch_size=128):
        """Treinamento melhorado com técnicas avançadas"""
        print(f"\n🚀 TREINAMENTO MELHORADO")
        print("=" * 30)
        
        # Converter dados para GPU se necessário
        if self.use_gpu:
            if not isinstance(X, cp.ndarray):
                X = cp.asarray(X)
            if not isinstance(y, cp.ndarray):
                y = cp.asarray(y)
        
        num_batches = len(X) // batch_size
        best_loss = float('inf')
        patience = 300
        patience_counter = 0
        
        print(f"Configuração:")
        print(f"  Amostras: {len(X):,}")
        print(f"  Batch size: {batch_size}")
        print(f"  Batches por época: {num_batches}")
        print(f"  Learning rate inicial: {initial_lr}")
        
        for epoch in range(epochs):
            # Learning rate schedule mais agressivo
            if epoch < 500:
                lr = initial_lr
            elif epoch < 1000:
                lr = initial_lr * 0.5
            elif epoch < 2000:
                lr = initial_lr * 0.2
            else:
                lr = initial_lr * 0.1
            
            # Regularização adaptativa
            l2_reg = 0.0001 * (1 + epoch / 1000)
            
            # Embaralhar dados
            if self.use_gpu:
                indices = cp.random.permutation(len(X))
            else:
                indices = np.random.permutation(len(X))
            
            X_shuffled = X[indices]
            y_shuffled = y[indices]
            
            epoch_loss = 0.0
            
            # Treinar por batches
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = start_idx + batch_size
                
                X_batch = X_shuffled[start_idx:end_idx]
                y_batch = y_shuffled[start_idx:end_idx]
                
                # Forward pass
                activations, z_values = self.forward(X_batch)
                
                # Backward pass
                batch_loss = self.backward_improved(X_batch, y_batch, activations, z_values, lr, l2_reg)
                epoch_loss += batch_loss
            
            epoch_loss /= num_batches
            
            # Early stopping
            if epoch_loss < best_loss:
                best_loss = epoch_loss
                patience_counter = 0
            else:
                patience_counter += 1
            
            if patience_counter >= patience:
                print(f"\n⏰ Early stopping na época {epoch+1}")
                break
            
            # Log progresso
            if (epoch + 1) % 100 == 0:
                print(f"Época {epoch+1:4d}/{epochs} | "
                      f"Loss: {epoch_loss:.6f} | "
                      f"LR: {lr:.6f} | "
                      f"L2: {l2_reg:.6f} | "
                      f"Best: {best_loss:.6f}")
        
        print(f"\n✅ Treinamento melhorado concluído!")
        print(f"📈 Melhor loss: {best_loss:.6f}")
        
        return best_loss
    
    def predict(self, X):
        """Predição melhorada"""
        activations, _ = self.forward(X)
        result = activations[-1]
        
        # Converter para CPU se necessário
        if self.use_gpu and isinstance(result, cp.ndarray):
            result = cp.asnumpy(result)
        
        return result

class ImprovedHashPredictor:
    """Preditor melhorado com técnicas avançadas"""
    
    def __init__(self):
        self.model = None
        self.use_gpu = GPU_AVAILABLE
        
        print(f"🚀 Improved Hash Predictor")
        print(f"🔧 GPU: {'Habilitada' if self.use_gpu else 'Desabilitada'}")
    
    def generate_dataset_improved(self, num_samples=30000, start_key=1, end_key=1000000):
        """Gera dataset melhorado com mais amostras"""
        print(f"🚀 GERANDO DATASET MELHORADO: {num_samples:,} AMOSTRAS")
        print("=" * 55)
        
        # Chaves aleatórias com distribuição melhor
        all_keys = []
        
        # 70% chaves pequenas (mais padrões)
        small_keys = random.sample(range(start_key, min(100000, end_key)), 
                                  min(int(num_samples * 0.7), 99999))
        all_keys.extend(small_keys)
        
        # 30% chaves grandes (generalização)
        remaining = num_samples - len(all_keys)
        if remaining > 0:
            large_keys = random.sample(range(100000, end_key + 1), 
                                     min(remaining, end_key - 100000 + 1))
            all_keys.extend(large_keys)
        
        # Processamento paralelo
        num_processes = min(mp.cpu_count(), 10)
        batch_size = len(all_keys) // num_processes
        
        key_batches = [all_keys[i:i + batch_size] 
                      for i in range(0, len(all_keys), batch_size)]
        
        print(f"🔄 Processamento: {num_processes} processos")
        print(f"📦 Distribuição: 70% chaves pequenas, 30% grandes")
        
        all_inputs = []
        all_outputs = []
        
        start_time = time.time()
        
        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            futures = [executor.submit(generate_sample_batch_improved, batch) 
                      for batch in key_batches]
            
            for i, future in enumerate(as_completed(futures)):
                try:
                    inputs, outputs, keys = future.result()
                    all_inputs.extend(inputs)
                    all_outputs.extend(outputs)
                    
                    print(f"   Batch {i+1}/{len(key_batches)}: {len(inputs)} amostras")
                    
                except Exception as e:
                    print(f"   ❌ Erro batch {i+1}: {e}")
        
        generation_time = time.time() - start_time
        
        print(f"\n📊 DATASET MELHORADO:")
        print(f"✅ Amostras: {len(all_inputs):,}")
        print(f"⏱️  Tempo: {generation_time:.1f}s")
        print(f"⚡ Velocidade: {len(all_inputs) / generation_time:.0f} amostras/s")
        
        if len(all_inputs) == 0:
            return None, None
        
        return np.array(all_inputs, dtype=np.float32), np.array(all_outputs, dtype=np.float32)
    
    def evaluate_improved(self, X_test, y_test):
        """Avaliação melhorada"""
        print(f"\n📊 AVALIAÇÃO MELHORADA")
        print("=" * 25)
        
        predictions = self.model.predict(X_test)
        
        # Converter predições para hex
        print(f"🔍 AMOSTRAS DE TESTE (primeiras 10):")
        print("-" * 80)
        
        accuracies = []
        
        for i in range(min(10, len(predictions))):
            hash_real = vector_to_hex_improved(y_test[i])
            hash_pred = vector_to_hex_improved(predictions[i])
            
            # Acurácia por caractere
            correct_chars = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            accuracies.append(accuracy)
            
            print(f"Amostra {i+1}:")
            print(f"  Real:     {hash_real}")
            print(f"  Predito:  {hash_pred}")
            print(f"  Acurácia: {accuracy:.1f}% ({correct_chars}/40)")
            
            # Mostrar diferenças
            diffs = [j for j, (a, b) in enumerate(zip(hash_real, hash_pred)) if a != b]
            if len(diffs) <= 10:
                print(f"  Diffs:    posições {diffs}")
            else:
                print(f"  Diffs:    {len(diffs)} posições diferentes")
            print()
        
        # Acurácia geral
        all_accuracies = []
        for i in range(len(predictions)):
            hash_real = vector_to_hex_improved(y_test[i])
            hash_pred = vector_to_hex_improved(predictions[i])
            correct_chars = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            all_accuracies.append(accuracy)
        
        avg_accuracy = np.mean(all_accuracies)
        median_accuracy = np.median(all_accuracies)
        max_accuracy = np.max(all_accuracies)
        
        print(f"📈 ESTATÍSTICAS DE ACURÁCIA:")
        print(f"   Média: {avg_accuracy:.1f}%")
        print(f"   Mediana: {median_accuracy:.1f}%")
        print(f"   Máxima: {max_accuracy:.1f}%")
        print(f"   Amostras > 50%: {sum(1 for a in all_accuracies if a > 50)}")
        print(f"   Amostras > 80%: {sum(1 for a in all_accuracies if a > 80)}")
        
        return avg_accuracy
    
    def run_improved_training(self, num_samples=30000):
        """Pipeline melhorado completo"""
        print("🚀 IMPROVED HASH PREDICTOR")
        print("=" * 30)
        
        total_start = time.time()
        
        # 1. Gerar dataset melhorado
        X, y = self.generate_dataset_improved(num_samples=num_samples)
        
        if X is None:
            print("❌ Falha na geração do dataset")
            return 0
        
        # 2. Dividir dados (mais dados para teste)
        test_size = min(3000, len(X) // 8)  # 12.5% para teste
        
        indices = np.random.permutation(len(X))
        X_test = X[indices[:test_size]]
        y_test = y[indices[:test_size]]
        X_train = X[indices[test_size:]]
        y_train = y[indices[test_size:]]
        
        print(f"\n📊 DIVISÃO MELHORADA:")
        print(f"Treino: {len(X_train):,} amostras")
        print(f"Teste: {len(X_test):,} amostras")
        
        # 3. Criar e treinar modelo melhorado
        self.model = ImprovedNeuralNetwork(use_gpu=self.use_gpu)
        self.model.train_improved(X_train, y_train, epochs=2000, batch_size=64)
        
        # 4. Avaliar
        accuracy = self.evaluate_improved(X_test, y_test)
        
        # 5. Salvar
        self.save_improved_model()
        
        total_time = time.time() - total_start
        
        print(f"\n🎊 TREINAMENTO MELHORADO CONCLUÍDO!")
        print(f"⏱️  Tempo total: {total_time:.1f} segundos")
        print(f"📈 Acurácia final: {accuracy:.1f}%")
        
        return accuracy
    
    def save_improved_model(self):
        """Salva modelo melhorado"""
        # Converter pesos para CPU
        weights_cpu = []
        biases_cpu = []
        
        for w, b in zip(self.model.weights, self.model.biases):
            if self.use_gpu:
                weights_cpu.append(cp.asnumpy(w).tolist())
                biases_cpu.append(cp.asnumpy(b).tolist())
            else:
                weights_cpu.append(w.tolist())
                biases_cpu.append(b.tolist())
        
        model_data = {
            'layers': self.model.layers,
            'weights': weights_cpu,
            'biases': biases_cpu,
            'encoding': 'one_hot',
            'input_size': 640,
            'output_size': 640
        }
        
        with open('improved_neural_hash_model.json', 'w') as f:
            json.dump(model_data, f)
        
        print(f"💾 Modelo melhorado salvo em 'improved_neural_hash_model.json'")
    
    def predict_magic_number_improved(self, hash160_correto_hex):
        """Predição melhorada"""
        if self.model is None:
            print("❌ Modelo não treinado!")
            return None
        
        input_vector = hex_to_vector_improved(hash160_correto_hex).reshape(1, -1)
        prediction = self.model.predict(input_vector)
        
        return vector_to_hex_improved(prediction[0])

def main():
    """Função principal melhorada"""
    print("🚀 IMPROVED NEURAL HASH PREDICTOR")
    print("=" * 40)
    
    # Verificar status
    if GPU_AVAILABLE:
        print("✅ CUDA disponível")
    else:
        print("⚠️  Usando CPU")
    
    # Configuração
    num_samples = int(input("Número de amostras (recomendado: 30000): ") or "30000")
    
    # Executar
    predictor = ImprovedHashPredictor()
    accuracy = predictor.run_improved_training(num_samples=num_samples)
    
    if accuracy > 0:
        print(f"\n🎯 RESULTADO FINAL: {accuracy:.1f}% de acurácia")
        
        # Teste interativo
        print(f"\n🧪 TESTE INTERATIVO:")
        while True:
            hash_input = input("\nDigite hash160 correto (40 chars) ou 'quit': ").strip()
            
            if hash_input.lower() == 'quit':
                break
            
            if len(hash_input) != 40:
                print("❌ Hash deve ter 40 caracteres")
                continue
            
            magic_number = predictor.predict_magic_number_improved(hash_input)
            print(f"Número mágico melhorado: {magic_number}")
            print(f"🚀 Comando: ./buscar_chave_por_hash_errado {magic_number} 1:1000000")

if __name__ == "__main__":
    main()
