#!/bin/bash

# TESTAR ENDEREÇO ESPECÍFICO
# Testa o endereço 14oFNXucftsHiUMY8uctg6N487riuyXs4h

echo "🎯 TESTANDO ENDEREÇO ESPECÍFICO"
echo "==============================="

ENDERECO="14oFNXucftsHiUMY8uctg6N487riuyXs4h"

echo "Endereço: $ENDERECO"
echo ""

# PASSO 1: Calcular hash errado do endereço
echo "📍 PASSO 1: Calculando hash errado do endereço"
echo "=============================================="

python3 hash_errado_direto.py "$ENDERECO"

echo ""
echo "📋 AGORA VOCÊ TEM O HASH ERRADO!"
echo "================================"
echo ""
echo "💡 PRÓXIMOS PASSOS:"
echo "1. Copie o hash errado gerado acima"
echo "2. Compile o programa de busca:"
echo "   make -f Makefile_buscar_chave auto"
echo "3. Execute a busca:"
echo "   ./buscar_chave_por_hash_errado [HASH_ERRADO] 1:1000000000"
echo ""
echo "🧪 EXEMPLO DE TESTE COM CHAVE CONHECIDA:"
echo "make -f Makefile_buscar_chave test-chave1"
echo ""
echo "📝 ARQUIVO SALVO:"
echo "Verifique o arquivo hash_errado_${ENDERECO:0:10}.txt"
