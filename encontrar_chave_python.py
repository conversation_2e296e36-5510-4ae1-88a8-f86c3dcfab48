#!/usr/bin/env python3
"""
ENCONTRAR CHAVE REAL - VERSÃO PYTHON
Busca a chave privada real que gera um endereço e calcula seu hash errado
"""

import sys
import time
from main import private_key_to_address, simular_gpu_errada_para_chave

def encontrar_chave_real_python(endereco_alvo, start_range, end_range):
    """
    Busca a chave privada real que gera o endereço alvo
    """
    print(f"🎯 ENCONTRAR CHAVE REAL - VERSÃO PYTHON")
    print("=" * 50)
    print(f"Endereço alvo: {endereco_alvo}")
    print(f"Range: {start_range:,} - {end_range:,}")
    print(f"Total chaves: {end_range - start_range:,}")
    
    inicio = time.time()
    chaves_testadas = 0
    
    print(f"\n🔍 PROCURANDO CHAVE PRIVADA REAL...")
    print("=" * 40)
    
    for chave_privada in range(start_range, end_range + 1):
        try:
            # Gerar endereço usando a função correta
            endereco_gerado = private_key_to_address(chave_privada)
            
            chaves_testadas += 1
            
            # Verificar se bate com o alvo
            if endereco_gerado == endereco_alvo:
                tempo_busca = time.time() - inicio
                
                print(f"\n🎉 CHAVE PRIVADA REAL ENCONTRADA!")
                print("=" * 40)
                print(f"🔑 Chave privada: {chave_privada}")
                print(f"🔑 Chave (hex): 0x{chave_privada:x}")
                print(f"🎯 Endereço: {endereco_gerado}")
                print(f"⏱️  Tempo de busca: {tempo_busca:.2f} segundos")
                print(f"🔢 Chaves testadas: {chaves_testadas:,}")
                
                if tempo_busca > 0:
                    velocidade = chaves_testadas / tempo_busca
                    print(f"⚡ Velocidade: {velocidade:.0f} chaves/segundo")
                
                # Calcular hash errado da chave real
                print(f"\n🎩 CALCULANDO HASH ERRADO DA CHAVE REAL...")
                hash_errado = simular_gpu_errada_para_chave(chave_privada)
                hash_errado_hex = hash_errado.hex()
                
                print(f"✅ Hash errado calculado: {hash_errado_hex}")
                
                # Salvar resultado
                filename = f"chave_real_{endereco_alvo[:10]}.txt"
                with open(filename, 'w') as f:
                    f.write(f"Endereço alvo: {endereco_alvo}\n")
                    f.write(f"Chave privada real: {chave_privada}\n")
                    f.write(f"Chave (hex): 0x{chave_privada:x}\n")
                    f.write(f"Hash errado: {hash_errado_hex}\n")
                    f.write(f"Tempo de busca: {tempo_busca:.2f} segundos\n")
                    f.write(f"Chaves testadas: {chaves_testadas}\n")
                    f.write(f"Descoberto em: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"\nComando para usar:\n")
                    f.write(f"./buscar_chave_por_hash_errado {hash_errado_hex} {start_range} {end_range}\n")
                
                print(f"💾 Resultado salvo em: {filename}")
                
                print(f"\n✅ AGORA USE ESTE HASH ERRADO:")
                print("=" * 40)
                print(f"Hash errado: {hash_errado_hex}")
                print(f"\n🚀 COMANDO PARA BUSCAR:")
                print(f"./buscar_chave_por_hash_errado {hash_errado_hex} {start_range} {end_range}")
                
                return chave_privada, hash_errado_hex
            
            # Progresso a cada 100K chaves
            if chaves_testadas % 100000 == 0:
                tempo_decorrido = time.time() - inicio
                velocidade = chaves_testadas / tempo_decorrido if tempo_decorrido > 0 else 0
                progresso = ((chave_privada - start_range) / (end_range - start_range)) * 100
                
                print(f"   Progresso: {progresso:.3f}% | Chave: {chave_privada:,} | Velocidade: {velocidade:.0f} chaves/seg")
            
        except KeyboardInterrupt:
            print(f"\n⏹️  Busca interrompida pelo usuário")
            return None, None
        except Exception as e:
            continue
    
    print(f"\n❌ CHAVE REAL NÃO ENCONTRADA")
    print("=" * 30)
    print(f"💡 A chave privada não está no range {start_range:,} - {end_range:,}")
    print(f"💡 Tente um range maior")
    
    return None, None

def testar_chaves_conhecidas():
    """
    Testa com chaves privadas conhecidas para validar
    """
    print("🧪 TESTE COM CHAVES CONHECIDAS")
    print("=" * 40)
    
    chaves_conhecidas = [
        (1, "**********************************"),
        (2, "**********************************"),
        (3, "1C4vLLyKHdMsQ4vw5xnATkxgXLHdniaA1s"),
        (4, "1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa"),
        (5, "1HLoD9E4SDFFPDiYfNYnkBLQ85Y51J3Zb1"),
    ]
    
    for chave_privada, endereco_esperado in chaves_conhecidas:
        print(f"\n🔍 Testando chave privada {chave_privada}:")
        
        # Gerar endereço
        endereco_gerado = private_key_to_address(chave_privada)
        print(f"   Endereço gerado: {endereco_gerado}")
        print(f"   Endereço esperado: {endereco_esperado}")
        
        if endereco_gerado == endereco_esperado:
            print(f"   ✅ Endereço correto!")
            
            # Calcular hash errado
            hash_errado = simular_gpu_errada_para_chave(chave_privada)
            hash_errado_hex = hash_errado.hex()
            print(f"   Hash errado: {hash_errado_hex}")
        else:
            print(f"   ❌ Endereço incorreto!")

def converter_range(range_str):
    """
    Converte string de range para números
    """
    if ':' not in range_str:
        raise ValueError("Range deve estar no formato start:end")
    
    start_str, end_str = range_str.split(':')
    
    # Converter hex para decimal se necessário
    def convert_num(s):
        if s.startswith('0x') or s.startswith('0X'):
            return int(s, 16)
        elif any(c in s.lower() for c in 'abcdef'):
            return int(s, 16)
        else:
            return int(s)
    
    return convert_num(start_str), convert_num(end_str)

def main():
    """Função principal"""
    if len(sys.argv) >= 3:
        # Modo com argumentos
        endereco_alvo = sys.argv[1]
        range_str = sys.argv[2]
        
        try:
            start_range, end_range = converter_range(range_str)
            
            print("🎯 ENCONTRAR CHAVE REAL - MODO ARGUMENTOS")
            print("=" * 50)
            
            chave, hash_errado = encontrar_chave_real_python(endereco_alvo, start_range, end_range)
            
            if chave:
                print(f"\n🎊 SUCESSO!")
                print(f"Chave privada real: {chave}")
                print(f"Hash errado: {hash_errado}")
            else:
                print(f"\n😞 Não foi possível encontrar a chave")
                
        except ValueError as e:
            print(f"❌ Erro no range: {e}")
            print("Formato correto: start:end (ex: 1:1000000)")
    
    else:
        # Modo interativo
        print("🎯 ENCONTRAR CHAVE REAL - MODO INTERATIVO")
        print("=" * 50)
        
        print("\nEscolha uma opção:")
        print("1. 🧪 Testar chaves conhecidas (validação)")
        print("2. 🎯 Buscar chave real de um endereço")
        print("3. 📋 Buscar com endereços de exemplo")
        
        opcao = input("Opção (1/2/3): ").strip()
        
        if opcao == "1":
            testar_chaves_conhecidas()
            
        elif opcao == "2":
            endereco = input("\nDigite o endereço Bitcoin: ").strip()
            range_str = input("Digite o range (start:end): ").strip()
            
            if endereco and range_str:
                try:
                    start_range, end_range = converter_range(range_str)
                    
                    chave, hash_errado = encontrar_chave_real_python(endereco, start_range, end_range)
                    
                    if chave:
                        print(f"\n🎊 SUCESSO!")
                        print(f"Chave privada real: {chave}")
                        print(f"Hash errado: {hash_errado}")
                    
                except ValueError as e:
                    print(f"❌ Erro no range: {e}")
            
        elif opcao == "3":
            print(f"\n📋 ENDEREÇOS DE EXEMPLO:")
            exemplos = [
                ("**********************************", "1:10", "Chave privada 1"),
                ("**********************************", "1:10", "Chave privada 2"),
                ("1C4vLLyKHdMsQ4vw5xnATkxgXLHdniaA1s", "1:10", "Chave privada 3"),
            ]
            
            for i, (endereco, range_sugerido, descricao) in enumerate(exemplos, 1):
                print(f"{i}. {descricao}")
                print(f"   Endereço: {endereco}")
                print(f"   Range sugerido: {range_sugerido}")
                print()
            
            escolha = input("Escolha um exemplo (1-3): ").strip()
            
            try:
                idx = int(escolha) - 1
                if 0 <= idx < len(exemplos):
                    endereco, range_sugerido, descricao = exemplos[idx]
                    
                    print(f"\n🎯 Testando: {descricao}")
                    start_range, end_range = converter_range(range_sugerido)
                    
                    chave, hash_errado = encontrar_chave_real_python(endereco, start_range, end_range)
                    
                    if chave:
                        print(f"\n🎊 SUCESSO!")
                        print(f"Chave privada real: {chave}")
                        print(f"Hash errado: {hash_errado}")
                        
            except (ValueError, IndexError):
                print("❌ Escolha inválida")
        
        else:
            print("❌ Opção inválida!")

if __name__ == "__main__":
    main()
