#!/usr/bin/env python3
"""
REDE NEURAL PARA PREDIÇÃO DE HASH ERRADO
Utiliza deep learning para aprender a relação entre hash correto e hash errado
"""

import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, accuracy_score
import warnings
warnings.filterwarnings('ignore')

# Configurar TensorFlow para usar CPU (evitar problemas de GPU)
tf.config.set_visible_devices([], 'GPU')

class HashNeuralNetwork:
    def __init__(self):
        """Inicializa o preditor de hash com rede neural"""
        self.model = None
        self.history = None
        
        # Dados de treinamento conhecidos
        self.training_data = [
            {
                'chave_privada': 'd2c55',
                'hash_correto': '8ffac8f5ea58ea7a48722370d05f717ca695675e',
                'hash_errado': 'b907c3a2a3b27789dfb509b730dd47703c272868'
            },
            {
                'chave_privada': '1',
                'hash_correto': '36df2f22295784ab7f81989f9247bfd99bb00c03',
                'hash_errado': '751e76e8199196d454941c45d1b3a323f1433bd6'
            },
            {
                'chave_privada': '2',
                'hash_correto': '5fed51813a4b0353320dbee6fc24a63c5f695181',
                'hash_errado': '06afd46bcdfd22ef94ac122aa11f241244a37ecc'
            },
            {
                'chave_privada': '3',
                'hash_correto': 'b0548c85212204a8a9555adbbdb6dab85b77afa4',
                'hash_errado': '7dd65592d0ab2fe0d0257d571abf032cd9db93dc'
            }
        ]
    
    def hex_to_vector(self, hex_string):
        """
        Converte string hexadecimal para vetor numérico normalizado
        
        Args:
            hex_string: String hexadecimal (40 caracteres)
            
        Returns:
            numpy array: Vetor normalizado de 40 dimensões (valores 0-1)
        """
        # Garantir que o hash tenha 40 caracteres
        hex_string = hex_string.ljust(40, '0')[:40]
        
        # Converter cada caractere hex para valor numérico (0-15)
        vector = []
        for char in hex_string:
            if char in '0123456789abcdef':
                vector.append(int(char, 16))
            else:
                vector.append(0)  # Caractere inválido = 0
        
        # Normalizar para range 0-1
        return np.array(vector, dtype=np.float32) / 15.0
    
    def vector_to_hex(self, vector):
        """
        Converte vetor numérico de volta para string hexadecimal
        
        Args:
            vector: numpy array normalizado (0-1)
            
        Returns:
            str: String hexadecimal de 40 caracteres
        """
        # Desnormalizar (0-1 -> 0-15)
        denormalized = np.round(vector * 15.0).astype(int)
        
        # Garantir que valores estejam no range válido
        denormalized = np.clip(denormalized, 0, 15)
        
        # Converter para hex
        hex_chars = '0123456789abcdef'
        hex_string = ''.join([hex_chars[val] for val in denormalized])
        
        return hex_string
    
    def prepare_data(self):
        """
        Prepara dados de treinamento com aumento de dados
        
        Returns:
            tuple: (X_train, y_train, X_test, y_test)
        """
        print("📊 PREPARANDO DADOS DE TREINAMENTO")
        print("=" * 40)
        
        X = []  # Hashes corretos
        y = []  # Hashes errados
        
        # Processar dados originais
        for data in self.training_data:
            hash_correto_vec = self.hex_to_vector(data['hash_correto'])
            hash_errado_vec = self.hex_to_vector(data['hash_errado'])
            
            X.append(hash_correto_vec)
            y.append(hash_errado_vec)
            
            print(f"Chave {data['chave_privada']:>5}: {data['hash_correto'][:8]}... -> {data['hash_errado'][:8]}...")
        
        # AUMENTO DE DADOS: Gerar variações sintéticas
        print(f"\n🔄 GERANDO DADOS SINTÉTICOS...")
        
        original_count = len(X)
        
        for i in range(original_count):
            # Gerar 20 variações para cada amostra original
            for j in range(20):
                # Adicionar ruído pequeno ao hash correto
                noise_level = 0.02  # 2% de ruído
                noisy_input = X[i] + np.random.normal(0, noise_level, X[i].shape)
                noisy_input = np.clip(noisy_input, 0, 1)  # Manter no range válido
                
                # Para o output, adicionar ruído correlacionado
                noisy_output = y[i] + np.random.normal(0, noise_level * 0.5, y[i].shape)
                noisy_output = np.clip(noisy_output, 0, 1)
                
                X.append(noisy_input)
                y.append(noisy_output)
        
        print(f"Dados originais: {original_count}")
        print(f"Dados sintéticos: {len(X) - original_count}")
        print(f"Total: {len(X)}")
        
        # Converter para numpy arrays
        X = np.array(X)
        y = np.array(y)
        
        # Dividir em treino e teste (manter dados originais no teste)
        X_train = X[original_count:]  # Apenas dados sintéticos para treino
        y_train = y[original_count:]
        
        X_test = X[:original_count]   # Dados originais para teste
        y_test = y[:original_count]
        
        print(f"\nConjunto de treino: {len(X_train)} amostras")
        print(f"Conjunto de teste: {len(X_test)} amostras")
        
        return X_train, y_train, X_test, y_test
    
    def create_model(self, input_dim=40, output_dim=40):
        """
        Cria arquitetura da rede neural
        
        Args:
            input_dim: Dimensão de entrada (40 para hash de 40 chars)
            output_dim: Dimensão de saída (40 para hash de 40 chars)
            
        Returns:
            keras.Model: Modelo compilado
        """
        print(f"\n🧠 CRIANDO ARQUITETURA DA REDE NEURAL")
        print("=" * 45)
        
        model = keras.Sequential([
            # Camada de entrada
            layers.Input(shape=(input_dim,)),
            
            # Camadas densas com dropout para evitar overfitting
            layers.Dense(128, activation='relu', name='dense_1'),
            layers.Dropout(0.3),
            
            layers.Dense(256, activation='relu', name='dense_2'),
            layers.Dropout(0.4),
            
            layers.Dense(512, activation='relu', name='dense_3'),
            layers.Dropout(0.4),
            
            layers.Dense(256, activation='relu', name='dense_4'),
            layers.Dropout(0.3),
            
            layers.Dense(128, activation='relu', name='dense_5'),
            layers.Dropout(0.2),
            
            # Camada de saída com ativação sigmoid (valores 0-1)
            layers.Dense(output_dim, activation='sigmoid', name='output')
        ])
        
        # Compilar modelo
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss='mse',  # Mean Squared Error para regressão
            metrics=['mae']  # Mean Absolute Error
        )
        
        print(f"Arquitetura criada:")
        model.summary()
        
        return model
    
    def train_model(self, X_train, y_train, X_test, y_test, epochs=1000):
        """
        Treina o modelo com os dados preparados
        
        Args:
            X_train, y_train: Dados de treinamento
            X_test, y_test: Dados de teste
            epochs: Número de épocas de treinamento
            
        Returns:
            keras.callbacks.History: Histórico do treinamento
        """
        print(f"\n🚀 INICIANDO TREINAMENTO")
        print("=" * 30)
        
        # Callbacks para melhorar o treinamento
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=100,
                restore_best_weights=True,
                verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=50,
                min_lr=1e-6,
                verbose=1
            )
        ]
        
        # Treinar modelo
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=epochs,
            batch_size=32,
            callbacks=callbacks,
            verbose=1
        )
        
        return history
    
    def evaluate_model(self, X_test, y_test):
        """
        Avalia o modelo nos dados de teste
        
        Args:
            X_test, y_test: Dados de teste
        """
        print(f"\n📊 AVALIAÇÃO DO MODELO")
        print("=" * 25)
        
        # Fazer predições
        predictions = self.model.predict(X_test, verbose=0)
        
        # Calcular métricas
        mse = mean_squared_error(y_test.flatten(), predictions.flatten())
        mae = np.mean(np.abs(y_test - predictions))
        
        print(f"Mean Squared Error: {mse:.6f}")
        print(f"Mean Absolute Error: {mae:.6f}")
        
        # Avaliar acurácia por caractere
        char_accuracy = []
        
        print(f"\n🔍 PREDIÇÕES DETALHADAS:")
        print("-" * 80)
        
        for i, data in enumerate(self.training_data):
            # Hash correto original
            hash_correto = data['hash_correto']
            hash_errado_real = data['hash_errado']
            
            # Predição
            hash_errado_pred = self.vector_to_hex(predictions[i])
            
            # Calcular acurácia por caractere
            correct_chars = sum(1 for a, b in zip(hash_errado_real, hash_errado_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            char_accuracy.append(accuracy)
            
            print(f"Chave {data['chave_privada']:>5}:")
            print(f"  Hash correto: {hash_correto}")
            print(f"  Hash real:    {hash_errado_real}")
            print(f"  Hash predito: {hash_errado_pred}")
            print(f"  Acurácia:     {accuracy:.1f}% ({correct_chars}/40 caracteres)")
            print()
        
        avg_accuracy = np.mean(char_accuracy)
        print(f"📈 ACURÁCIA MÉDIA POR CARACTERE: {avg_accuracy:.1f}%")
        
        return predictions, avg_accuracy
    
    def plot_training_history(self):
        """Plota gráficos do histórico de treinamento"""
        if self.history is None:
            print("❌ Nenhum histórico de treinamento disponível")
            return
        
        plt.figure(figsize=(12, 4))
        
        # Loss
        plt.subplot(1, 2, 1)
        plt.plot(self.history.history['loss'], label='Treino')
        plt.plot(self.history.history['val_loss'], label='Validação')
        plt.title('Loss durante Treinamento')
        plt.xlabel('Época')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True)
        
        # MAE
        plt.subplot(1, 2, 2)
        plt.plot(self.history.history['mae'], label='Treino')
        plt.plot(self.history.history['val_mae'], label='Validação')
        plt.title('MAE durante Treinamento')
        plt.xlabel('Época')
        plt.ylabel('MAE')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("📊 Gráficos salvos em 'training_history.png'")
    
    def predict_hash_errado(self, hash_correto):
        """
        Prediz hash errado a partir de hash correto
        
        Args:
            hash_correto: String hexadecimal de 40 caracteres
            
        Returns:
            str: Hash errado predito
        """
        if self.model is None:
            print("❌ Modelo não foi treinado ainda!")
            return None
        
        # Converter para vetor
        input_vector = self.hex_to_vector(hash_correto).reshape(1, -1)
        
        # Fazer predição
        prediction = self.model.predict(input_vector, verbose=0)
        
        # Converter de volta para hex
        hash_errado_pred = self.vector_to_hex(prediction[0])
        
        return hash_errado_pred
    
    def run_complete_pipeline(self):
        """Executa pipeline completo de treinamento e avaliação"""
        print("🧠 REDE NEURAL PARA PREDIÇÃO DE HASH ERRADO")
        print("=" * 50)
        
        # 1. Preparar dados
        X_train, y_train, X_test, y_test = self.prepare_data()
        
        # 2. Criar modelo
        self.model = self.create_model()
        
        # 3. Treinar modelo
        self.history = self.train_model(X_train, y_train, X_test, y_test)
        
        # 4. Avaliar modelo
        predictions, accuracy = self.evaluate_model(X_test, y_test)
        
        # 5. Plotar histórico
        self.plot_training_history()
        
        # 6. Salvar modelo
        self.model.save('hash_predictor_model.h5')
        print(f"\n💾 Modelo salvo em 'hash_predictor_model.h5'")
        
        return accuracy

def main():
    """Função principal"""
    # Criar e treinar rede neural
    predictor = HashNeuralNetwork()
    accuracy = predictor.run_complete_pipeline()
    
    print(f"\n🎊 TREINAMENTO CONCLUÍDO!")
    print(f"Acurácia final: {accuracy:.1f}%")
    
    # Teste interativo
    print(f"\n🧪 TESTE INTERATIVO:")
    while True:
        hash_input = input("\nDigite um hash correto (40 chars hex) ou 'quit': ").strip()
        
        if hash_input.lower() == 'quit':
            break
        
        if len(hash_input) != 40:
            print("❌ Hash deve ter exatamente 40 caracteres hexadecimais")
            continue
        
        hash_pred = predictor.predict_hash_errado(hash_input)
        print(f"Hash errado predito: {hash_pred}")

if __name__ == "__main__":
    main()
