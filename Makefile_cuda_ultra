# Makefile para Encontrar Chave CUDA Ultra-Rápida

NVCC = nvcc
TARGET = encontrar_chave_cuda_ultra
SOURCE = encontrar_chave_cuda_ultra.cu

# Detectar arquitetura automaticamente
GPU_ARCH_RAW := $(shell nvidia-smi --query-gpu=compute_cap --format=csv,noheader,nounits 2>/dev/null | head -1)
GPU_ARCH_MAJOR := $(shell echo $(GPU_ARCH_RAW) | cut -d'.' -f1)

# Mapear arquiteturas
ifeq ($(GPU_ARCH_MAJOR),12)
    GPU_ARCH = 89  # RTX 5090
else ifeq ($(GPU_ARCH_MAJOR),8)
    GPU_ARCH = 86  # RTX 30xx/40xx
else ifeq ($(GPU_ARCH_MAJOR),7)
    GPU_ARCH = 75  # RTX 20xx
else
    GPU_ARCH = 75  # Default
endif

# Flags ultra-otimizadas para máxima performance
NVCC_FLAGS = -O3 -arch=sm_$(GPU_ARCH) -std=c++11 --use_fast_math -maxrregcount=64 --ptxas-options=-v

all: $(TARGET)

$(TARGET): $(SOURCE)
	@echo "🚀 Compilando versão CUDA ultra-rápida..."
	@echo "GPU Arch detectada: sm_$(GPU_ARCH) (Compute $(GPU_ARCH_RAW))"
	@echo "Flags otimizadas: $(NVCC_FLAGS)"
	$(NVCC) $(NVCC_FLAGS) -o $(TARGET) $(SOURCE)
	@echo "✅ Compilação ultra-otimizada concluída!"

# Compilação automática com fallback
auto: 
	@echo "🚀 Compilação automática ultra-otimizada..."
	$(NVCC) -O3 -arch=sm_89 -std=c++11 --use_fast_math -maxrregcount=64 -o $(TARGET) $(SOURCE) 2>/dev/null || \
	$(NVCC) -O3 -arch=sm_86 -std=c++11 --use_fast_math -maxrregcount=64 -o $(TARGET) $(SOURCE) 2>/dev/null || \
	$(NVCC) -O3 -arch=sm_75 -std=c++11 --use_fast_math -maxrregcount=64 -o $(TARGET) $(SOURCE) 2>/dev/null || \
	$(NVCC) -O3 -std=c++11 --use_fast_math -o $(TARGET) $(SOURCE)
	@echo "✅ Compilação automática concluída!"

# Teste com chave conhecida 1 (validação)
test-chave1: $(TARGET)
	@echo "🧪 TESTE CUDA: Chave privada 1"
	@echo "=============================="
	@echo "Endereço: 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2"
	@echo "Range: 1:10 (deve encontrar chave 1 rapidamente)"
	@echo "Comparação: Python levaria ~0.01 segundos"
	@echo ""
	./$(TARGET) 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2 1:10

# Teste com chave conhecida 2
test-chave2: $(TARGET)
	@echo "🧪 TESTE CUDA: Chave privada 2"
	@echo "=============================="
	@echo "Endereço: 1JwSSubhmg6iPtRjtyqhUYYH7bZg3Lfy1T"
	@echo "Range: 1:10 (deve encontrar chave 2 rapidamente)"
	@echo ""
	./$(TARGET) 1JwSSubhmg6iPtRjtyqhUYYH7bZg3Lfy1T 1:10

# Teste com endereço que funcionou no Python
test-endereco-funcionou: $(TARGET)
	@echo "🧪 TESTE CUDA: Endereço que funcionou no Python"
	@echo "=============================================="
	@echo "Endereço: 1HsMJxNiV7TLxmoF6uJNkydxPFDog4NQum"
	@echo "Range: 80000:1048575 (chave 863317)"
	@echo "Python levou: 91 segundos"
	@echo "CUDA deve ser 100-1000x mais rápido!"
	@echo ""
	./$(TARGET) 1HsMJxNiV7TLxmoF6uJNkydxPFDog4NQum 80000:1048575

# Teste de performance com range maior
test-performance: $(TARGET)
	@echo "🚀 TESTE DE PERFORMANCE CUDA"
	@echo "============================"
	@echo "Range grande para medir velocidade máxima"
	@echo ""
	./$(TARGET) 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2 1:10000000

# Teste interativo
test-interactive: $(TARGET)
	@echo "🧪 TESTE INTERATIVO CUDA"
	@echo "======================="
	./$(TARGET)

# Comparação Python vs CUDA
compare-python-cuda: $(TARGET)
	@echo "⚡ COMPARAÇÃO PYTHON vs CUDA"
	@echo "============================"
	@echo ""
	@echo "1️⃣  Executando Python (para comparação):"
	@echo "Endereço: 1HsMJxNiV7TLxmoF6uJNkydxPFDog4NQum"
	@echo "Range: 863000:864000 (1000 chaves)"
	@echo ""
	time python3 encontrar_chave_python.py 1HsMJxNiV7TLxmoF6uJNkydxPFDog4NQum 863000:864000
	@echo ""
	@echo "2️⃣  Executando CUDA (mesma busca):"
	@echo ""
	time ./$(TARGET) 1HsMJxNiV7TLxmoF6uJNkydxPFDog4NQum 863000:864000

clean:
	rm -f $(TARGET)
	rm -f chave_real_cuda_*.txt

info:
	@echo "ℹ️  Informações do sistema:"
	@echo "NVCC: $(shell nvcc --version | grep release)"
	@echo "GPU: $(shell nvidia-smi --query-gpu=name --format=csv,noheader)"
	@echo "Compute Cap: $(GPU_ARCH_RAW)"
	@echo "Arquitetura: sm_$(GPU_ARCH)"
	@echo "Memória GPU: $(shell nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits) MB"
	@echo "Multiprocessors: $(shell nvidia-smi --query-gpu=count --format=csv,noheader)"

benchmark: $(TARGET)
	@echo "🏁 BENCHMARK COMPLETO"
	@echo "===================="
	@echo ""
	@echo "📊 Teste 1: Range pequeno (validação)"
	./$(TARGET) 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2 1:1000
	@echo ""
	@echo "📊 Teste 2: Range médio"
	./$(TARGET) 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2 1:100000
	@echo ""
	@echo "📊 Teste 3: Range grande"
	./$(TARGET) 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2 1:1000000

help:
	@echo "🚀 ENCONTRAR CHAVE CUDA ULTRA-RÁPIDA"
	@echo "===================================="
	@echo ""
	@echo "📋 COMANDOS:"
	@echo "make all                    - Compilar programa"
	@echo "make auto                   - Compilação automática"
	@echo "make test-chave1            - Testar chave privada 1"
	@echo "make test-chave2            - Testar chave privada 2"
	@echo "make test-endereco-funcionou - Testar endereço que funcionou no Python"
	@echo "make test-performance       - Teste de performance"
	@echo "make test-interactive       - Teste interativo"
	@echo "make compare-python-cuda    - Comparar Python vs CUDA"
	@echo "make benchmark              - Benchmark completo"
	@echo "make clean                  - Limpar"
	@echo "make info                   - Informações do sistema"
	@echo ""
	@echo "🚀 USO DIRETO:"
	@echo "./encontrar_chave_cuda_ultra <endereco> <start:end>"
	@echo "./encontrar_chave_cuda_ultra <endereco> <start> <end>"
	@echo ""
	@echo "📝 EXEMPLOS:"
	@echo "./encontrar_chave_cuda_ultra 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2 1:10"
	@echo "./encontrar_chave_cuda_ultra 1HsMJxNiV7TLxmoF6uJNkydxPFDog4NQum 80000:1048575"
	@echo ""
	@echo "⚡ PERFORMANCE ESPERADA:"
	@echo "• Python: ~8,000 chaves/segundo"
	@echo "• CUDA: ~1,000,000+ chaves/segundo"
	@echo "• Speedup: 100-1000x mais rápido"
	@echo ""
	@echo "🎯 TESTE RECOMENDADO:"
	@echo "make test-endereco-funcionou  # Testar endereço que funcionou no Python"

.PHONY: all auto test-chave1 test-chave2 test-endereco-funcionou test-performance test-interactive compare-python-cuda benchmark clean info help
