#!/bin/bash

# Script de Compilação Inteligente para Descobrir Hash Errado

echo "🔧 COMPILAÇÃO INTELIGENTE - DESCOBRIR HASH ERRADO"
echo "================================================="

# Verificar se NVCC existe
if ! command -v nvcc &> /dev/null; then
    echo "❌ NVCC não encontrado! Instale CUDA Toolkit"
    exit 1
fi

# Detectar arquitetura da GPU
echo "🔍 Detectando arquitetura da GPU..."
GPU_INFO=$(nvidia-smi --query-gpu=compute_cap --format=csv,noheader,nounits | head -1)
echo "Compute Capability detectada: $GPU_INFO"

# Mapear para arquitetura NVCC
if [[ $GPU_INFO == "12.0" ]]; then
    ARCH="89"  # RTX 5090 usa sm_89
elif [[ $GPU_INFO == "8.9" ]]; then
    ARCH="89"  # RTX 4090
elif [[ $GPU_INFO == "8.6" ]]; then
    ARCH="86"  # RTX 3080/3090
elif [[ $GPU_INFO == "7.5" ]]; then
    ARCH="75"  # RTX 2080
elif [[ $GPU_INFO == "6.1" ]]; then
    ARCH="61"  # GTX 1080
else
    ARCH="75"  # Default seguro
fi

echo "Usando arquitetura: sm_$ARCH"

# Tentar compilação com diferentes arquiteturas
echo ""
echo "🔧 Tentando compilação..."

ARQUITETURAS=("$ARCH" "89" "86" "75" "61")
COMPILOU=false

for arch in "${ARQUITETURAS[@]}"; do
    echo "Tentando sm_$arch..."
    
    if nvcc -O3 -arch=sm_$arch -std=c++11 -o descobrir_hash_errado descobrir_hash_errado.cu 2>/dev/null; then
        echo "✅ Compilação bem-sucedida com sm_$arch!"
        COMPILOU=true
        break
    else
        echo "❌ Falhou com sm_$arch"
    fi
done

if [ "$COMPILOU" = false ]; then
    echo ""
    echo "❌ ERRO: Não foi possível compilar com nenhuma arquitetura"
    echo "Tentando compilação sem especificar arquitetura..."
    
    if nvcc -O3 -std=c++11 -o descobrir_hash_errado descobrir_hash_errado.cu; then
        echo "✅ Compilação bem-sucedida sem arquitetura específica!"
        COMPILOU=true
    else
        echo "❌ Falha total na compilação"
        echo ""
        echo "💡 SOLUÇÕES:"
        echo "1. Verificar se CUDA Toolkit está instalado corretamente"
        echo "2. Verificar se drivers NVIDIA estão atualizados"
        echo "3. Tentar compilação manual:"
        echo "   nvcc -O3 -o descobrir_hash_errado descobrir_hash_errado.cu"
        exit 1
    fi
fi

echo ""
echo "🎉 COMPILAÇÃO CONCLUÍDA!"
echo "======================="
echo "Programa: ./descobrir_hash_errado"
echo "Arquitetura usada: sm_$arch"

# Verificar se o arquivo foi criado
if [ -f "./descobrir_hash_errado" ]; then
    echo "✅ Executável criado com sucesso"
    echo ""
    echo "🧪 TESTE RÁPIDO:"
    echo "./descobrir_hash_errado f6f5431d25bbf7b12e8add9af5e3475c44a0a5b8 1 1000"
    echo ""
    echo "🎯 COMANDO COMPLETO:"
    echo "./descobrir_hash_errado f6f5431d25bbf7b12e8add9af5e3475c44a0a5b8 4611686018427387904 9223372036854775807"
else
    echo "❌ Erro: Executável não foi criado"
    exit 1
fi
