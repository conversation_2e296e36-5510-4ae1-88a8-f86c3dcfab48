# Makefile para Encontrar Chave Real

NVCC = nvcc
TARGET = encontrar_chave_real
SOURCE = encontrar_chave_real.cu

# Detectar arquitetura automaticamente
GPU_ARCH_RAW := $(shell nvidia-smi --query-gpu=compute_cap --format=csv,noheader,nounits 2>/dev/null | head -1)
GPU_ARCH_MAJOR := $(shell echo $(GPU_ARCH_RAW) | cut -d'.' -f1)

# Mapear arquiteturas
ifeq ($(GPU_ARCH_MAJOR),12)
    GPU_ARCH = 89  # RTX 5090
else ifeq ($(GPU_ARCH_MAJOR),8)
    GPU_ARCH = 86  # RTX 30xx/40xx
else ifeq ($(GPU_ARCH_MAJOR),7)
    GPU_ARCH = 75  # RTX 20xx
else
    GPU_ARCH = 75  # Default
endif

# Flags otimizadas
NVCC_FLAGS = -O3 -arch=sm_$(GPU_ARCH) -std=c++11 --use_fast_math

all: $(TARGET)

$(TARGET): $(SOURCE)
	@echo "🔧 Compilando encontrar chave real..."
	@echo "GPU Arch detectada: sm_$(GPU_ARCH) (Compute $(GPU_ARCH_RAW))"
	$(NVCC) $(NVCC_FLAGS) -o $(TARGET) $(SOURCE)
	@echo "✅ Compilação concluída!"

# Compilação automática com fallback
auto: 
	@echo "🔧 Compilação automática com fallback..."
	$(NVCC) -O3 -arch=sm_89 -std=c++11 --use_fast_math -o $(TARGET) $(SOURCE) 2>/dev/null || \
	$(NVCC) -O3 -arch=sm_86 -std=c++11 --use_fast_math -o $(TARGET) $(SOURCE) 2>/dev/null || \
	$(NVCC) -O3 -arch=sm_75 -std=c++11 --use_fast_math -o $(TARGET) $(SOURCE) 2>/dev/null || \
	$(NVCC) -O3 -std=c++11 -o $(TARGET) $(SOURCE)
	@echo "✅ Compilação automática concluída!"

# Teste com chave conhecida 1
test-chave1: $(TARGET)
	@echo "🧪 TESTE: Encontrar chave privada 1"
	@echo "Endereço da chave 1: **********************************"
	@echo "Deve encontrar chave privada: 1"
	./$(TARGET) ********************************** 1:1000000

# Teste com chave conhecida 2
test-chave2: $(TARGET)
	@echo "🧪 TESTE: Encontrar chave privada 2"
	@echo "Endereço da chave 2: **********************************"
	@echo "Deve encontrar chave privada: 2"
	./$(TARGET) ********************************** 1:1000000

# Teste com endereço personalizado
test-custom: $(TARGET)
	@echo "🧪 TESTE: Endereço personalizado"
	@echo "=================="
	./$(TARGET) ********************************** 1:1000000000

# Teste interativo
test-interactive: $(TARGET)
	@echo "🧪 TESTE INTERATIVO"
	@echo "=================="
	./$(TARGET)

clean:
	rm -f $(TARGET)

info:
	@echo "ℹ️  Informações do sistema:"
	@echo "NVCC: $(shell nvcc --version | grep release)"
	@echo "GPU: $(shell nvidia-smi --query-gpu=name --format=csv,noheader)"
	@echo "Compute Cap: $(GPU_ARCH_RAW)"
	@echo "Arquitetura: sm_$(GPU_ARCH)"

help:
	@echo "🎯 ENCONTRAR CHAVE REAL E CALCULAR HASH ERRADO"
	@echo "=============================================="
	@echo ""
	@echo "📋 COMANDOS:"
	@echo "make all              - Compilar programa"
	@echo "make auto             - Compilação automática"
	@echo "make test-chave1      - Testar com chave privada 1"
	@echo "make test-chave2      - Testar com chave privada 2"
	@echo "make test-custom      - Testar com endereço personalizado"
	@echo "make test-interactive - Teste interativo"
	@echo "make clean            - Limpar"
	@echo "make info             - Informações do sistema"
	@echo ""
	@echo "🚀 USO DIRETO:"
	@echo "./encontrar_chave_real <endereco> <range>"
	@echo ""
	@echo "📝 EXEMPLOS:"
	@echo "./encontrar_chave_real ********************************** 1:1000000"
	@echo "./encontrar_chave_real ********************************** 1:1000000000"
	@echo ""
	@echo "🎯 LÓGICA DO PROGRAMA:"
	@echo "1. Recebe endereço Bitcoin"
	@echo "2. Busca chave privada real que gera este endereço"
	@echo "3. Quando encontra: calcula hash errado da chave real"
	@echo "4. Retorna hash errado para usar no programa principal"
	@echo ""
	@echo "💡 ENDEREÇOS CONHECIDOS PARA TESTE:"
	@echo "Chave 1: **********************************"
	@echo "Chave 2: **********************************"

.PHONY: all auto test-chave1 test-chave2 test-custom test-interactive clean info help
