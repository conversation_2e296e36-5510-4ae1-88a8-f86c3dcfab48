#!/usr/bin/env python3
"""
ENCONTRAR CHAVE HÍBRIDO - Python + CUDA
Usa Python para validação e CUDA para velocidade
"""

import sys
import time
import subprocess
from main import private_key_to_address, simular_gpu_errada_para_chave, endereco_para_hash160

def encontrar_chave_hibrido(endereco_alvo, start_range, end_range, chunk_size=1000000):
    """
    Busca híbrida: CUDA para velocidade, Python para validação
    """
    print(f"🚀 BUSCA HÍBRIDA - PYTHON + CUDA")
    print("=" * 40)
    print(f"Endereço alvo: {endereco_alvo}")
    print(f"Range: {start_range:,} - {end_range:,}")
    print(f"Chunk size: {chunk_size:,}")
    
    # Calcular hash160 real do endereço alvo
    try:
        hash160_real = endereco_para_hash160(endereco_alvo)
        hash160_hex = hash160_real.hex()
        print(f"Hash160 alvo: {hash160_hex}")
    except Exception as e:
        print(f"❌ Erro ao calcular hash160: {e}")
        return None, None
    
    total_chaves = end_range - start_range
    chunks_processados = 0
    inicio_total = time.time()
    
    print(f"\n🔍 INICIANDO BUSCA HÍBRIDA...")
    print("=" * 40)
    
    # Processar em chunks
    for chunk_start in range(start_range, end_range, chunk_size):
        chunk_end = min(chunk_start + chunk_size, end_range)
        chunks_processados += 1
        
        print(f"\n📍 CHUNK {chunks_processados}: {chunk_start:,} - {chunk_end:,}")
        
        # FASE 1: Busca rápida com CUDA (se disponível)
        print("⚡ Fase 1: Busca CUDA (ultra-rápida)")
        
        try:
            # Tentar usar programa CUDA se existir
            if subprocess.run(["test", "-f", "./encontrar_chave_cuda_ultra"], capture_output=True).returncode == 0:
                resultado_cuda = subprocess.run([
                    "./encontrar_chave_cuda_ultra", 
                    endereco_alvo, 
                    f"{chunk_start}:{chunk_end}"
                ], capture_output=True, text=True, timeout=30)
                
                if resultado_cuda.returncode == 0:
                    print("✅ CUDA encontrou resultado!")
                    # Extrair chave do output CUDA
                    for linha in resultado_cuda.stdout.split('\n'):
                        if "Chave privada real:" in linha:
                            try:
                                chave_str = linha.split(':')[1].strip().split()[0]
                                chave_encontrada = int(chave_str)
                                
                                # Validar com Python
                                endereco_gerado = private_key_to_address(chave_encontrada)
                                if endereco_gerado == endereco_alvo:
                                    hash_errado = simular_gpu_errada_para_chave(chave_encontrada)
                                    return chave_encontrada, hash_errado.hex()
                            except:
                                continue
                
            print("⚠️  CUDA não encontrou ou não disponível, usando Python")
            
        except Exception as e:
            print(f"⚠️  Erro CUDA: {e}, usando Python")
        
        # FASE 2: Busca Python (confiável)
        print("🐍 Fase 2: Busca Python (confiável)")
        
        inicio_chunk = time.time()
        
        for chave_privada in range(chunk_start, chunk_end):
            try:
                endereco_gerado = private_key_to_address(chave_privada)
                
                if endereco_gerado == endereco_alvo:
                    tempo_chunk = time.time() - inicio_chunk
                    tempo_total = time.time() - inicio_total
                    
                    print(f"\n🎉 CHAVE PRIVADA ENCONTRADA!")
                    print("=" * 40)
                    print(f"🔑 Chave privada: {chave_privada}")
                    print(f"🔑 Chave (hex): 0x{chave_privada:x}")
                    print(f"🎯 Endereço: {endereco_gerado}")
                    print(f"⏱️  Tempo chunk: {tempo_chunk:.2f} segundos")
                    print(f"⏱️  Tempo total: {tempo_total:.2f} segundos")
                    
                    # Calcular hash errado
                    hash_errado = simular_gpu_errada_para_chave(chave_privada)
                    hash_errado_hex = hash_errado.hex()
                    
                    print(f"🎩 Hash errado: {hash_errado_hex}")
                    
                    return chave_privada, hash_errado_hex
                
                # Progresso a cada 50K chaves
                if (chave_privada - chunk_start) % 50000 == 0:
                    tempo_decorrido = time.time() - inicio_chunk
                    if tempo_decorrido > 0:
                        velocidade = (chave_privada - chunk_start) / tempo_decorrido
                        progresso_chunk = ((chave_privada - chunk_start) / (chunk_end - chunk_start)) * 100
                        
                        print(f"   Chunk {chunks_processados}: {progresso_chunk:.1f}% | Chave: {chave_privada:,} | Velocidade: {velocidade:.0f} chaves/seg")
                
            except KeyboardInterrupt:
                print(f"\n⏹️  Busca interrompida pelo usuário")
                return None, None
            except Exception:
                continue
        
        tempo_chunk = time.time() - inicio_chunk
        chaves_chunk = chunk_end - chunk_start
        velocidade_chunk = chaves_chunk / tempo_chunk if tempo_chunk > 0 else 0
        
        print(f"❌ Chunk {chunks_processados} concluído: {chaves_chunk:,} chaves em {tempo_chunk:.2f}s ({velocidade_chunk:.0f} chaves/seg)")
    
    print(f"\n❌ CHAVE NÃO ENCONTRADA")
    print("=" * 30)
    print(f"💡 Testadas {total_chaves:,} chaves em {chunks_processados} chunks")
    
    return None, None

def main():
    if len(sys.argv) >= 3:
        endereco = sys.argv[1]
        
        if ':' in sys.argv[2]:
            start_str, end_str = sys.argv[2].split(':')
            start_range = int(start_str, 16) if any(c in start_str.lower() for c in 'abcdef') else int(start_str)
            end_range = int(end_str, 16) if any(c in end_str.lower() for c in 'abcdef') else int(end_str)
        else:
            start_range = int(sys.argv[2])
            end_range = int(sys.argv[3]) if len(sys.argv) > 3 else start_range + 1000000
        
        chunk_size = int(sys.argv[4]) if len(sys.argv) > 4 else 1000000
        
        chave, hash_errado = encontrar_chave_hibrido(endereco, start_range, end_range, chunk_size)
        
        if chave:
            print(f"\n🎊 SUCESSO!")
            print(f"Chave: {chave}")
            print(f"Hash errado: {hash_errado}")
            
            print(f"\n🚀 COMANDO PARA USAR:")
            print(f"./buscar_chave_por_hash_errado {hash_errado} {start_range}:{end_range}")
        
    else:
        print("🚀 BUSCA HÍBRIDA - PYTHON + CUDA")
        print("=" * 40)
        print("Uso: python3 encontrar_chave_hibrido.py <endereco> <start:end> [chunk_size]")
        print("")
        print("Exemplos:")
        print("python3 encontrar_chave_hibrido.py 1HsMJxNiV7TLxmoF6uJNkydxPFDog4NQum 80000:1048575")
        print("python3 encontrar_chave_hibrido.py 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2 1:10")

if __name__ == "__main__":
    main()
