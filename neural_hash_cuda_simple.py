#!/usr/bin/env python3
"""
REDE NEURAL CUDA SIMPLIFICADA
Versão robusta que funciona com qualquer versão do CuPy
"""

import numpy as np
import time
import json
import random
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed

# Tentar importar CuPy com fallback para NumPy
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("✅ CuPy disponível - GPU habilitada")
    
    # Testar operações básicas
    try:
        test_array = cp.array([1, 2, 3])
        _ = cp.sum(test_array)
        print("✅ Operações CuPy funcionando")
    except Exception as e:
        print(f"⚠️  Problema com CuPy: {e}")
        import numpy as cp
        GPU_AVAILABLE = False
        
except ImportError:
    import numpy as cp
    GPU_AVAILABLE = False
    print("⚠️  CuPy não encontrado - usando CPU")

# Importar funções necessárias
try:
    from test_magic_number import simular_gpu_errada_para_chave
    from main import private_key_to_address
    from bitcoin_conversions import calculate_target_hash160
    print("✅ Módulos Bitcoin importados")
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    exit(1)

def hex_to_vector_simple(hex_string):
    """Converte hex para vetor (função global para multiprocessing)"""
    hex_string = hex_string.ljust(40, '0')[:40]
    
    vector = np.zeros(40, dtype=np.float32)
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            vector[i] = int(char, 16) / 15.0
        else:
            vector[i] = 0.0
    
    return vector

def generate_sample_batch_simple(key_batch):
    """Gera batch de amostras"""
    inputs = []
    outputs = []
    successful_keys = []
    
    for private_key in key_batch:
        try:
            endereco = private_key_to_address(private_key)
            if not endereco:
                continue
            
            hash160_correto = calculate_target_hash160(endereco)
            if not hash160_correto:
                continue
            
            numero_magico = simular_gpu_errada_para_chave(private_key)
            if not numero_magico:
                continue
            
            input_vector = hex_to_vector_simple(hash160_correto.hex())
            output_vector = hex_to_vector_simple(numero_magico.hex())
            
            inputs.append(input_vector)
            outputs.append(output_vector)
            successful_keys.append(private_key)
            
        except Exception:
            continue
    
    return inputs, outputs, successful_keys

class SimpleNeuralNetworkCuda:
    """Rede neural simples com suporte CUDA opcional"""
    
    def __init__(self, layers=[40, 256, 512, 1024, 512, 256, 40], use_gpu=True):
        self.layers = layers
        self.use_gpu = use_gpu and GPU_AVAILABLE
        self.xp = cp if self.use_gpu else np
        
        print(f"🧠 Rede Neural Simples CUDA")
        print(f"🔧 Dispositivo: {'GPU' if self.use_gpu else 'CPU'}")
        print(f"📊 Arquitetura: {' → '.join(map(str, layers))}")
        
        # Inicializar pesos
        self.weights = []
        self.biases = []
        
        for i in range(len(layers) - 1):
            fan_in = layers[i]
            fan_out = layers[i + 1]
            
            # Inicialização Xavier
            limit = self.xp.sqrt(6.0 / (fan_in + fan_out))
            w = self.xp.random.uniform(-limit, limit, (fan_in, fan_out)).astype(self.xp.float32)
            b = self.xp.zeros((1, fan_out), dtype=self.xp.float32)
            
            self.weights.append(w)
            self.biases.append(b)
        
        total_params = sum(w.size for w in self.weights) + sum(b.size for b in self.biases)
        print(f"📊 Parâmetros: {total_params:,}")
        
        if self.use_gpu:
            try:
                # Tentar obter informações da GPU de forma segura
                device_info = "GPU disponível"
                try:
                    device = cp.cuda.Device()
                    device_info = f"GPU {device.id}"
                except:
                    pass
                print(f"🚀 {device_info}")
            except:
                print("🚀 GPU disponível (detalhes não acessíveis)")
    
    def relu(self, x):
        """ReLU activation"""
        return self.xp.maximum(0, x)
    
    def sigmoid(self, x):
        """Sigmoid activation"""
        return 1 / (1 + self.xp.exp(-self.xp.clip(x, -10, 10)))
    
    def forward(self, X):
        """Forward pass"""
        if self.use_gpu and not isinstance(X, cp.ndarray):
            X = cp.asarray(X)
        elif not self.use_gpu and isinstance(X, cp.ndarray):
            X = cp.asnumpy(X)
        
        activations = [X]
        
        for i, (w, b) in enumerate(zip(self.weights, self.biases)):
            z = self.xp.dot(activations[-1], w) + b
            
            # ReLU nas camadas ocultas, sigmoid na saída
            if i < len(self.weights) - 1:
                a = self.relu(z)
            else:
                a = self.sigmoid(z)
            
            activations.append(a)
        
        return activations
    
    def backward(self, X, y, activations, learning_rate=0.001):
        """Backward pass simplificado"""
        m = X.shape[0]
        
        # Erro da saída
        output_error = activations[-1] - y
        
        # Backpropagation simples
        errors = [output_error]
        
        for i in range(len(self.weights) - 1, 0, -1):
            error = self.xp.dot(errors[-1], self.weights[i].T)
            # Derivada ReLU
            error *= (activations[i] > 0).astype(self.xp.float32)
            errors.append(error)
        
        errors.reverse()
        
        # Atualizar pesos
        for i in range(len(self.weights)):
            dw = self.xp.dot(activations[i].T, errors[i]) / m
            db = self.xp.mean(errors[i], axis=0, keepdims=True)
            
            self.weights[i] -= learning_rate * dw
            self.biases[i] -= learning_rate * db
        
        # Calcular loss
        loss = self.xp.mean(output_error ** 2)
        return float(loss)
    
    def train(self, X, y, epochs=2000, learning_rate=0.001, batch_size=256):
        """Treinamento com batches"""
        print(f"\n🚀 TREINAMENTO {'CUDA' if self.use_gpu else 'CPU'}")
        print("=" * 30)
        
        # Converter dados para GPU se necessário
        if self.use_gpu:
            if not isinstance(X, cp.ndarray):
                X = cp.asarray(X)
            if not isinstance(y, cp.ndarray):
                y = cp.asarray(y)
        
        num_batches = len(X) // batch_size
        best_loss = float('inf')
        patience = 200
        patience_counter = 0
        
        print(f"Amostras: {len(X):,}")
        print(f"Batch size: {batch_size}")
        print(f"Batches por época: {num_batches}")
        
        for epoch in range(epochs):
            # Embaralhar dados
            if self.use_gpu:
                indices = cp.random.permutation(len(X))
            else:
                indices = np.random.permutation(len(X))
            
            X_shuffled = X[indices]
            y_shuffled = y[indices]
            
            epoch_loss = 0.0
            
            # Treinar por batches
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = start_idx + batch_size
                
                X_batch = X_shuffled[start_idx:end_idx]
                y_batch = y_shuffled[start_idx:end_idx]
                
                # Forward pass
                activations = self.forward(X_batch)
                
                # Backward pass
                batch_loss = self.backward(X_batch, y_batch, activations, learning_rate)
                epoch_loss += batch_loss
            
            epoch_loss /= num_batches
            
            # Learning rate decay
            if epoch % 300 == 0 and epoch > 0:
                learning_rate *= 0.9
            
            # Early stopping
            if epoch_loss < best_loss:
                best_loss = epoch_loss
                patience_counter = 0
            else:
                patience_counter += 1
            
            if patience_counter >= patience:
                print(f"\n⏰ Early stopping na época {epoch+1}")
                break
            
            # Log progresso
            if (epoch + 1) % 100 == 0:
                print(f"Época {epoch+1:4d}/{epochs} | "
                      f"Loss: {epoch_loss:.6f} | "
                      f"LR: {learning_rate:.6f} | "
                      f"Best: {best_loss:.6f}")
        
        print(f"\n✅ Treinamento concluído!")
        print(f"📈 Melhor loss: {best_loss:.6f}")
        
        return best_loss
    
    def predict(self, X):
        """Predição"""
        activations = self.forward(X)
        result = activations[-1]
        
        # Converter para CPU se necessário
        if self.use_gpu and isinstance(result, cp.ndarray):
            result = cp.asnumpy(result)
        
        return result

class SimpleCudaHashPredictor:
    """Preditor simples com CUDA"""
    
    def __init__(self):
        self.model = None
        self.use_gpu = GPU_AVAILABLE
        
        print(f"🚀 Simple CUDA Hash Predictor")
        print(f"🔧 GPU: {'Habilitada' if self.use_gpu else 'Desabilitada'}")
    
    def vector_to_hex(self, vector):
        """Converte vetor para hex"""
        hex_chars = '0123456789abcdef'
        hex_string = ''
        
        for val in vector:
            idx = int(round(float(val) * 15))
            idx = max(0, min(15, idx))
            hex_string += hex_chars[idx]
        
        return hex_string
    
    def generate_dataset(self, num_samples=20000, start_key=1, end_key=500000):
        """Gera dataset com processamento paralelo"""
        print(f"🚀 GERANDO DATASET: {num_samples:,} AMOSTRAS")
        print("=" * 40)
        
        # Chaves aleatórias
        all_keys = random.sample(range(start_key, end_key + 1), 
                                min(num_samples, end_key - start_key + 1))
        
        # Processamento paralelo
        num_processes = min(mp.cpu_count(), 8)
        batch_size = len(all_keys) // num_processes
        
        key_batches = [all_keys[i:i + batch_size] 
                      for i in range(0, len(all_keys), batch_size)]
        
        print(f"🔄 Processamento: {num_processes} processos")
        
        all_inputs = []
        all_outputs = []
        
        start_time = time.time()
        
        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            futures = [executor.submit(generate_sample_batch_simple, batch) 
                      for batch in key_batches]
            
            for i, future in enumerate(as_completed(futures)):
                try:
                    inputs, outputs, keys = future.result()
                    all_inputs.extend(inputs)
                    all_outputs.extend(outputs)
                    
                    print(f"   Batch {i+1}/{len(key_batches)}: {len(inputs)} amostras")
                    
                except Exception as e:
                    print(f"   ❌ Erro batch {i+1}: {e}")
        
        generation_time = time.time() - start_time
        
        print(f"\n📊 DATASET GERADO:")
        print(f"✅ Amostras: {len(all_inputs):,}")
        print(f"⏱️  Tempo: {generation_time:.1f}s")
        print(f"⚡ Velocidade: {len(all_inputs) / generation_time:.0f} amostras/s")
        
        if len(all_inputs) == 0:
            return None, None
        
        return np.array(all_inputs, dtype=np.float32), np.array(all_outputs, dtype=np.float32)
    
    def run_training(self, num_samples=20000):
        """Pipeline completo"""
        print("🚀 SIMPLE CUDA HASH PREDICTOR")
        print("=" * 35)
        
        total_start = time.time()
        
        # 1. Gerar dataset
        X, y = self.generate_dataset(num_samples=num_samples)
        
        if X is None:
            print("❌ Falha na geração do dataset")
            return 0
        
        # 2. Dividir dados
        test_size = min(1000, len(X) // 10)
        
        indices = np.random.permutation(len(X))
        X_test = X[indices[:test_size]]
        y_test = y[indices[:test_size]]
        X_train = X[indices[test_size:]]
        y_train = y[indices[test_size:]]
        
        print(f"\n📊 DIVISÃO:")
        print(f"Treino: {len(X_train):,}")
        print(f"Teste: {len(X_test):,}")
        
        # 3. Criar e treinar modelo
        self.model = SimpleNeuralNetworkCuda(use_gpu=self.use_gpu)
        self.model.train(X_train, y_train, epochs=1500)
        
        # 4. Avaliar
        predictions = self.model.predict(X_test)
        
        # Calcular acurácia
        accuracies = []
        for i in range(min(5, len(predictions))):
            hash_real = self.vector_to_hex(y_test[i])
            hash_pred = self.vector_to_hex(predictions[i])
            
            correct_chars = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            accuracies.append(accuracy)
            
            print(f"\nAmostra {i+1}:")
            print(f"  Real:     {hash_real}")
            print(f"  Predito:  {hash_pred}")
            print(f"  Acurácia: {accuracy:.1f}%")
        
        avg_accuracy = np.mean(accuracies) if accuracies else 0
        
        total_time = time.time() - total_start
        
        print(f"\n🎊 TREINAMENTO CONCLUÍDO!")
        print(f"⏱️  Tempo total: {total_time:.1f} segundos")
        print(f"📈 Acurácia: {avg_accuracy:.1f}%")
        
        return avg_accuracy
    
    def predict_magic_number(self, hash160_correto_hex):
        """Predição"""
        if self.model is None:
            print("❌ Modelo não treinado!")
            return None
        
        input_vector = hex_to_vector_simple(hash160_correto_hex).reshape(1, -1)
        prediction = self.model.predict(input_vector)
        
        return self.vector_to_hex(prediction[0])

def main():
    """Função principal"""
    print("🚀 SIMPLE CUDA HASH PREDICTOR")
    print("=" * 35)
    
    # Verificar status
    if GPU_AVAILABLE:
        print("✅ CUDA disponível e funcionando")
    else:
        print("⚠️  Usando CPU (CUDA não disponível)")
    
    # Configuração
    num_samples = int(input("Número de amostras (recomendado: 20000): ") or "20000")
    
    # Executar
    predictor = SimpleCudaHashPredictor()
    accuracy = predictor.run_training(num_samples=num_samples)
    
    if accuracy > 0:
        # Teste interativo
        print(f"\n🧪 TESTE INTERATIVO:")
        while True:
            hash_input = input("\nDigite hash160 correto (40 chars) ou 'quit': ").strip()
            
            if hash_input.lower() == 'quit':
                break
            
            if len(hash_input) != 40:
                print("❌ Hash deve ter 40 caracteres")
                continue
            
            magic_number = predictor.predict_magic_number(hash_input)
            print(f"Número mágico: {magic_number}")
            print(f"🚀 Comando: ./buscar_chave_por_hash_errado {magic_number} 1:1000000")

if __name__ == "__main__":
    main()
