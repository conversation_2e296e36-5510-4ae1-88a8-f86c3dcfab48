#!/usr/bin/env python3
"""
DESCOBRIR HASH ERRADO - SISTEMA COMPLETO
Encontra hash errado, valida tudo e envia para Telegram
"""

import time
import requests
import json
import hashlib
import base58
from main import (
    private_key_to_address, 
    simular_gpu_errada_para_chave,
    calculate_target_hash160
)

class SistemaCompleto:
    def __init__(self, endereco_alvo, telegram_config=None):
        self.endereco_alvo = endereco_alvo
        self.hash160_correto = calculate_target_hash160(endereco_alvo)
        self.telegram_config = telegram_config
        
    def private_key_to_wif(self, private_key_int, compressed=True):
        """Converte chave privada para formato WIF"""
        # Converter para bytes (32 bytes)
        private_key_bytes = private_key_int.to_bytes(32, byteorder='big')
        
        # Adicionar prefixo da mainnet (0x80)
        extended_key = b'\x80' + private_key_bytes
        
        # Adicionar sufixo se compressed
        if compressed:
            extended_key += b'\x01'
        
        # Calcular checksum (duplo SHA256)
        hash1 = hashlib.sha256(extended_key).digest()
        hash2 = hashlib.sha256(hash1).digest()
        checksum = hash2[:4]
        
        # Adicionar checksum
        final_key = extended_key + checksum
        
        # Codificar em Base58
        wif = base58.b58encode(final_key).decode('utf-8')
        
        return wif
    
    def validar_chave_completa(self, chave_privada):
        """Validação completa da chave privada encontrada"""
        print(f"\n🔍 VALIDAÇÃO COMPLETA DA CHAVE PRIVADA")
        print("="*60)
        
        try:
            # 1. Gerar endereço
            endereco_gerado = private_key_to_address(chave_privada)
            print(f"🏠 Endereço gerado: {endereco_gerado}")
            
            # 2. Verificar se bate com o alvo
            endereco_correto = endereco_gerado == self.endereco_alvo
            print(f"✅ Endereço correto: {'SIM' if endereco_correto else 'NÃO'}")
            
            if not endereco_correto:
                print(f"❌ ERRO: Endereço não bate!")
                print(f"   Esperado: {self.endereco_alvo}")
                print(f"   Gerado:   {endereco_gerado}")
                return None
            
            # 3. Calcular hash160 correto
            hash160_correto_calc = calculate_target_hash160(endereco_gerado)
            print(f"🎯 Hash160 correto: {hash160_correto_calc.hex()}")
            
            # 4. Calcular hash160 errado
            hash160_errado = simular_gpu_errada_para_chave(chave_privada)
            print(f"🎩 Hash160 errado:  {hash160_errado.hex()}")
            
            # 5. Gerar WIF (compressed e uncompressed)
            wif_compressed = self.private_key_to_wif(chave_privada, compressed=True)
            wif_uncompressed = self.private_key_to_wif(chave_privada, compressed=False)
            print(f"🔑 WIF (compressed):   {wif_compressed}")
            print(f"🔑 WIF (uncompressed): {wif_uncompressed}")
            
            # 6. Informações adicionais
            print(f"🔢 Chave privada (decimal): {chave_privada}")
            print(f"🔢 Chave privada (hex):     0x{chave_privada:x}")
            print(f"🔢 Chave privada (64 hex):  {chave_privada:064x}")
            
            # Resultado completo
            resultado = {
                'chave_privada_decimal': chave_privada,
                'chave_privada_hex': f"0x{chave_privada:x}",
                'chave_privada_64hex': f"{chave_privada:064x}",
                'endereco': endereco_gerado,
                'hash160_correto': hash160_correto_calc.hex(),
                'hash160_errado': hash160_errado.hex(),
                'wif_compressed': wif_compressed,
                'wif_uncompressed': wif_uncompressed,
                'endereco_alvo': self.endereco_alvo,
                'validacao_ok': True
            }
            
            print(f"\n✅ VALIDAÇÃO COMPLETA PASSOU!")
            return resultado
            
        except Exception as e:
            print(f"❌ Erro na validação: {e}")
            return None
    
    def enviar_telegram(self, resultado):
        """Envia resultado para Telegram"""
        if not self.telegram_config:
            print("⚠️  Configuração do Telegram não fornecida")
            return False
        
        try:
            print(f"\n📱 ENVIANDO PARA TELEGRAM...")
            
            # Montar mensagem
            mensagem = f"""
🎉 CHAVE PRIVADA BITCOIN ENCONTRADA! 🎉

🎯 CARTEIRA ALVO:
{resultado['endereco_alvo']}

🔑 CHAVE PRIVADA:
• Decimal: {resultado['chave_privada_decimal']}
• Hex: {resultado['chave_privada_hex']}
• 64-Hex: {resultado['chave_privada_64hex']}

🏠 ENDEREÇO GERADO:
{resultado['endereco']}

🔐 WIF (WALLET IMPORT FORMAT):
• Compressed: {resultado['wif_compressed']}
• Uncompressed: {resultado['wif_uncompressed']}

🎯 HASHES:
• Hash160 Correto: {resultado['hash160_correto']}
• Hash160 Errado: {resultado['hash160_errado']}

⏰ Encontrado em: {time.strftime('%Y-%m-%d %H:%M:%S')}

🎊 PARABÉNS! Você quebrou uma carteira Bitcoin!
            """.strip()
            
            # Enviar via API do Telegram
            url = f"https://api.telegram.org/bot{self.telegram_config['bot_token']}/sendMessage"
            
            payload = {
                'chat_id': self.telegram_config['chat_id'],
                'text': mensagem,
                'parse_mode': 'HTML'
            }
            
            response = requests.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ Mensagem enviada para Telegram com sucesso!")
                return True
            else:
                print(f"❌ Erro ao enviar para Telegram: {response.status_code}")
                print(f"   Resposta: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Erro ao enviar Telegram: {e}")
            return False
    
    def salvar_resultado_local(self, resultado):
        """Salva resultado em arquivo local"""
        try:
            filename = f"chave_encontrada_{int(time.time())}.json"
            
            # Adicionar timestamp
            resultado['timestamp'] = time.time()
            resultado['data_hora'] = time.strftime('%Y-%m-%d %H:%M:%S')
            
            with open(filename, 'w') as f:
                json.dump(resultado, f, indent=2)
            
            print(f"💾 Resultado salvo em: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ Erro ao salvar arquivo: {e}")
            return None
    
    def buscar_chave_no_range(self, start_range, end_range, chunk_size=1000000):
        """Busca a chave privada no range especificado"""
        print(f"🔍 BUSCANDO CHAVE PRIVADA NO RANGE")
        print(f"   Range: {start_range:,} - {end_range:,}")
        print(f"   Chunk size: {chunk_size:,}")
        
        inicio_busca = time.time()
        tentativas = 0
        
        for chave_privada in range(start_range, end_range + 1):
            try:
                # Gerar endereço para esta chave privada
                endereco_gerado = private_key_to_address(chave_privada)
                
                if endereco_gerado == self.endereco_alvo:
                    tempo_busca = time.time() - inicio_busca
                    
                    print(f"\n🎉 CHAVE PRIVADA ENCONTRADA!")
                    print(f"   Chave: {chave_privada}")
                    print(f"   Tempo de busca: {tempo_busca:.2f} segundos")
                    print(f"   Tentativas: {tentativas:,}")
                    
                    # Validação completa
                    resultado = self.validar_chave_completa(chave_privada)
                    
                    if resultado:
                        # Salvar localmente
                        self.salvar_resultado_local(resultado)
                        
                        # Enviar para Telegram
                        self.enviar_telegram(resultado)
                        
                        return resultado
                    
                tentativas += 1
                
                # Progresso a cada chunk
                if tentativas % chunk_size == 0:
                    tempo_decorrido = time.time() - inicio_busca
                    velocidade = tentativas / tempo_decorrido
                    progresso = ((chave_privada - start_range) / (end_range - start_range)) * 100
                    
                    print(f"   Progresso: {progresso:.3f}% | Tentativas: {tentativas:,} | Velocidade: {velocidade:.0f} chaves/seg")
                    
            except Exception as e:
                continue
        
        print(f"❌ Chave não encontrada no range especificado")
        return None

def configurar_telegram():
    """Configura as credenciais do Telegram"""
    print("📱 CONFIGURAÇÃO DO TELEGRAM")
    print("="*40)
    print("Para receber notificações no Telegram, você precisa:")
    print("1. Criar um bot no @BotFather")
    print("2. Obter o token do bot")
    print("3. Obter seu chat_id")
    print()
    
    usar_telegram = input("Configurar Telegram? (s/N): ").lower().startswith('s')
    
    if not usar_telegram:
        return None
    
    bot_token = input("Token do bot: ").strip()
    chat_id = input("Chat ID: ").strip()
    
    if bot_token and chat_id:
        config = {
            'bot_token': bot_token,
            'chat_id': chat_id
        }
        
        # Teste rápido
        try:
            url = f"https://api.telegram.org/bot{bot_token}/getMe"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print("✅ Configuração do Telegram válida!")
                return config
            else:
                print("❌ Token inválido")
                return None
        except:
            print("❌ Erro ao testar configuração")
            return None
    
    return None

def main():
    """Função principal"""
    print("🎯 SISTEMA COMPLETO - DESCOBRIR E VALIDAR CHAVE BITCOIN")
    print("="*60)
    
    # Configuração da carteira alvo
    endereco_alvo = "**********************************"
    
    print(f"🎯 Carteira alvo: {endereco_alvo}")
    
    # Configurar Telegram
    telegram_config = configurar_telegram()
    
    # Criar sistema
    sistema = SistemaCompleto(endereco_alvo, telegram_config)
    
    # Configurar range de busca
    print(f"\n📊 CONFIGURAÇÃO DO RANGE DE BUSCA")
    print("="*40)
    
    usar_range_completo = input("Usar range completo 400000000000000000:7fffffffffffffffff? (s/N): ").lower().startswith('s')
    
    if usar_range_completo:
        start_range = 4611686018427387904  # 0x400000000000000000
        end_range = 9223372036854775807    # 0x7fffffffffffffffff
        print(f"⚠️  ATENÇÃO: Range muito grande! Pode levar dias/semanas")
    else:
        print("Ranges sugeridos:")
        print("1. Teste rápido: 1 - 1,000,000")
        print("2. Range médio: 1,000,000 - 100,000,000")
        print("3. Range personalizado")
        
        opcao = input("Escolha (1/2/3): ").strip()
        
        if opcao == "1":
            start_range, end_range = 1, 1000000
        elif opcao == "2":
            start_range, end_range = 1000000, 100000000
        else:
            start_range = int(input("Range início: "))
            end_range = int(input("Range fim: "))
    
    print(f"\n🚀 INICIANDO BUSCA...")
    print(f"Range: {start_range:,} - {end_range:,}")
    
    # Executar busca
    resultado = sistema.buscar_chave_no_range(start_range, end_range)
    
    if resultado:
        print(f"\n🎊 MISSÃO CUMPRIDA!")
        print(f"Chave privada da carteira {endereco_alvo} foi encontrada!")
    else:
        print(f"\n😞 Chave não encontrada no range especificado")
        print(f"Tente um range diferente ou use o programa CUDA para maior velocidade")

if __name__ == "__main__":
    main()
