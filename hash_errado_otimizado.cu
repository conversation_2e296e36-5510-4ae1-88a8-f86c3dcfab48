/*
HASH ERRADO OTIMIZADO - Versões Simplificadas
Diferentes níveis de simplificação para máxima velocidade
*/

#include <cuda_runtime.h>
#include <stdint.h>

// VERSÃO 1: Hash errado ultra-simplificado (ma<PERSON>)
__device__ __forceinline__ void hash_errado_v1(uint64_t pk, uint8_t* hash) {
    // Apenas 3 operações por byte
    #pragma unroll
    for (int i = 0; i < 20; i++) {
        hash[i] = ((pk >> (i & 7)) ^ (pk * (i + 1)) ^ ((pk + i) * 0x9E3779B9)) & 0xFF;
    }
}

// VERSÃO 2: Hash errado com lookup table
__constant__ uint8_t lookup_table[256] = {
    // Pré-computar transformações comuns
    0x00, 0x9E, 0x3C, 0xA2, 0x78, 0xE6, 0x44, 0xDA,
    0xF0, 0x6E, 0xCC, 0x52, 0x88, 0x16, 0xB4, 0x2A,
    // ... (continuar com 256 valores)
};

__device__ __forceinline__ void hash_errado_v2(uint64_t pk, uint8_t* hash) {
    #pragma unroll
    for (int i = 0; i < 20; i++) {
        uint8_t base = (pk >> (i & 7)) & 0xFF;
        hash[i] = lookup_table[base ^ (i & 0xFF)];
    }
}

// VERSÃO 3: Hash errado com SIMD (4 bytes por vez)
__device__ __forceinline__ void hash_errado_v3(uint64_t pk, uint32_t* hash_32) {
    // Processar 4 bytes por vez
    #pragma unroll
    for (int i = 0; i < 5; i++) {  // 20 bytes / 4 = 5 iterações
        uint32_t val = (uint32_t)(pk >> (i * 8));
        hash_32[i] = val ^ (val << 8) ^ (val >> 8) ^ (pk * (i + 1));
    }
}

// VERSÃO 4: Hash errado mínimo (apenas 2 operações)
__device__ __forceinline__ void hash_errado_minimo(uint64_t pk, uint64_t* hash_64) {
    // Apenas 2 operações de 64-bit para 16 bytes
    hash_64[0] = pk ^ (pk << 13) ^ (pk >> 51);
    hash_64[1] = (pk * 0x9E3779B9) ^ (pk << 21) ^ (pk >> 43);
    
    // Últimos 4 bytes
    uint32_t* last = (uint32_t*)&hash_64[2];
    *last = (uint32_t)((pk ^ (pk >> 32)) & 0xFFFFFFFF);
}

// VERSÃO 5: Hash errado com bit manipulation tricks
__device__ __forceinline__ void hash_errado_bits(uint64_t pk, uint8_t* hash) {
    // Usar operações de bit ultra-rápidas
    uint64_t h1 = pk;
    uint64_t h2 = __brevll(pk);  // Reverse bits (GPU instruction)
    uint64_t h3 = __popcll(pk);  // Population count
    
    #pragma unroll
    for (int i = 0; i < 20; i++) {
        hash[i] = ((h1 >> (i * 3)) ^ (h2 << (i * 2)) ^ (h3 + i)) & 0xFF;
    }
}

// Benchmark das versões
__global__ void benchmark_hash_versions(uint64_t start, uint64_t count, int version) {
    uint64_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    uint64_t stride = gridDim.x * blockDim.x;
    
    for (uint64_t i = idx; i < count; i += stride) {
        uint64_t pk = start + i;
        
        switch (version) {
            case 1: {
                uint8_t hash[20];
                hash_errado_v1(pk, hash);
                break;
            }
            case 2: {
                uint8_t hash[20];
                hash_errado_v2(pk, hash);
                break;
            }
            case 3: {
                uint32_t hash[5];
                hash_errado_v3(pk, hash);
                break;
            }
            case 4: {
                uint64_t hash[3];
                hash_errado_minimo(pk, hash);
                break;
            }
            case 5: {
                uint8_t hash[20];
                hash_errado_bits(pk, hash);
                break;
            }
        }
    }
}

// Teste de velocidade
void testar_velocidades() {
    printf("🏃 BENCHMARK DAS VERSÕES DE HASH ERRADO\n");
    printf("======================================\n");
    
    uint64_t num_testes = 10000000;  // 10M testes
    int threads = 256;
    int blocks = 1024;
    
    const char* nomes[] = {"", "Ultra-Simples", "Lookup Table", "SIMD 32-bit", "Mínimo 64-bit", "Bit Tricks"};
    
    for (int version = 1; version <= 5; version++) {
        printf("\n🧪 Testando versão %d: %s\n", version, nomes[version]);
        
        cudaEvent_t start, stop;
        cudaEventCreate(&start);
        cudaEventCreate(&stop);
        
        cudaEventRecord(start);
        benchmark_hash_versions<<<blocks, threads>>>(1, num_testes, version);
        cudaEventRecord(stop);
        
        cudaEventSynchronize(stop);
        
        float tempo_ms;
        cudaEventElapsedTime(&tempo_ms, start, stop);
        
        double tempo_s = tempo_ms / 1000.0;
        double velocidade = num_testes / tempo_s;
        
        printf("   Tempo: %.3f ms\n", tempo_ms);
        printf("   Velocidade: %.0f hashes/segundo\n", velocidade);
        printf("   Velocidade: %.2f M hashes/segundo\n", velocidade / 1000000.0);
        
        cudaEventDestroy(start);
        cudaEventDestroy(stop);
    }
}

int main() {
    printf("⚡ OTIMIZAÇÃO DE HASH ERRADO\n");
    printf("===========================\n");
    
    // Verificar GPU
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, 0);
    printf("GPU: %s\n", prop.name);
    printf("Compute Capability: %d.%d\n", prop.major, prop.minor);
    
    // Executar benchmark
    testar_velocidades();
    
    printf("\n📊 RECOMENDAÇÕES:\n");
    printf("• Versão 4 (Mínimo 64-bit): Mais rápida para comparação\n");
    printf("• Versão 1 (Ultra-Simples): Melhor compatibilidade\n");
    printf("• Versão 5 (Bit Tricks): Para GPUs modernas\n");
    
    return 0;
}
