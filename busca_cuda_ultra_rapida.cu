/*
BUSCA CUDA ULTRA-RÁPIDA - Hash Errado
Programa CUDA otimizado para encontrar hash errado com máxima velocidade
*/

#include <cuda_runtime.h>
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <time.h>
#include <chrono>
#include <thread>

#ifdef _WIN32
    #include <windows.h>
    #define sleep_ms(ms) Sleep(ms)
#else
    #include <unistd.h>
    #define sleep_ms(ms) usleep((ms) * 1000)
#endif

// Constantes do algoritmo
#define GOLDEN_RATIO 0x9E3779B9ULL
#define MULT_CONST 0x9E
#define HASH_SIZE 20

// Coordenadas do ponto gerador secp256k1
__constant__ uint64_t gx[4] = {
    0x79BE667EF9DCBBACULL, 0x55A06295CE870B07ULL, 
    0x029BFCDB2DCE28D9ULL, 0x59F2815B16F81798ULL
};

// Resultado global
__device__ volatile uint64_t d_result = 0;
__device__ volatile int d_found = 0;

__device__ void calcular_hash_errado_gpu(uint64_t private_key, uint8_t* hash_errado) {
    // PASSO 1: Transformar coordenadas (versão otimizada)
    uint64_t px[4];
    
    #pragma unroll
    for (int i = 0; i < 4; i++) {
        uint64_t rotacao = (private_key << (i * 8)) | (private_key >> (56 - i * 8));
        px[i] = gx[i] ^ rotacao;
        px[i] = ((px[i] << 1) ^ (px[i] >> 63));
    }
    
    // PASSO 2: Construir chave pública simplificada
    uint8_t y_parity = 2 + ((private_key ^ px[0]) & 1);
    
    // PASSO 3: Gerar hash errado (versão ultra-otimizada)
    #pragma unroll
    for (int i = 0; i < HASH_SIZE; i++) {
        uint32_t hash_val = 0;
        
        // Simular influência da chave pública (otimizado)
        hash_val ^= (y_parity + i) & 0xFF;
        
        // Adicionar bytes das coordenadas px (otimizado)
        for (int j = 0; j < 4; j++) {
            for (int k = 0; k < 8; k++) {
                uint8_t coord_byte = (px[j] >> (56 - k * 8)) & 0xFF;
                hash_val ^= (coord_byte + i + j * 8 + k + 1) & 0xFF;
            }
        }
        
        // Operações baseadas na chave privada
        hash_val ^= (private_key >> (i & 7)) & 0xFF;
        hash_val ^= (private_key >> ((i + 8) & 15)) & 0xFF;
        hash_val ^= (private_key * (i + 1)) & 0xFF;
        hash_val ^= ((private_key + i) * GOLDEN_RATIO) & 0xFF;
        
        // Transformação final
        hash_val &= 0xFF;
        hash_errado[i] = ((hash_val * MULT_CONST) ^ (hash_val >> 4)) & 0xFF;
    }
}

__global__ void busca_hash_errado_kernel(
    uint64_t start_range, 
    uint64_t end_range,
    uint8_t* target_hash,
    uint64_t* stats_counter
) {
    // Calcular índice da thread
    uint64_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    uint64_t stride = gridDim.x * blockDim.x;
    
    // Buffer local para hash calculado
    uint8_t hash_calculado[HASH_SIZE];
    
    // Loop principal - cada thread testa múltiplas chaves
    for (uint64_t private_key = start_range + idx; 
         private_key < end_range && !d_found; 
         private_key += stride) {
        
        // Calcular hash errado para esta chave privada
        calcular_hash_errado_gpu(private_key, hash_calculado);
        
        // Comparação otimizada (64-bit chunks)
        bool match = true;
        
        // Comparar em chunks de 64 bits para velocidade
        uint64_t* calc_64 = (uint64_t*)hash_calculado;
        uint64_t* target_64 = (uint64_t*)target_hash;
        
        if (calc_64[0] != target_64[0] || 
            calc_64[1] != target_64[1] ||
            ((uint32_t*)hash_calculado)[4] != ((uint32_t*)target_hash)[4]) {
            match = false;
        }
        
        // Se encontrou match
        if (match) {
            // Usar atomic para evitar race condition
            if (atomicCAS((unsigned long long*)&d_result, 0ULL, private_key) == 0ULL) {
                d_found = 1;
                printf("GPU ENCONTROU! Chave: %llu (0x%llx)\n", private_key, private_key);
            }
        }
        
        // Atualizar contador de estatísticas (ocasionalmente)
        if ((private_key & 0xFFFFF) == 0) {  // A cada ~1M tentativas
            atomicAdd((unsigned long long*)stats_counter, 0x100000ULL);
        }
    }
}

class BuscaCUDA {
private:
    uint8_t* d_target_hash;
    uint64_t* d_stats_counter;
    uint64_t h_stats_counter;
    
public:
    bool inicializar(const char* target_hash_hex) {
        printf("🚀 INICIALIZANDO BUSCA CUDA ULTRA-RÁPIDA\n");
        printf("Target hash: %s\n", target_hash_hex);
        
        // Converter hex string para bytes
        uint8_t target_hash[HASH_SIZE];
        for (int i = 0; i < HASH_SIZE; i++) {
            sscanf(target_hash_hex + i * 2, "%2hhx", &target_hash[i]);
        }
        
        // Alocar memória GPU
        cudaMalloc(&d_target_hash, HASH_SIZE);
        cudaMalloc(&d_stats_counter, sizeof(uint64_t));
        
        // Copiar dados para GPU
        cudaMemcpy(d_target_hash, target_hash, HASH_SIZE, cudaMemcpyHostToDevice);
        
        // Inicializar contadores
        h_stats_counter = 0;
        cudaMemcpy(d_stats_counter, &h_stats_counter, sizeof(uint64_t), cudaMemcpyHostToDevice);
        
        // Reset resultado global
        uint64_t zero = 0;
        int zero_int = 0;
        cudaMemcpyToSymbol(d_result, &zero, sizeof(uint64_t));
        cudaMemcpyToSymbol(d_found, &zero_int, sizeof(int));
        
        printf("✅ CUDA inicializado com sucesso!\n");
        return true;
    }
    
    uint64_t buscar(uint64_t start_range, uint64_t end_range) {
        printf("\n🔍 INICIANDO BUSCA CUDA\n");
        printf("Range: %llu - %llu (%llu chaves)\n", start_range, end_range, end_range - start_range);
        
        // Configuração otimizada para máxima velocidade
        int threads_per_block = 1024;  // Máximo para a maioria das GPUs
        int blocks_per_grid = 65535;   // Máximo
        
        printf("Configuração: %d threads/block, %d blocks\n", threads_per_block, blocks_per_grid);
        printf("Total threads simultâneas: %d\n", threads_per_block * blocks_per_grid);
        
        // Timing
        clock_t inicio = clock();
        cudaEvent_t start, stop;
        cudaEventCreate(&start);
        cudaEventCreate(&stop);
        
        cudaEventRecord(start);
        
        // Lançar kernel
        busca_hash_errado_kernel<<<blocks_per_grid, threads_per_block>>>(
            start_range, end_range, d_target_hash, d_stats_counter
        );
        
        // Monitorar progresso
        uint64_t resultado = 0;
        int encontrado = 0;
        uint64_t tentativas_anteriores = 0;
        
        while (!encontrado) {
            cudaDeviceSynchronize();
            
            // Verificar se encontrou
            cudaMemcpyFromSymbol(&encontrado, d_found, sizeof(int));
            if (encontrado) {
                cudaMemcpyFromSymbol(&resultado, d_result, sizeof(uint64_t));
                break;
            }
            
            // Mostrar progresso
            cudaMemcpy(&h_stats_counter, d_stats_counter, sizeof(uint64_t), cudaMemcpyDeviceToHost);
            
            if (h_stats_counter > tentativas_anteriores) {
                clock_t agora = clock();
                double tempo_decorrido = ((double)(agora - inicio)) / CLOCKS_PER_SEC;
                double velocidade = h_stats_counter / tempo_decorrido;
                
                printf("Progresso: %llu tentativas | %.0f chaves/seg | %.1fs\n", 
                       h_stats_counter, velocidade, tempo_decorrido);
                
                tentativas_anteriores = h_stats_counter;
            }
            
            // Verificar se terminou o range
            cudaError_t status = cudaDeviceSynchronize();
            if (status == cudaSuccess) {
                // Kernel terminou sem encontrar
                break;
            }
            
            // Pequena pausa para não sobrecarregar
            sleep_ms(100);  // 100ms
        }
        
        cudaEventRecord(stop);
        cudaEventSynchronize(stop);
        
        float tempo_gpu_ms;
        cudaEventElapsedTime(&tempo_gpu_ms, start, stop);
        
        clock_t fim = clock();
        double tempo_total = ((double)(fim - inicio)) / CLOCKS_PER_SEC;
        
        printf("\n📊 ESTATÍSTICAS FINAIS:\n");
        printf("Tempo total: %.2f segundos\n", tempo_total);
        printf("Tempo GPU: %.2f segundos\n", tempo_gpu_ms / 1000.0);
        printf("Tentativas: %llu\n", h_stats_counter);
        
        if (h_stats_counter > 0 && tempo_total > 0) {
            double velocidade_final = h_stats_counter / tempo_total;
            printf("Velocidade média: %.0f chaves/segundo\n", velocidade_final);
            printf("Velocidade: %.2f M chaves/segundo\n", velocidade_final / 1000000.0);
        }
        
        if (encontrado) {
            printf("✅ CHAVE ENCONTRADA: %llu (0x%llx)\n", resultado, resultado);
        } else {
            printf("❌ Chave não encontrada no range especificado\n");
        }
        
        cudaEventDestroy(start);
        cudaEventDestroy(stop);
        
        return encontrado ? resultado : 0;
    }
    
    ~BuscaCUDA() {
        if (d_target_hash) cudaFree(d_target_hash);
        if (d_stats_counter) cudaFree(d_stats_counter);
    }
};

int main(int argc, char* argv[]) {
    printf("🚀 BUSCA CUDA ULTRA-RÁPIDA - HASH ERRADO\n");
    printf("==========================================\n");
    
    // Verificar argumentos
    if (argc < 2) {
        printf("Uso: %s <hash_errado_hex> [start_range] [end_range]\n", argv[0]);
        printf("Exemplo: %s 36df2f22295784ab7f81989f9247bfd99bb00c03\n", argv[0]);
        return 1;
    }
    
    const char* target_hash = argv[1];
    uint64_t start_range = (argc > 2) ? strtoull(argv[2], NULL, 0) : 1;
    uint64_t end_range = (argc > 3) ? strtoull(argv[3], NULL, 0) : 100000000;  // 100M por padrão
    
    // Verificar CUDA
    int device_count;
    cudaGetDeviceCount(&device_count);
    if (device_count == 0) {
        printf("❌ Nenhuma GPU CUDA encontrada!\n");
        return 1;
    }
    
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, 0);
    printf("GPU: %s\n", prop.name);
    printf("Compute Capability: %d.%d\n", prop.major, prop.minor);
    printf("Multiprocessors: %d\n", prop.multiProcessorCount);
    printf("Max threads per block: %d\n", prop.maxThreadsPerBlock);
    
    // Inicializar e executar busca
    BuscaCUDA busca;
    if (!busca.inicializar(target_hash)) {
        printf("❌ Erro ao inicializar CUDA\n");
        return 1;
    }
    
    uint64_t resultado = busca.buscar(start_range, end_range);
    
    if (resultado > 0) {
        printf("\n🎉 SUCESSO! Chave privada encontrada: %llu\n", resultado);
        return 0;
    } else {
        printf("\n❌ Chave não encontrada no range especificado\n");
        return 1;
    }
}
