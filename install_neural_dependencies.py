#!/usr/bin/env python3
"""
INSTALADOR DE DEPENDÊNCIAS PARA REDE NEURAL
Instala todas as bibliotecas necessárias para o projeto
"""

import subprocess
import sys
import importlib

def install_package(package):
    """Instala um pacote usando pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_package(package_name, import_name=None):
    """Verifica se um pacote está instalado"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def main():
    """Instala todas as dependências necessárias"""
    print("🔧 INSTALADOR DE DEPENDÊNCIAS - REDE NEURAL HASH")
    print("=" * 55)
    
    # Lista de dependências
    dependencies = [
        ("tensorflow", "tensorflow"),
        ("numpy", "numpy"),
        ("matplotlib", "matplotlib"),
        ("scikit-learn", "sklearn"),
        ("pandas", "pandas")
    ]
    
    print("📦 Verificando dependências...")
    
    to_install = []
    
    for package, import_name in dependencies:
        if check_package(package, import_name):
            print(f"✅ {package} - já instalado")
        else:
            print(f"❌ {package} - precisa instalar")
            to_install.append(package)
    
    if not to_install:
        print("\n🎉 Todas as dependências já estão instaladas!")
        return
    
    print(f"\n📥 Instalando {len(to_install)} pacotes...")
    
    for package in to_install:
        print(f"\n🔄 Instalando {package}...")
        
        if install_package(package):
            print(f"✅ {package} instalado com sucesso")
        else:
            print(f"❌ Erro ao instalar {package}")
    
    print(f"\n🎊 Instalação concluída!")
    print(f"\n🚀 Agora você pode executar:")
    print("python3 neural_network_hash_predictor.py")
    print("python3 lstm_hash_predictor.py")

if __name__ == "__main__":
    main()
