#!/usr/bin/env python3
"""
VERIFICAR NÚMERO MÁGICO
Testa se o número mágico hardcoded é real ou inventado
"""

from main import simular_gpu_errada_para_chave, private_key_to_address

def verificar_numero_magico_hardcoded():
    """
    Verifica se o número mágico hardcoded é real
    """
    print("🔍 VERIFICANDO NÚMERO MÁGICO HARDCODED")
    print("=" * 50)
    
    endereco_alvo = "1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU"
    numero_magico_hardcoded = "15dcad75ce214766086340311434d412874c7e77"
    
    print(f"Endereço alvo: {endereco_alvo}")
    print(f"Número mágico hardcoded: {numero_magico_hardcoded}")
    
    print(f"\n🧪 TESTE: Buscar chave que gera este número mágico")
    print("=" * 50)
    
    # Testar algumas chaves conhecidas
    chaves_teste = [1, 2, 3, 4, 5, 10, 100, 1000, 10000, 100000, 1000000]
    
    for chave in chaves_teste:
        hash_errado = simular_gpu_errada_para_chave(chave)
        hash_errado_hex = hash_errado.hex()
        
        print(f"Chave {chave:8d}: {hash_errado_hex}")
        
        if hash_errado_hex == numero_magico_hardcoded:
            print(f"🎉 ENCONTRADO! Chave {chave} gera o número mágico hardcoded!")
            
            # Verificar se esta chave gera o endereço alvo
            endereco_gerado = private_key_to_address(chave)
            print(f"Endereço gerado: {endereco_gerado}")
            print(f"Endereço alvo:   {endereco_alvo}")
            print(f"Match: {endereco_gerado == endereco_alvo}")
            
            return chave
    
    print(f"\n❌ NENHUMA CHAVE TESTADA GERA O NÚMERO MÁGICO HARDCODED")
    print("💡 Isso significa que:")
    print("   1. O valor foi inventado/simulado")
    print("   2. A chave real está fora do range testado")
    print("   3. O valor foi calculado anteriormente e salvo")
    
    return None

def testar_range_maior():
    """
    Testa um range maior para encontrar a chave real
    """
    print(f"\n🔍 TESTANDO RANGE MAIOR")
    print("=" * 30)
    
    endereco_alvo = "1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU"
    numero_magico_hardcoded = "15dcad75ce214766086340311434d412874c7e77"
    
    # Testar range maior (pode demorar)
    print("⚠️  ATENÇÃO: Teste pode demorar alguns minutos...")
    
    continuar = input("Continuar com teste de range maior? (s/N): ")
    if not continuar.lower().startswith('s'):
        return None
    
    print("🔍 Testando range 1 - 10,000,000...")
    
    for chave in range(1, 10000001):
        try:
            # Primeiro verificar se gera o endereço
            endereco_gerado = private_key_to_address(chave)
            
            if endereco_gerado == endereco_alvo:
                print(f"\n🎉 CHAVE REAL ENCONTRADA!")
                print(f"Chave privada: {chave}")
                print(f"Endereço: {endereco_gerado}")
                
                # Calcular hash errado real
                hash_errado_real = simular_gpu_errada_para_chave(chave)
                hash_errado_hex = hash_errado_real.hex()
                
                print(f"Hash errado real: {hash_errado_hex}")
                print(f"Hash hardcoded:   {numero_magico_hardcoded}")
                print(f"Match: {hash_errado_hex == numero_magico_hardcoded}")
                
                return chave
            
            # Progresso a cada 100K
            if chave % 100000 == 0:
                print(f"   Testadas: {chave:,} chaves...")
                
        except KeyboardInterrupt:
            print(f"\n⏹️  Teste interrompido pelo usuário")
            return None
        except Exception:
            continue
    
    print(f"\n❌ CHAVE NÃO ENCONTRADA NO RANGE 1-10M")
    return None

def analisar_origem_numero_magico():
    """
    Analisa possíveis origens do número mágico
    """
    print(f"\n🕵️ ANÁLISE DA ORIGEM DO NÚMERO MÁGICO")
    print("=" * 40)
    
    numero_magico = "15dcad75ce214766086340311434d412874c7e77"
    
    print(f"Número mágico: {numero_magico}")
    print(f"Tamanho: {len(numero_magico)} caracteres")
    print(f"Formato: {'Hex válido' if all(c in '0123456789abcdef' for c in numero_magico.lower()) else 'Inválido'}")
    
    # Converter para bytes
    try:
        bytes_magico = bytes.fromhex(numero_magico)
        print(f"Bytes: {len(bytes_magico)} bytes")
        print(f"Primeiros 4 bytes: {bytes_magico[:4].hex()}")
        print(f"Últimos 4 bytes: {bytes_magico[-4:].hex()}")
    except:
        print("❌ Erro ao converter para bytes")
    
    print(f"\n🔍 POSSÍVEIS ORIGENS:")
    print("1. 🎯 Calculado de chave real conhecida")
    print("2. 🎲 Gerado aleatoriamente para teste")
    print("3. 📊 Copiado de outro exemplo")
    print("4. 🔧 Resultado de algoritmo específico")
    
    print(f"\n💡 COMO DESCOBRIR A VERDADE:")
    print("1. Buscar chave real que gera este endereço")
    print("2. Calcular hash errado da chave real")
    print("3. Comparar com valor hardcoded")

def main():
    """Função principal"""
    print("🔍 INVESTIGAÇÃO: NÚMERO MÁGICO HARDCODED")
    print("=" * 50)
    
    print("🎯 OBJETIVO: Descobrir se o número mágico é real ou inventado")
    print()
    
    # Verificar com chaves pequenas
    chave_encontrada = verificar_numero_magico_hardcoded()
    
    if not chave_encontrada:
        # Testar range maior
        chave_encontrada = testar_range_maior()
    
    # Análise final
    analisar_origem_numero_magico()
    
    print(f"\n🎊 CONCLUSÃO:")
    if chave_encontrada:
        print(f"✅ Número mágico é REAL! Gerado pela chave {chave_encontrada}")
    else:
        print(f"❓ Número mágico pode ser INVENTADO ou chave está em range muito alto")
        print(f"💡 Para descobrir a verdade, seria necessário:")
        print(f"   1. Buscar a chave real da carteira 1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU")
        print(f"   2. Calcular o hash errado real")
        print(f"   3. Comparar com o valor hardcoded")

if __name__ == "__main__":
    main()
