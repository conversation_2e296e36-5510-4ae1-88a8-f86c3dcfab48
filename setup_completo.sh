#!/bin/bash

# SETUP COMPLETO - Sistema Bitcoin Key Finder
# Configura tudo automaticamente

echo "🚀 SETUP COMPLETO - BITCOIN KEY FINDER"
echo "======================================"
echo "Este script vai configurar todo o sistema automaticamente"
echo ""

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar se está rodando como root
if [ "$EUID" -eq 0 ]; then
    log_warning "Não execute como root! Use um usuário normal."
    exit 1
fi

# PASSO 1: Verificar dependências do sistema
log_info "PASSO 1: Verificando dependências do sistema..."

# Verificar CUDA
if ! command -v nvcc &> /dev/null; then
    log_error "NVCC não encontrado!"
    echo "💡 Instale o CUDA Toolkit:"
    echo "   Ubuntu: sudo apt install nvidia-cuda-toolkit"
    echo "   CentOS: sudo yum install cuda-toolkit"
    echo "   Ou baixe de: https://developer.nvidia.com/cuda-downloads"
    exit 1
fi

if ! command -v nvidia-smi &> /dev/null; then
    log_error "nvidia-smi não encontrado!"
    echo "💡 Instale os drivers NVIDIA"
    exit 1
fi

log_success "CUDA Toolkit encontrado: $(nvcc --version | grep release)"

# Verificar GPU
GPU_COUNT=$(nvidia-smi --list-gpus | wc -l)
if [ $GPU_COUNT -eq 0 ]; then
    log_error "Nenhuma GPU NVIDIA detectada!"
    exit 1
fi

log_success "GPUs detectadas: $GPU_COUNT"
nvidia-smi --query-gpu=name,compute_cap,memory.total --format=csv,noheader

# Verificar Python
if ! command -v python3 &> /dev/null; then
    log_error "Python3 não encontrado!"
    echo "💡 Instale Python3: sudo apt install python3"
    exit 1
fi

log_success "Python3 encontrado: $(python3 --version)"

# PASSO 2: Instalar dependências Python
log_info "PASSO 2: Instalando dependências Python..."

# Verificar pip
if ! command -v pip3 &> /dev/null; then
    log_warning "pip3 não encontrado, tentando instalar..."
    sudo apt update && sudo apt install python3-pip -y
fi

# Instalar dependências
PYTHON_DEPS=("requests" "base58" "hashlib" "ecdsa")

for dep in "${PYTHON_DEPS[@]}"; do
    if python3 -c "import $dep" 2>/dev/null; then
        log_success "Dependência Python OK: $dep"
    else
        log_info "Instalando dependência Python: $dep"
        pip3 install $dep
        if [ $? -eq 0 ]; then
            log_success "Instalado: $dep"
        else
            log_error "Falha ao instalar: $dep"
        fi
    fi
done

# PASSO 3: Compilar programas CUDA
log_info "PASSO 3: Compilando programas CUDA..."

# Detectar arquitetura da GPU
GPU_ARCH_RAW=$(nvidia-smi --query-gpu=compute_cap --format=csv,noheader,nounits | head -1)
GPU_ARCH_MAJOR=$(echo $GPU_ARCH_RAW | cut -d'.' -f1)

if [ "$GPU_ARCH_MAJOR" = "12" ]; then
    ARCH="89"  # RTX 5090
elif [ "$GPU_ARCH_MAJOR" = "8" ]; then
    ARCH="86"  # RTX 30xx/40xx
elif [ "$GPU_ARCH_MAJOR" = "7" ]; then
    ARCH="75"  # RTX 20xx
else
    ARCH="75"  # Default
fi

log_info "Arquitetura GPU detectada: sm_$ARCH"

# Lista de programas para compilar
PROGRAMAS=(
    "descobrir_cuda_completo:descobrir_cuda_completo.cu:Programa principal"
    "busca_extrema:configuracao_extrema.cu:Programa otimizado"
    "busca_ultra:busca_ultra_rapida.cu:Programa ultra-rápido"
)

COMPILADOS=0

for programa_info in "${PROGRAMAS[@]}"; do
    IFS=':' read -r programa fonte descricao <<< "$programa_info"
    
    if [ -f "$fonte" ]; then
        log_info "Compilando: $descricao ($programa)"
        
        # Tentar compilar com diferentes arquiteturas
        COMPILOU=false
        for arch in "$ARCH" "89" "86" "75"; do
            if nvcc -O3 -arch=sm_$arch -std=c++11 --use_fast_math -o $programa $fonte 2>/dev/null; then
                log_success "Compilado com sm_$arch: $programa"
                COMPILOU=true
                COMPILADOS=$((COMPILADOS + 1))
                break
            fi
        done
        
        if [ "$COMPILOU" = false ]; then
            log_warning "Falha na compilação: $programa"
        fi
    else
        log_warning "Arquivo fonte não encontrado: $fonte"
    fi
done

if [ $COMPILADOS -eq 0 ]; then
    log_error "Nenhum programa foi compilado com sucesso!"
    exit 1
fi

log_success "Programas compilados: $COMPILADOS"

# PASSO 4: Verificar arquivos necessários
log_info "PASSO 4: Verificando arquivos necessários..."

ARQUIVOS_NECESSARIOS=(
    "main.py:Funções Bitcoin principais"
    "sistema_interativo.py:Interface interativa"
    "configurar_telegram.py:Configurador Telegram"
)

for arquivo_info in "${ARQUIVOS_NECESSARIOS[@]}"; do
    IFS=':' read -r arquivo descricao <<< "$arquivo_info"
    
    if [ -f "$arquivo" ]; then
        log_success "Arquivo OK: $descricao ($arquivo)"
    else
        log_warning "Arquivo não encontrado: $arquivo"
    fi
done

# PASSO 5: Configurar permissões
log_info "PASSO 5: Configurando permissões..."

# Dar permissão de execução
chmod +x descobrir_cuda_completo 2>/dev/null
chmod +x busca_extrema 2>/dev/null
chmod +x busca_ultra 2>/dev/null
chmod +x sistema_interativo.py
chmod +x configurar_telegram.py

log_success "Permissões configuradas"

# PASSO 6: Teste rápido
log_info "PASSO 6: Executando teste rápido..."

# Encontrar programa principal
PROGRAMA_PRINCIPAL=""
if [ -f "./descobrir_cuda_completo" ]; then
    PROGRAMA_PRINCIPAL="./descobrir_cuda_completo"
elif [ -f "./busca_extrema" ]; then
    PROGRAMA_PRINCIPAL="./busca_extrema"
elif [ -f "./busca_ultra" ]; then
    PROGRAMA_PRINCIPAL="./busca_ultra"
fi

if [ -n "$PROGRAMA_PRINCIPAL" ]; then
    log_info "Testando programa: $PROGRAMA_PRINCIPAL"
    
    # Teste com chave conhecida (hash da chave privada 1)
    timeout 10s $PROGRAMA_PRINCIPAL f6f5431d25bbf7b12e8add9af5e3475c44a0a5b8 1 1000 1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU >/dev/null 2>&1
    
    if [ $? -eq 0 ] || [ $? -eq 124 ]; then  # 124 = timeout
        log_success "Teste básico passou"
    else
        log_warning "Teste básico falhou, mas programa pode estar funcionando"
    fi
else
    log_warning "Nenhum programa principal encontrado para teste"
fi

# PASSO 7: Criar scripts de conveniência
log_info "PASSO 7: Criando scripts de conveniência..."

# Script para executar sistema interativo
cat > run_interactive.sh << 'EOF'
#!/bin/bash
echo "🎯 INICIANDO SISTEMA INTERATIVO"
echo "=============================="
python3 sistema_interativo.py
EOF

# Script para configurar Telegram
cat > setup_telegram.sh << 'EOF'
#!/bin/bash
echo "📱 CONFIGURANDO TELEGRAM"
echo "======================="
python3 configurar_telegram.py
EOF

# Script para teste rápido
cat > test_quick.sh << 'EOF'
#!/bin/bash
echo "🧪 TESTE RÁPIDO"
echo "=============="

# Encontrar programa
if [ -f "./descobrir_cuda_completo" ]; then
    PROG="./descobrir_cuda_completo"
elif [ -f "./busca_extrema" ]; then
    PROG="./busca_extrema"
elif [ -f "./busca_ultra" ]; then
    PROG="./busca_ultra"
else
    echo "❌ Nenhum programa encontrado!"
    exit 1
fi

echo "Usando programa: $PROG"
echo "Testando com chave conhecida..."

# Teste com hash da chave 1
$PROG 36df2f22295784ab7f81989f9247bfd99bb00c03 1 1000000
EOF

chmod +x run_interactive.sh
chmod +x setup_telegram.sh
chmod +x test_quick.sh

log_success "Scripts de conveniência criados"

# PASSO 8: Resumo final
echo ""
echo "🎉 SETUP COMPLETO CONCLUÍDO!"
echo "============================"
echo ""
log_success "Sistema configurado com sucesso!"
echo ""
echo "📋 ARQUIVOS CRIADOS:"
echo "   • Programas CUDA compilados: $COMPILADOS"
echo "   • run_interactive.sh - Executar sistema interativo"
echo "   • setup_telegram.sh - Configurar Telegram"
echo "   • test_quick.sh - Teste rápido"
echo ""
echo "🚀 COMO USAR:"
echo "   1. ./run_interactive.sh     # Sistema completo interativo"
echo "   2. ./setup_telegram.sh      # Configurar Telegram (opcional)"
echo "   3. ./test_quick.sh          # Teste rápido"
echo ""
echo "💡 PRÓXIMOS PASSOS:"
echo "   1. Execute: ./run_interactive.sh"
echo "   2. Digite o endereço Bitcoin alvo"
echo "   3. Escolha o range de busca"
echo "   4. Configure Telegram (opcional)"
echo "   5. Inicie a busca!"
echo ""

# Mostrar informações da GPU
echo "🔧 INFORMAÇÕES DA GPU:"
nvidia-smi --query-gpu=name,compute_cap,memory.total,utilization.gpu --format=csv,noheader

echo ""
echo "🎯 SISTEMA PRONTO PARA USO!"
echo "Execute: ./run_interactive.sh"
