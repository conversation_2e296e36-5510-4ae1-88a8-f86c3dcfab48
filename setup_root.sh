#!/bin/bash

# SETUP COMPLETO - Versão ROOT
# Configura sistema como root (com cuidados especiais)

echo "🚀 SETUP COMPLETO - BITCOIN KEY FINDER (ROOT)"
echo "=============================================="
echo "⚠️  Executando como root - usando configurações especiais"
echo ""

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# PASSO 1: Verificar dependências do sistema
log_info "PASSO 1: Verificando dependências do sistema..."

# Verificar CUDA
if ! command -v nvcc &> /dev/null; then
    log_error "NVCC não encontrado!"
    echo "💡 Instalando CUDA Toolkit..."
    
    # Detectar distribuição
    if [ -f /etc/debian_version ]; then
        # Ubuntu/Debian
        apt update
        apt install -y nvidia-cuda-toolkit
    elif [ -f /etc/redhat-release ]; then
        # CentOS/RHEL
        yum install -y cuda-toolkit
    else
        log_error "Distribuição não suportada para instalação automática"
        echo "Instale manualmente: https://developer.nvidia.com/cuda-downloads"
        exit 1
    fi
fi

if ! command -v nvidia-smi &> /dev/null; then
    log_error "nvidia-smi não encontrado!"
    echo "💡 Instalando drivers NVIDIA..."
    
    if [ -f /etc/debian_version ]; then
        apt install -y nvidia-driver-470  # Driver estável
    else
        log_error "Instale drivers NVIDIA manualmente"
        exit 1
    fi
fi

log_success "CUDA Toolkit: $(nvcc --version | grep release | head -1)"

# Verificar GPU
GPU_COUNT=$(nvidia-smi --list-gpus 2>/dev/null | wc -l)
if [ $GPU_COUNT -eq 0 ]; then
    log_error "Nenhuma GPU NVIDIA detectada!"
    echo "💡 Possíveis soluções:"
    echo "   1. Reiniciar o sistema após instalar drivers"
    echo "   2. Verificar se a GPU está conectada"
    echo "   3. Verificar BIOS/UEFI settings"
    exit 1
fi

log_success "GPUs detectadas: $GPU_COUNT"
nvidia-smi --query-gpu=name,compute_cap,memory.total --format=csv,noheader

# Verificar Python
if ! command -v python3 &> /dev/null; then
    log_info "Instalando Python3..."
    if [ -f /etc/debian_version ]; then
        apt install -y python3 python3-pip
    elif [ -f /etc/redhat-release ]; then
        yum install -y python3 python3-pip
    fi
fi

log_success "Python3: $(python3 --version)"

# PASSO 2: Instalar dependências Python
log_info "PASSO 2: Instalando dependências Python..."

# Instalar pip se necessário
if ! command -v pip3 &> /dev/null; then
    log_info "Instalando pip3..."
    if [ -f /etc/debian_version ]; then
        apt install -y python3-pip
    elif [ -f /etc/redhat-release ]; then
        yum install -y python3-pip
    fi
fi

# Dependências Python
PYTHON_DEPS=("requests" "base58")

for dep in "${PYTHON_DEPS[@]}"; do
    log_info "Instalando dependência Python: $dep"
    pip3 install $dep --break-system-packages 2>/dev/null || pip3 install $dep
    
    if python3 -c "import $dep" 2>/dev/null; then
        log_success "Instalado: $dep"
    else
        log_warning "Possível problema com: $dep"
    fi
done

# PASSO 3: Compilar programas CUDA
log_info "PASSO 3: Compilando programas CUDA..."

# Detectar arquitetura da GPU
GPU_ARCH_RAW=$(nvidia-smi --query-gpu=compute_cap --format=csv,noheader,nounits 2>/dev/null | head -1)
if [ -n "$GPU_ARCH_RAW" ]; then
    GPU_ARCH_MAJOR=$(echo $GPU_ARCH_RAW | cut -d'.' -f1)
    
    if [ "$GPU_ARCH_MAJOR" = "12" ]; then
        ARCH="89"  # RTX 5090
    elif [ "$GPU_ARCH_MAJOR" = "8" ]; then
        ARCH="86"  # RTX 30xx/40xx
    elif [ "$GPU_ARCH_MAJOR" = "7" ]; then
        ARCH="75"  # RTX 20xx
    else
        ARCH="75"  # Default
    fi
    
    log_info "Arquitetura GPU detectada: sm_$ARCH (Compute $GPU_ARCH_RAW)"
else
    ARCH="75"
    log_warning "Não foi possível detectar arquitetura, usando sm_75"
fi

# Lista de programas para compilar
PROGRAMAS=(
    "descobrir_cuda_completo:descobrir_cuda_completo.cu:Programa principal"
    "busca_extrema:configuracao_extrema.cu:Programa otimizado"
    "busca_ultra:busca_ultra_rapida.cu:Programa ultra-rápido"
)

COMPILADOS=0

for programa_info in "${PROGRAMAS[@]}"; do
    IFS=':' read -r programa fonte descricao <<< "$programa_info"
    
    if [ -f "$fonte" ]; then
        log_info "Compilando: $descricao ($programa)"
        
        # Tentar compilar com diferentes arquiteturas
        COMPILOU=false
        for arch in "$ARCH" "89" "86" "75" "61"; do
            if nvcc -O3 -arch=sm_$arch -std=c++11 --use_fast_math -o $programa $fonte 2>/dev/null; then
                log_success "Compilado com sm_$arch: $programa"
                COMPILOU=true
                COMPILADOS=$((COMPILADOS + 1))
                break
            fi
        done
        
        if [ "$COMPILOU" = false ]; then
            # Tentar sem arquitetura específica
            if nvcc -O3 -std=c++11 -o $programa $fonte 2>/dev/null; then
                log_success "Compilado sem arquitetura específica: $programa"
                COMPILADOS=$((COMPILADOS + 1))
            else
                log_warning "Falha na compilação: $programa"
            fi
        fi
    else
        log_warning "Arquivo fonte não encontrado: $fonte"
    fi
done

if [ $COMPILADOS -eq 0 ]; then
    log_error "Nenhum programa foi compilado com sucesso!"
    echo "💡 Possíveis soluções:"
    echo "   1. Verificar se CUDA está instalado corretamente"
    echo "   2. Verificar se os arquivos .cu existem"
    echo "   3. Tentar compilação manual"
    exit 1
fi

log_success "Programas compilados: $COMPILADOS"

# PASSO 4: Configurar permissões (importante para root)
log_info "PASSO 4: Configurando permissões..."

# Dar permissão de execução
chmod +x descobrir_cuda_completo 2>/dev/null
chmod +x busca_extrema 2>/dev/null
chmod +x busca_ultra 2>/dev/null
chmod +x sistema_interativo.py 2>/dev/null
chmod +x configurar_telegram.py 2>/dev/null

# Configurar variáveis de ambiente para CUDA (importante para root)
export CUDA_VISIBLE_DEVICES=0
export NVIDIA_VISIBLE_DEVICES=all
export NVIDIA_DRIVER_CAPABILITIES=compute,utility

log_success "Permissões e variáveis configuradas"

# PASSO 5: Teste rápido
log_info "PASSO 5: Executando teste rápido..."

# Encontrar programa principal
PROGRAMA_PRINCIPAL=""
if [ -f "./descobrir_cuda_completo" ]; then
    PROGRAMA_PRINCIPAL="./descobrir_cuda_completo"
elif [ -f "./busca_extrema" ]; then
    PROGRAMA_PRINCIPAL="./busca_extrema"
elif [ -f "./busca_ultra" ]; then
    PROGRAMA_PRINCIPAL="./busca_ultra"
fi

if [ -n "$PROGRAMA_PRINCIPAL" ]; then
    log_info "Testando programa: $PROGRAMA_PRINCIPAL"
    
    # Teste básico (apenas verificar se executa)
    timeout 5s $PROGRAMA_PRINCIPAL 2>/dev/null >/dev/null
    EXIT_CODE=$?
    
    if [ $EXIT_CODE -eq 124 ]; then  # timeout
        log_success "Programa executa (timeout esperado)"
    elif [ $EXIT_CODE -eq 1 ]; then  # argumentos insuficientes
        log_success "Programa executa (argumentos necessários)"
    else
        log_warning "Programa pode ter problemas (código: $EXIT_CODE)"
    fi
else
    log_warning "Nenhum programa principal encontrado para teste"
fi

# PASSO 6: Criar scripts especiais para root
log_info "PASSO 6: Criando scripts para root..."

# Script para executar como root
cat > run_as_root.sh << 'EOF'
#!/bin/bash
echo "🎯 EXECUTANDO COMO ROOT"
echo "======================"

# Configurar variáveis CUDA
export CUDA_VISIBLE_DEVICES=0
export NVIDIA_VISIBLE_DEVICES=all
export NVIDIA_DRIVER_CAPABILITIES=compute,utility

# Verificar se programa existe
if [ -f "./descobrir_cuda_completo" ]; then
    PROG="./descobrir_cuda_completo"
elif [ -f "./busca_extrema" ]; then
    PROG="./busca_extrema"
elif [ -f "./busca_ultra" ]; then
    PROG="./busca_ultra"
else
    echo "❌ Nenhum programa encontrado!"
    exit 1
fi

echo "Programa encontrado: $PROG"
echo ""

# Obter parâmetros do usuário
read -p "Endereço Bitcoin: " ENDERECO
read -p "Hash160 correto: " HASH160
read -p "Range início: " START
read -p "Range fim: " END

echo ""
echo "🚀 Iniciando busca..."
echo "Endereço: $ENDERECO"
echo "Hash160: $HASH160"
echo "Range: $START - $END"
echo ""

# Executar
$PROG "$HASH160" "$START" "$END" "$ENDERECO"
EOF

# Script de teste simples
cat > test_root.sh << 'EOF'
#!/bin/bash
echo "🧪 TESTE COMO ROOT"
echo "=================="

export CUDA_VISIBLE_DEVICES=0
export NVIDIA_VISIBLE_DEVICES=all

if [ -f "./descobrir_cuda_completo" ]; then
    echo "Testando descobrir_cuda_completo..."
    ./descobrir_cuda_completo 36df2f22295784ab7f81989f9247bfd99bb00c03 1 1000 **********************************
elif [ -f "./busca_extrema" ]; then
    echo "Testando busca_extrema..."
    ./busca_extrema 36df2f22295784ab7f81989f9247bfd99bb00c03 1 1000
else
    echo "❌ Nenhum programa encontrado!"
fi
EOF

chmod +x run_as_root.sh
chmod +x test_root.sh

log_success "Scripts para root criados"

# PASSO 7: Resumo final
echo ""
echo "🎉 SETUP ROOT CONCLUÍDO!"
echo "========================"
echo ""
log_success "Sistema configurado para execução como root!"
echo ""
echo "📋 PROGRAMAS COMPILADOS: $COMPILADOS"
echo "📋 SCRIPTS CRIADOS:"
echo "   • run_as_root.sh - Executar busca como root"
echo "   • test_root.sh - Teste rápido"
echo ""
echo "🚀 COMO USAR:"
echo "   1. ./test_root.sh           # Teste rápido"
echo "   2. ./run_as_root.sh         # Busca completa"
echo ""
echo "⚠️  IMPORTANTE PARA ROOT:"
echo "   • Use CUDA_VISIBLE_DEVICES=0"
echo "   • Monitore uso de recursos"
echo "   • Considere criar usuário normal para uso regular"
echo ""

# Mostrar informações da GPU
echo "🔧 INFORMAÇÕES DA GPU:"
nvidia-smi --query-gpu=name,compute_cap,memory.total,utilization.gpu --format=csv,noheader 2>/dev/null || echo "Erro ao acessar GPU"

echo ""
echo "🎯 SISTEMA PRONTO!"
echo "Execute: ./test_root.sh para testar"
