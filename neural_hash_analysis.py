#!/usr/bin/env python3
"""
ANÁLISE DA RELAÇÃO HASH CORRETO → HASH ERRADO
Determina se existe padrão aprendível ou se é completamente aleatório
"""

import numpy as np
import time
import random
from collections import defaultdict, Counter
import matplotlib.pyplot as plt

# Importar funções necessárias
try:
    from test_magic_number import simular_gpu_errada_para_chave
    from main import private_key_to_address
    from bitcoin_conversions import calculate_target_hash160
    print("✅ Módulos Bitcoin importados")
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    exit(1)

def collect_hash_pairs(num_samples=2000):
    """Coleta pares hash correto → hash errado para análise"""
    print(f"🔍 COLETANDO {num_samples} PARES HASH CORRETO → HASH ERRADO...")
    
    pairs = []
    
    for key in range(1, num_samples + 1):
        try:
            endereco = private_key_to_address(key)
            if not endereco:
                continue
            
            hash160_correto = calculate_target_hash160(endereco)
            if not hash160_correto:
                continue
            
            numero_magico = simular_gpu_errada_para_chave(key)
            if not numero_magico:
                continue
            
            hash_correto = hash160_correto.hex()
            hash_errado = numero_magico.hex()
            
            pairs.append((key, hash_correto, hash_errado))
            
            if len(pairs) % 100 == 0:
                print(f"   Coletados: {len(pairs)} pares")
            
        except Exception as e:
            continue
    
    print(f"✅ Total coletado: {len(pairs)} pares")
    return pairs

def analyze_randomness(pairs):
    """Analisa se a relação é aleatória ou tem padrões"""
    print(f"\n🔬 ANÁLISE DE ALEATORIEDADE")
    print("=" * 30)
    
    # 1. Análise por posição
    position_patterns = defaultdict(Counter)
    
    for key, h_correto, h_errado in pairs:
        for pos in range(40):
            if pos < len(h_correto) and pos < len(h_errado):
                char_correto = h_correto[pos]
                char_errado = h_errado[pos]
                
                if char_correto in '0123456789abcdef' and char_errado in '0123456789abcdef':
                    position_patterns[pos][(char_correto, char_errado)] += 1
    
    # Calcular entropia por posição
    entropies = []
    max_entropies = []
    
    for pos in range(40):
        if pos in position_patterns:
            total = sum(position_patterns[pos].values())
            if total > 0:
                # Entropia observada
                entropy = 0
                for count in position_patterns[pos].values():
                    p = count / total
                    if p > 0:
                        entropy -= p * np.log2(p)
                
                # Entropia máxima (distribuição uniforme)
                num_unique = len(position_patterns[pos])
                max_entropy = np.log2(num_unique) if num_unique > 1 else 0
                
                entropies.append(entropy)
                max_entropies.append(max_entropy)
            else:
                entropies.append(0)
                max_entropies.append(0)
        else:
            entropies.append(0)
            max_entropies.append(0)
    
    avg_entropy = np.mean(entropies)
    avg_max_entropy = np.mean(max_entropies)
    randomness_ratio = avg_entropy / avg_max_entropy if avg_max_entropy > 0 else 0
    
    print(f"📊 Entropia média observada: {avg_entropy:.3f}")
    print(f"📊 Entropia máxima possível: {avg_max_entropy:.3f}")
    print(f"📊 Razão de aleatoriedade: {randomness_ratio:.3f} (1.0 = completamente aleatório)")
    
    return randomness_ratio, position_patterns

def analyze_transformations(pairs):
    """Analisa transformações matemáticas entre hashes"""
    print(f"\n🔬 ANÁLISE DE TRANSFORMAÇÕES")
    print("=" * 30)
    
    transformations = {
        'xor': defaultdict(int),
        'add': defaultdict(int),
        'sub': defaultdict(int),
        'mul': defaultdict(int)
    }
    
    for key, h_correto, h_errado in pairs[:500]:  # Amostra para análise
        for pos in range(40):
            if pos < len(h_correto) and pos < len(h_errado):
                char_correto = h_correto[pos]
                char_errado = h_errado[pos]
                
                if char_correto in '0123456789abcdef' and char_errado in '0123456789abcdef':
                    val_correto = int(char_correto, 16)
                    val_errado = int(char_errado, 16)
                    
                    # XOR
                    xor_val = val_correto ^ val_errado
                    transformations['xor'][xor_val] += 1
                    
                    # Adição modular
                    add_val = (val_errado - val_correto) % 16
                    transformations['add'][add_val] += 1
                    
                    # Subtração modular
                    sub_val = (val_correto - val_errado) % 16
                    transformations['sub'][sub_val] += 1
                    
                    # Multiplicação modular
                    if val_correto != 0:
                        for mult in range(1, 16):
                            if (val_correto * mult) % 16 == val_errado:
                                transformations['mul'][mult] += 1
                                break
    
    print("📈 Transformações mais comuns:")
    
    for transform_type, counts in transformations.items():
        if counts:
            total = sum(counts.values())
            most_common = counts.most_common(3)
            
            print(f"\n{transform_type.upper()}:")
            for value, count in most_common:
                percentage = (count / total) * 100
                print(f"   {value}: {count} ({percentage:.1f}%)")
            
            # Calcular uniformidade
            expected_uniform = total / 16
            chi_square = sum((count - expected_uniform) ** 2 / expected_uniform 
                           for count in counts.values())
            
            print(f"   Chi-square: {chi_square:.2f} (menor = mais uniforme)")
    
    return transformations

def analyze_correlations(pairs):
    """Analisa correlações entre posições"""
    print(f"\n🔬 ANÁLISE DE CORRELAÇÕES")
    print("=" * 25)
    
    # Correlação entre posições do hash correto e errado
    correlations = []
    
    for pos in range(40):
        correto_values = []
        errado_values = []
        
        for key, h_correto, h_errado in pairs:
            if pos < len(h_correto) and pos < len(h_errado):
                char_correto = h_correto[pos]
                char_errado = h_errado[pos]
                
                if char_correto in '0123456789abcdef' and char_errado in '0123456789abcdef':
                    correto_values.append(int(char_correto, 16))
                    errado_values.append(int(char_errado, 16))
        
        if len(correto_values) > 10:
            correlation = np.corrcoef(correto_values, errado_values)[0, 1]
            if not np.isnan(correlation):
                correlations.append((pos, correlation))
    
    # Ordenar por correlação absoluta
    correlations.sort(key=lambda x: abs(x[1]), reverse=True)
    
    print("📊 Correlações por posição (top 10):")
    for pos, corr in correlations[:10]:
        print(f"   Posição {pos:2d}: {corr:6.3f}")
    
    avg_correlation = np.mean([abs(corr) for _, corr in correlations])
    print(f"\n📊 Correlação média absoluta: {avg_correlation:.3f}")
    print(f"📊 Interpretação: {avg_correlation:.3f} < 0.1 = sem correlação significativa")
    
    return correlations

def test_predictability(pairs):
    """Testa se é possível prever usando métodos simples"""
    print(f"\n🔬 TESTE DE PREVISIBILIDADE")
    print("=" * 25)
    
    # Dividir dados
    train_pairs = pairs[:len(pairs)//2]
    test_pairs = pairs[len(pairs)//2:]
    
    # Método 1: Lookup table simples
    lookup_table = {}
    for key, h_correto, h_errado in train_pairs:
        lookup_table[h_correto] = h_errado
    
    lookup_hits = 0
    for key, h_correto, h_errado in test_pairs:
        if h_correto in lookup_table and lookup_table[h_correto] == h_errado:
            lookup_hits += 1
    
    lookup_accuracy = (lookup_hits / len(test_pairs)) * 100
    print(f"📊 Lookup table accuracy: {lookup_accuracy:.1f}%")
    
    # Método 2: Padrão por posição mais comum
    position_mappings = defaultdict(lambda: defaultdict(int))
    
    for key, h_correto, h_errado in train_pairs:
        for pos in range(40):
            if pos < len(h_correto) and pos < len(h_errado):
                char_correto = h_correto[pos]
                char_errado = h_errado[pos]
                
                if char_correto in '0123456789abcdef' and char_errado in '0123456789abcdef':
                    position_mappings[pos][char_correto] = char_errado
    
    # Testar padrão por posição
    pattern_correct = 0
    pattern_total = 0
    
    for key, h_correto, h_errado in test_pairs:
        predicted_hash = ""
        
        for pos in range(40):
            if pos < len(h_correto):
                char_correto = h_correto[pos]
                
                if char_correto in position_mappings[pos]:
                    predicted_char = position_mappings[pos][char_correto]
                else:
                    predicted_char = random.choice('0123456789abcdef')
                
                predicted_hash += predicted_char
        
        # Comparar caractere por caractere
        for i in range(min(len(predicted_hash), len(h_errado))):
            if predicted_hash[i] == h_errado[i]:
                pattern_correct += 1
            pattern_total += 1
    
    pattern_accuracy = (pattern_correct / pattern_total) * 100 if pattern_total > 0 else 0
    print(f"📊 Pattern mapping accuracy: {pattern_accuracy:.1f}%")
    
    # Método 3: Baseline aleatório
    random_correct = 0
    random_total = 0
    
    for key, h_correto, h_errado in test_pairs:
        random_hash = ''.join(random.choice('0123456789abcdef') for _ in range(40))
        
        for i in range(40):
            if random_hash[i] == h_errado[i]:
                random_correct += 1
            random_total += 1
    
    random_accuracy = (random_correct / random_total) * 100 if random_total > 0 else 0
    print(f"📊 Random baseline accuracy: {random_accuracy:.1f}%")
    
    return lookup_accuracy, pattern_accuracy, random_accuracy

def analyze_key_dependency(pairs):
    """Analisa se o hash errado depende da chave privada"""
    print(f"\n🔬 ANÁLISE DE DEPENDÊNCIA DA CHAVE")
    print("=" * 35)
    
    # Agrupar por chave
    key_patterns = defaultdict(list)
    
    for key, h_correto, h_errado in pairs:
        key_patterns[key].append((h_correto, h_errado))
    
    # Verificar se chaves próximas têm padrões similares
    key_similarities = []
    
    sorted_keys = sorted(key_patterns.keys())
    
    for i in range(len(sorted_keys) - 1):
        key1 = sorted_keys[i]
        key2 = sorted_keys[i + 1]
        
        if key1 in key_patterns and key2 in key_patterns:
            h_correto1, h_errado1 = key_patterns[key1][0]
            h_correto2, h_errado2 = key_patterns[key2][0]
            
            # Similaridade entre hashes errados
            similar_chars = sum(1 for a, b in zip(h_errado1, h_errado2) if a == b)
            similarity = similar_chars / 40
            
            key_similarities.append(similarity)
    
    avg_similarity = np.mean(key_similarities) if key_similarities else 0
    print(f"📊 Similaridade média entre chaves consecutivas: {avg_similarity:.3f}")
    print(f"📊 Interpretação: {avg_similarity:.3f} ≈ 0.0625 = completamente aleatório")
    
    return avg_similarity

def main():
    """Análise completa da relação hash correto → hash errado"""
    print("🔬 ANÁLISE COMPLETA DA RELAÇÃO HASH CORRETO → HASH ERRADO")
    print("=" * 65)
    
    # Coletar dados
    pairs = collect_hash_pairs(1500)
    
    if len(pairs) < 100:
        print("❌ Dados insuficientes para análise")
        return
    
    print(f"\n📊 DADOS COLETADOS: {len(pairs)} pares")
    print("=" * 30)
    
    # Mostrar alguns exemplos
    print("🔍 EXEMPLOS DE PARES:")
    for i, (key, h_correto, h_errado) in enumerate(pairs[:5]):
        print(f"Chave {key:4x}: {h_correto} → {h_errado}")
    
    # Análises
    randomness_ratio, position_patterns = analyze_randomness(pairs)
    transformations = analyze_transformations(pairs)
    correlations = analyze_correlations(pairs)
    lookup_acc, pattern_acc, random_acc = test_predictability(pairs)
    key_similarity = analyze_key_dependency(pairs)
    
    # Conclusões
    print(f"\n🎯 CONCLUSÕES DA ANÁLISE")
    print("=" * 25)
    
    print(f"📊 Aleatoriedade: {randomness_ratio:.3f}")
    print(f"📊 Correlação média: {np.mean([abs(corr) for _, corr in correlations]):.3f}")
    print(f"📊 Lookup accuracy: {lookup_acc:.1f}%")
    print(f"📊 Pattern accuracy: {pattern_acc:.1f}%")
    print(f"📊 Random baseline: {random_acc:.1f}%")
    print(f"📊 Key similarity: {key_similarity:.3f}")
    
    # Veredicto final
    print(f"\n🏆 VEREDICTO FINAL:")
    
    if randomness_ratio > 0.9 and pattern_acc <= random_acc + 2:
        print("❌ RELAÇÃO COMPLETAMENTE ALEATÓRIA")
        print("   • Não há padrão matemático detectável")
        print("   • Redes neurais não conseguirão aprender")
        print("   • Acurácia máxima esperada: ~6.25% (1/16)")
        print("   • Recomendação: Buscar outra abordagem")
        
    elif randomness_ratio > 0.7:
        print("⚠️  RELAÇÃO MAJORITARIAMENTE ALEATÓRIA")
        print("   • Padrões muito fracos ou esparsos")
        print("   • Redes neurais terão dificuldade extrema")
        print("   • Acurácia máxima esperada: ~10-15%")
        print("   • Recomendação: Investigar outras variáveis")
        
    elif pattern_acc > random_acc + 5:
        print("✅ PADRÕES DETECTÁVEIS ENCONTRADOS")
        print("   • Existe alguma estrutura na relação")
        print("   • Redes neurais podem ter sucesso limitado")
        print("   • Acurácia máxima esperada: ~20-40%")
        print("   • Recomendação: Continuar com ML avançado")
        
    else:
        print("🤔 RELAÇÃO AMBÍGUA")
        print("   • Padrões fracos ou inconsistentes")
        print("   • Sucesso de ML incerto")
        print("   • Recomendação: Mais análise necessária")
    
    # Sugestões
    print(f"\n💡 SUGESTÕES:")
    print("1. Verificar se test_magic_number usa informações além do hash")
    print("2. Analisar o código fonte da função de geração do hash errado")
    print("3. Investigar se há dependência da chave privada completa")
    print("4. Considerar que pode ser uma função hash criptográfica")
    print("5. Testar com ranges diferentes de chaves privadas")

if __name__ == "__main__":
    main()
