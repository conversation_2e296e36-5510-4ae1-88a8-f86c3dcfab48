#!/usr/bin/env python3
"""
REDE NEURAL ULTIMATE - MÁXIMA ACURÁCIA
Técnicas avançadas de deep learning para resolver a relação hash correto → hash errado
"""

import numpy as np
import time
import json
import random
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed

# Tentar importar CuPy
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("✅ CuPy disponível - GPU habilitada")
except ImportError:
    import numpy as cp
    GPU_AVAILABLE = False
    print("⚠️  CuPy não encontrado - usando CPU")

# Importar funções necessárias
try:
    from test_magic_number import simular_gpu_errada_para_chave
    from main import private_key_to_address
    from bitcoin_conversions import calculate_target_hash160
    print("✅ Módulos Bitcoin importados")
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    exit(1)

def analyze_hash_relationship(num_samples=1000):
    """Analisa a relação entre hash correto e hash errado"""
    print("🔍 ANALISANDO RELAÇÃO HASH CORRETO → HASH ERRADO...")
    
    relationships = []
    
    for key in range(1, num_samples + 1):
        try:
            endereco = private_key_to_address(key)
            if not endereco:
                continue
            
            hash160_correto = calculate_target_hash160(endereco)
            if not hash160_correto:
                continue
            
            numero_magico = simular_gpu_errada_para_chave(key)
            if not numero_magico:
                continue
            
            hash_correto = hash160_correto.hex()
            hash_errado = numero_magico.hex()
            
            relationships.append((key, hash_correto, hash_errado))
            
        except:
            continue
    
    print(f"📊 Analisadas {len(relationships)} relações")
    
    # Análise de padrões
    xor_patterns = {}
    add_patterns = {}
    position_correlations = []
    
    for key, h_correto, h_errado in relationships[:100]:  # Amostra para análise
        for pos in range(40):
            if h_correto[pos] in '0123456789abcdef' and h_errado[pos] in '0123456789abcdef':
                val_correto = int(h_correto[pos], 16)
                val_errado = int(h_errado[pos], 16)
                
                # XOR pattern
                xor_val = val_correto ^ val_errado
                xor_key = f"pos_{pos}_xor_{xor_val}"
                xor_patterns[xor_key] = xor_patterns.get(xor_key, 0) + 1
                
                # Addition pattern
                add_val = (val_errado - val_correto) % 16
                add_key = f"pos_{pos}_add_{add_val}"
                add_patterns[add_key] = add_patterns.get(add_key, 0) + 1
    
    # Padrões mais comuns
    top_xor = sorted(xor_patterns.items(), key=lambda x: x[1], reverse=True)[:10]
    top_add = sorted(add_patterns.items(), key=lambda x: x[1], reverse=True)[:10]
    
    print("📈 Padrões XOR mais comuns:")
    for pattern, count in top_xor[:5]:
        print(f"   {pattern}: {count} ocorrências")
    
    print("📈 Padrões ADD mais comuns:")
    for pattern, count in top_add[:5]:
        print(f"   {pattern}: {count} ocorrências")
    
    return relationships, top_xor, top_add

def hex_to_multi_representation(hex_string):
    """Múltiplas representações do hash para capturar diferentes padrões"""
    hex_string = hex_string.ljust(40, '0')[:40]
    
    features = []
    
    # 1. One-hot encoding (640 features)
    one_hot = np.zeros(40 * 16, dtype=np.float32)
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            one_hot[i * 16 + int(char, 16)] = 1.0
    features.extend(one_hot)
    
    # 2. Valores numéricos (40 features)
    numeric = np.zeros(40, dtype=np.float32)
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            numeric[i] = int(char, 16) / 15.0
    features.extend(numeric)
    
    # 3. Valores binários (160 features - 4 bits por char)
    binary = np.zeros(160, dtype=np.float32)
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            val = int(char, 16)
            for bit in range(4):
                binary[i * 4 + bit] = float((val >> bit) & 1)
    features.extend(binary)
    
    # 4. Diferenças entre caracteres adjacentes (39 features)
    diffs = np.zeros(39, dtype=np.float32)
    for i in range(39):
        char1 = hex_string[i] if hex_string[i] in '0123456789abcdef' else '0'
        char2 = hex_string[i+1] if hex_string[i+1] in '0123456789abcdef' else '0'
        diff = (int(char2, 16) - int(char1, 16)) % 16
        diffs[i] = diff / 15.0
    features.extend(diffs)
    
    # 5. Somas de grupos (20 features - grupos de 2 chars)
    sums = np.zeros(20, dtype=np.float32)
    for i in range(0, 40, 2):
        if i+1 < 40:
            char1 = hex_string[i] if hex_string[i] in '0123456789abcdef' else '0'
            char2 = hex_string[i+1] if hex_string[i+1] in '0123456789abcdef' else '0'
            sum_val = (int(char1, 16) + int(char2, 16)) % 16
            sums[i//2] = sum_val / 15.0
    features.extend(sums)
    
    # 6. XOR de grupos (20 features)
    xors = np.zeros(20, dtype=np.float32)
    for i in range(0, 40, 2):
        if i+1 < 40:
            char1 = hex_string[i] if hex_string[i] in '0123456789abcdef' else '0'
            char2 = hex_string[i+1] if hex_string[i+1] in '0123456789abcdef' else '0'
            xor_val = int(char1, 16) ^ int(char2, 16)
            xors[i//2] = xor_val / 15.0
    features.extend(xors)
    
    # 7. Posições pares e ímpares (40 features)
    even_odd = np.zeros(40, dtype=np.float32)
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            val = int(char, 16)
            if i % 2 == 0:  # Posição par
                even_odd[i] = val / 15.0
            else:  # Posição ímpar
                even_odd[i] = (val ^ 0xF) / 15.0  # XOR com F
    features.extend(even_odd)
    
    return np.array(features, dtype=np.float32)

def multi_representation_to_hex(features):
    """Converte de volta para hex usando one-hot"""
    one_hot = features[:640].reshape(40, 16)
    hex_chars = '0123456789abcdef'
    return ''.join(hex_chars[np.argmax(row)] for row in one_hot)

def generate_ultimate_sample(private_key):
    """Gera amostra com múltiplas representações"""
    try:
        endereco = private_key_to_address(private_key)
        if not endereco:
            return None, None, None
        
        hash160_correto = calculate_target_hash160(endereco)
        if not hash160_correto:
            return None, None, None
        
        numero_magico = simular_gpu_errada_para_chave(private_key)
        if not numero_magico:
            return None, None, None
        
        input_features = hex_to_multi_representation(hash160_correto.hex())
        output_features = hex_to_multi_representation(numero_magico.hex())
        
        return input_features, output_features, private_key
        
    except Exception:
        return None, None, None

def generate_batch_ultimate(key_batch):
    """Gera batch com representações múltiplas"""
    inputs = []
    outputs = []
    successful_keys = []
    
    for private_key in key_batch:
        input_vec, output_vec, key = generate_ultimate_sample(private_key)
        
        if input_vec is not None and output_vec is not None:
            inputs.append(input_vec)
            outputs.append(output_vec)
            successful_keys.append(key)
    
    return inputs, outputs, successful_keys

class UltimateNeuralNetwork:
    """Rede neural ultimate com arquitetura avançada"""
    
    def __init__(self, input_size=958, use_gpu=True):  # 640+40+160+39+20+20+40-1 = 958
        self.use_gpu = use_gpu and GPU_AVAILABLE
        self.xp = cp if self.use_gpu else np
        
        # Arquitetura muito profunda com skip connections
        self.layers = [
            input_size,    # 958 features
            1024,          # Primeira camada
            1536,          # Expansão
            2048,          # Camada principal
            2048,          # Processamento profundo
            1536,          # Refinamento
            1024,          # Contração
            input_size     # Saída
        ]
        
        print(f"🧠 Ultimate Neural Network")
        print(f"🔧 Dispositivo: {'GPU' if self.use_gpu else 'CPU'}")
        print(f"📊 Arquitetura: {' → '.join(map(str, self.layers))}")
        print(f"📊 Features: 958 (múltiplas representações)")
        
        # Inicialização He com variância adaptativa
        self.weights = []
        self.biases = []
        self.skip_weights = []  # Para skip connections
        
        for i in range(len(self.layers) - 1):
            fan_in = self.layers[i]
            fan_out = self.layers[i + 1]
            
            # He initialization com fator adaptativo
            std = self.xp.sqrt(2.0 / fan_in) * 0.7
            w = self.xp.random.normal(0, std, (fan_in, fan_out)).astype(self.xp.float32)
            b = self.xp.random.normal(0, 0.01, (1, fan_out)).astype(self.xp.float32)
            
            self.weights.append(w)
            self.biases.append(b)
            
            # Skip connections para camadas compatíveis
            if fan_in == fan_out:
                skip_w = self.xp.random.normal(0, 0.1, (fan_in, fan_out)).astype(self.xp.float32)
                self.skip_weights.append(skip_w)
            else:
                self.skip_weights.append(None)
        
        total_params = sum(w.size for w in self.weights) + sum(b.size for b in self.biases)
        total_params += sum(w.size for w in self.skip_weights if w is not None)
        print(f"📊 Parâmetros: {total_params:,}")
    
    def swish(self, x):
        """Swish activation function"""
        return x / (1 + self.xp.exp(-self.xp.clip(x, -10, 10)))
    
    def swish_derivative(self, x):
        """Derivada da Swish"""
        sigmoid = 1 / (1 + self.xp.exp(-self.xp.clip(x, -10, 10)))
        return sigmoid + x * sigmoid * (1 - sigmoid)
    
    def layer_norm(self, x, eps=1e-6):
        """Layer normalization"""
        mean = self.xp.mean(x, axis=1, keepdims=True)
        var = self.xp.var(x, axis=1, keepdims=True)
        return (x - mean) / self.xp.sqrt(var + eps)
    
    def forward_ultimate(self, X):
        """Forward pass com skip connections e layer norm"""
        if self.use_gpu and not isinstance(X, cp.ndarray):
            X = cp.asarray(X)
        elif not self.use_gpu and isinstance(X, cp.ndarray):
            X = cp.asnumpy(X)
        
        activations = [X]
        z_values = []
        
        for i, (w, b) in enumerate(zip(self.weights, self.biases)):
            z = self.xp.dot(activations[-1], w) + b
            z_values.append(z)
            
            if i < len(self.weights) - 1:
                # Camadas ocultas: Swish + Layer Norm
                a = self.swish(z)
                a = self.layer_norm(a)
                
                # Skip connection se disponível
                if self.skip_weights[i] is not None and activations[-1].shape == a.shape:
                    skip = self.xp.dot(activations[-1], self.skip_weights[i])
                    a = a + 0.3 * skip  # Residual connection com peso
                
                # Dropout adaptativo
                if i > 0:
                    dropout_rate = 0.1 + 0.05 * i  # Dropout crescente
                    dropout_mask = self.xp.random.random(a.shape) > dropout_rate
                    a = a * dropout_mask / (1 - dropout_rate)
                
            else:
                # Camada de saída: tratamento especial
                batch_size = z.shape[0]
                
                # One-hot part (primeiros 640): softmax agrupado
                one_hot_part = z[:, :640].reshape(batch_size, 40, 16)
                exp_vals = self.xp.exp(one_hot_part - self.xp.max(one_hot_part, axis=2, keepdims=True))
                softmax_part = exp_vals / self.xp.sum(exp_vals, axis=2, keepdims=True)
                softmax_flat = softmax_part.reshape(batch_size, 640)
                
                # Outras features: tanh
                other_part = self.xp.tanh(z[:, 640:])
                
                a = self.xp.concatenate([softmax_flat, other_part], axis=1)
            
            activations.append(a)
        
        return activations, z_values
    
    def compute_loss_ultimate(self, y_pred, y_true):
        """Loss function ultimate"""
        batch_size = y_pred.shape[0]
        
        # One-hot part: cross-entropy
        y_pred_oh = y_pred[:, :640]
        y_true_oh = y_true[:, :640]
        
        y_pred_oh_clipped = self.xp.clip(y_pred_oh, 1e-15, 1 - 1e-15)
        ce_loss = -self.xp.sum(y_true_oh * self.xp.log(y_pred_oh_clipped)) / batch_size
        
        # Outras features: Smooth L1 loss
        y_pred_other = y_pred[:, 640:]
        y_true_other = y_true[:, 640:]
        
        diff = self.xp.abs(y_pred_other - y_true_other)
        smooth_l1 = self.xp.where(diff < 1.0, 0.5 * diff ** 2, diff - 0.5)
        smooth_l1_loss = self.xp.mean(smooth_l1)
        
        # Loss combinada com pesos adaptativos
        total_loss = 0.8 * ce_loss + 0.2 * smooth_l1_loss
        
        return total_loss, ce_loss, smooth_l1_loss
    
    def backward_ultimate(self, X, y, activations, z_values, learning_rate, l2_reg=0.0001):
        """Backward pass ultimate com momentum"""
        m = X.shape[0]
        
        # Inicializar momentum se necessário
        if not hasattr(self, 'momentum_w'):
            self.momentum_w = [self.xp.zeros_like(w) for w in self.weights]
            self.momentum_b = [self.xp.zeros_like(b) for b in self.biases]
            self.momentum_skip = [self.xp.zeros_like(w) if w is not None else None 
                                 for w in self.skip_weights]
        
        # Erro da saída
        output_error = activations[-1] - y
        
        # Backpropagation
        errors = [output_error]
        
        for i in range(len(self.weights) - 1, 0, -1):
            error = self.xp.dot(errors[-1], self.weights[i].T)
            
            # Derivada da ativação
            if i < len(self.weights) - 1:
                error *= self.swish_derivative(z_values[i-1])
            
            errors.append(error)
        
        errors.reverse()
        
        # Atualizar pesos com momentum
        momentum_factor = 0.9
        
        for i in range(len(self.weights)):
            dw = self.xp.dot(activations[i].T, errors[i]) / m
            db = self.xp.mean(errors[i], axis=0, keepdims=True)
            
            # Regularização L2
            dw += l2_reg * self.weights[i]
            
            # Gradient clipping adaptativo
            grad_norm = self.xp.sqrt(self.xp.sum(dw ** 2))
            if grad_norm > 1.0:
                dw = dw / grad_norm
            
            # Momentum
            self.momentum_w[i] = momentum_factor * self.momentum_w[i] + (1 - momentum_factor) * dw
            self.momentum_b[i] = momentum_factor * self.momentum_b[i] + (1 - momentum_factor) * db
            
            self.weights[i] -= learning_rate * self.momentum_w[i]
            self.biases[i] -= learning_rate * self.momentum_b[i]
            
            # Atualizar skip weights
            if self.skip_weights[i] is not None:
                dw_skip = self.xp.dot(activations[i].T, errors[i]) / m * 0.1  # Menor learning rate
                
                if self.momentum_skip[i] is None:
                    self.momentum_skip[i] = self.xp.zeros_like(self.skip_weights[i])
                
                self.momentum_skip[i] = momentum_factor * self.momentum_skip[i] + (1 - momentum_factor) * dw_skip
                self.skip_weights[i] -= learning_rate * 0.5 * self.momentum_skip[i]
        
        # Calcular loss
        total_loss, ce_loss, smooth_l1_loss = self.compute_loss_ultimate(activations[-1], y)
        l2_loss = sum(self.xp.sum(w ** 2) for w in self.weights) * l2_reg
        
        return float(total_loss + l2_loss), float(ce_loss), float(smooth_l1_loss)

    def train_ultimate(self, X, y, epochs=1200, initial_lr=0.0005, batch_size=16):
        """Treinamento ultimate com técnicas avançadas"""
        print(f"\n🚀 TREINAMENTO ULTIMATE")
        print("=" * 30)

        if self.use_gpu:
            if not isinstance(X, cp.ndarray):
                X = cp.asarray(X)
            if not isinstance(y, cp.ndarray):
                y = cp.asarray(y)

        num_batches = len(X) // batch_size
        best_loss = float('inf')
        patience = 200
        patience_counter = 0

        print(f"Configuração ultimate:")
        print(f"  Amostras: {len(X):,}")
        print(f"  Batch size: {batch_size}")
        print(f"  Learning rate: {initial_lr}")
        print(f"  Técnicas: Skip connections + Layer norm + Momentum + Smooth L1")

        for epoch in range(epochs):
            # Learning rate schedule com warm-up e cosine annealing
            if epoch < 100:
                lr = initial_lr * (epoch + 1) / 100  # Warm-up
            elif epoch < 400:
                lr = initial_lr
            elif epoch < 800:
                lr = initial_lr * 0.5
            else:
                # Cosine annealing
                lr = initial_lr * 0.1 * (1 + np.cos(np.pi * (epoch - 800) / 400)) / 2

            # Regularização adaptativa
            l2_reg = 0.0001 * (1 + epoch / 300)

            # Embaralhar dados
            if self.use_gpu:
                indices = cp.random.permutation(len(X))
            else:
                indices = np.random.permutation(len(X))

            X_shuffled = X[indices]
            y_shuffled = y[indices]

            epoch_total_loss = 0.0
            epoch_ce_loss = 0.0
            epoch_smooth_l1_loss = 0.0

            # Treinar por batches
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = start_idx + batch_size

                X_batch = X_shuffled[start_idx:end_idx]
                y_batch = y_shuffled[start_idx:end_idx]

                # Forward pass
                activations, z_values = self.forward_ultimate(X_batch)

                # Backward pass
                total_loss, ce_loss, smooth_l1_loss = self.backward_ultimate(
                    X_batch, y_batch, activations, z_values, lr, l2_reg
                )

                epoch_total_loss += total_loss
                epoch_ce_loss += ce_loss
                epoch_smooth_l1_loss += smooth_l1_loss

            epoch_total_loss /= num_batches
            epoch_ce_loss /= num_batches
            epoch_smooth_l1_loss /= num_batches

            # Early stopping
            if epoch_total_loss < best_loss:
                best_loss = epoch_total_loss
                patience_counter = 0
            else:
                patience_counter += 1

            if patience_counter >= patience:
                print(f"\n⏰ Early stopping na época {epoch+1}")
                break

            # Log progresso
            if (epoch + 1) % 50 == 0:
                print(f"Época {epoch+1:4d}/{epochs} | "
                      f"Total: {epoch_total_loss:.6f} | "
                      f"CE: {epoch_ce_loss:.6f} | "
                      f"L1: {epoch_smooth_l1_loss:.6f} | "
                      f"LR: {lr:.6f}")

        print(f"\n✅ Treinamento ultimate concluído!")
        print(f"📈 Melhor loss: {best_loss:.6f}")

        return best_loss

    def predict_ultimate(self, X):
        """Predição ultimate"""
        activations, _ = self.forward_ultimate(X)
        result = activations[-1]

        if self.use_gpu and isinstance(result, cp.ndarray):
            result = cp.asnumpy(result)

        return result

class UltimateHashPredictor:
    """Preditor ultimate com máxima acurácia"""

    def __init__(self):
        self.model = None
        self.use_gpu = GPU_AVAILABLE

        print(f"🚀 Ultimate Hash Predictor")
        print(f"🔧 GPU: {'Habilitada' if self.use_gpu else 'Desabilitada'}")

    def generate_dataset_ultimate(self, num_samples=8000, start_key=1, end_key=30000):
        """Gera dataset ultimate com análise prévia"""
        print(f"🚀 GERANDO DATASET ULTIMATE: {num_samples:,} AMOSTRAS")
        print("=" * 60)

        # Análise prévia da relação
        relationships, xor_patterns, add_patterns = analyze_hash_relationship(500)

        # Usar chaves menores para padrões mais claros
        all_keys = random.sample(range(start_key, min(end_key, 25000)),
                                min(num_samples, 24999))

        # Processamento paralelo
        num_processes = min(mp.cpu_count(), 6)
        batch_size = len(all_keys) // num_processes

        key_batches = [all_keys[i:i + batch_size]
                      for i in range(0, len(all_keys), batch_size)]

        print(f"🔄 Processamento: {num_processes} processos")
        print(f"📊 Chaves: {start_key} a {min(end_key, 25000)}")

        all_inputs = []
        all_outputs = []
        all_keys = []

        start_time = time.time()

        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            futures = [executor.submit(generate_batch_ultimate, batch)
                      for batch in key_batches]

            for i, future in enumerate(as_completed(futures)):
                try:
                    inputs, outputs, keys = future.result()
                    all_inputs.extend(inputs)
                    all_outputs.extend(outputs)
                    all_keys.extend(keys)

                    print(f"   Batch {i+1}/{len(key_batches)}: {len(inputs)} amostras")

                except Exception as e:
                    print(f"   ❌ Erro batch {i+1}: {e}")

        generation_time = time.time() - start_time

        print(f"\n📊 DATASET ULTIMATE:")
        print(f"✅ Amostras: {len(all_inputs):,}")
        print(f"⏱️  Tempo: {generation_time:.1f}s")
        print(f"📊 Features por amostra: {len(all_inputs[0]) if all_inputs else 0}")

        if len(all_inputs) == 0:
            return None, None, None

        return np.array(all_inputs, dtype=np.float32), np.array(all_outputs, dtype=np.float32), all_keys

    def evaluate_ultimate(self, X_test, y_test, test_keys):
        """Avaliação ultimate"""
        print(f"\n📊 AVALIAÇÃO ULTIMATE")
        print("=" * 25)

        predictions = self.model.predict_ultimate(X_test)

        print(f"🔍 AMOSTRAS DE TESTE (primeiras 8):")
        print("-" * 90)

        accuracies = []
        perfect_matches = 0
        high_accuracy = 0  # >70%
        medium_accuracy = 0  # 40-70%

        for i in range(min(8, len(predictions))):
            key = test_keys[i]
            input_hash = multi_representation_to_hex(X_test[i])
            expected_output = multi_representation_to_hex(y_test[i])
            predicted_output = multi_representation_to_hex(predictions[i])

            # Acurácia por caractere
            correct_chars = sum(1 for a, b in zip(expected_output, predicted_output) if a == b)
            accuracy = (correct_chars / 40) * 100
            accuracies.append(accuracy)

            if accuracy == 100:
                perfect_matches += 1
            elif accuracy > 70:
                high_accuracy += 1
            elif accuracy > 40:
                medium_accuracy += 1

            print(f"Chave {key:x}:")
            print(f"  INPUT (hash correto):     {input_hash}")
            print(f"  OUTPUT esperado (errado): {expected_output}")
            print(f"  OUTPUT predito (errado):  {predicted_output}")
            print(f"  Acurácia: {accuracy:5.1f}% ({correct_chars:2d}/40)")

            # Análise de erros
            if accuracy < 100:
                errors = [(j, expected_output[j], predicted_output[j]) for j in range(40)
                         if expected_output[j] != predicted_output[j]]
                if len(errors) <= 6:
                    error_str = ", ".join([f"{pos}:{exp}→{pred}" for pos, exp, pred in errors])
                    print(f"  Erros:    {error_str}")
                else:
                    print(f"  Erros:    {len(errors)} posições diferentes")
            print()

        # Estatísticas completas
        all_accuracies = []
        all_perfect = 0
        all_high = 0
        all_medium = 0

        for i in range(len(predictions)):
            expected_output = multi_representation_to_hex(y_test[i])
            predicted_output = multi_representation_to_hex(predictions[i])
            correct_chars = sum(1 for a, b in zip(expected_output, predicted_output) if a == b)
            accuracy = (correct_chars / 40) * 100
            all_accuracies.append(accuracy)

            if accuracy == 100:
                all_perfect += 1
            elif accuracy > 70:
                all_high += 1
            elif accuracy > 40:
                all_medium += 1

        avg_accuracy = np.mean(all_accuracies)
        median_accuracy = np.median(all_accuracies)
        std_accuracy = np.std(all_accuracies)
        max_accuracy = np.max(all_accuracies)

        print(f"📈 ESTATÍSTICAS ULTIMATE:")
        print(f"   Média:           {avg_accuracy:6.1f}%")
        print(f"   Mediana:         {median_accuracy:6.1f}%")
        print(f"   Desvio padrão:   {std_accuracy:6.1f}%")
        print(f"   Máxima:          {max_accuracy:6.1f}%")
        print()
        print(f"📊 DISTRIBUIÇÃO ULTIMATE:")
        print(f"   Perfeitas (100%):     {all_perfect:4d} ({all_perfect/len(predictions)*100:5.1f}%)")
        print(f"   Altas (70-99%):       {all_high:4d} ({all_high/len(predictions)*100:5.1f}%)")
        print(f"   Médias (40-69%):      {all_medium:4d} ({all_medium/len(predictions)*100:5.1f}%)")
        print(f"   Baixas (<40%):        {len(predictions)-all_perfect-all_high-all_medium:4d}")

        return avg_accuracy

    def run_ultimate_training(self, num_samples=8000):
        """Pipeline ultimate completo"""
        print("🚀 ULTIMATE HASH PREDICTOR - MÁXIMA ACURÁCIA")
        print("=" * 60)

        total_start = time.time()

        # 1. Gerar dataset ultimate
        X, y, keys = self.generate_dataset_ultimate(num_samples=num_samples)

        if X is None:
            print("❌ Falha na geração do dataset")
            return 0

        # 2. Dividir dados
        test_size = min(800, len(X) // 8)

        indices = np.random.permutation(len(X))
        X_test = X[indices[:test_size]]
        y_test = y[indices[:test_size]]
        test_keys = [keys[i] for i in indices[:test_size]]

        X_train = X[indices[test_size:]]
        y_train = y[indices[test_size:]]

        print(f"\n📊 DIVISÃO ULTIMATE:")
        print(f"Treino: {len(X_train):,} amostras")
        print(f"Teste: {len(X_test):,} amostras")

        # 3. Criar e treinar modelo ultimate
        self.model = UltimateNeuralNetwork(use_gpu=self.use_gpu)
        self.model.train_ultimate(X_train, y_train, epochs=1000, batch_size=8)

        # 4. Avaliar
        accuracy = self.evaluate_ultimate(X_test, y_test, test_keys)

        total_time = time.time() - total_start

        print(f"\n🎊 TREINAMENTO ULTIMATE CONCLUÍDO!")
        print(f"⏱️  Tempo total: {total_time:.1f} segundos")
        print(f"📈 Acurácia final: {accuracy:.1f}%")

        if accuracy > 40:
            print("🏆 BREAKTHROUGH! Acurácia muito alta alcançada!")
        elif accuracy > 25:
            print("🎉 EXCELENTE! Progresso significativo!")
        elif accuracy > 15:
            print("📈 BOM! Melhor que versões anteriores!")
        else:
            print("⚠️  Ainda precisa melhorar")

        return accuracy

    def predict_magic_number_ultimate(self, hash160_correto_hex):
        """Predição ultimate"""
        if self.model is None:
            print("❌ Modelo não treinado!")
            return None

        input_features = hex_to_multi_representation(hash160_correto_hex).reshape(1, -1)
        prediction = self.model.predict_ultimate(input_features)

        return multi_representation_to_hex(prediction[0])

def main():
    """Função principal ultimate"""
    print("🚀 ULTIMATE NEURAL HASH PREDICTOR")
    print("=" * 45)

    # Verificar status
    if GPU_AVAILABLE:
        print("✅ CUDA disponível")
    else:
        print("⚠️  Usando CPU")

    print(f"\n🧠 TÉCNICAS ULTIMATE IMPLEMENTADAS:")
    print("• Multi-representation: 958 features por hash")
    print("• Skip connections: Residual learning")
    print("• Layer normalization: Estabilização")
    print("• Swish activation: Melhor convergência")
    print("• Momentum optimization: Velocidade")
    print("• Smooth L1 loss: Robustez")
    print("• Cosine annealing: Learning rate adaptativo")
    print("• Dropout adaptativo: Regularização")
    print("• Análise de padrões: Pré-processamento")

    # Configuração
    num_samples = int(input("\nNúmero de amostras (recomendado: 8000): ") or "8000")

    # Executar
    predictor = UltimateHashPredictor()
    accuracy = predictor.run_ultimate_training(num_samples=num_samples)

    if accuracy > 0:
        print(f"\n🎯 RESULTADO ULTIMATE: {accuracy:.1f}% de acurácia")

        if accuracy > 20:
            # Teste interativo
            print(f"\n🧪 TESTE ULTIMATE:")
            while True:
                hash_input = input("\nHash160 correto (40 chars) ou 'quit': ").strip()

                if hash_input.lower() == 'quit':
                    break

                if len(hash_input) != 40:
                    print("❌ Hash deve ter 40 caracteres")
                    continue

                if not all(c in '0123456789abcdef' for c in hash_input.lower()):
                    print("❌ Hash deve conter apenas caracteres hexadecimais")
                    continue

                magic_number = predictor.predict_magic_number_ultimate(hash_input.lower())
                print(f"Hash errado/mágico ultimate: {magic_number}")
                print(f"🚀 Comando: ./buscar_chave_por_hash_errado {magic_number} 1:1000000")

if __name__ == "__main__":
    main()
