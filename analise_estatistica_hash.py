#!/usr/bin/env python3
"""
ANÁLISE ESTATÍSTICA AVANÇADA
Análises estatísticas profundas dos padrões entre hash correto e errado
"""

from main import private_key_to_address, simular_gpu_errada_para_chave
from bitcoin_conversions import calculate_target_hash160
import statistics

def calcular_entropia(hash_hex):
    """
    Calcula entropia de um hash
    """
    # Contar frequência de cada byte
    bytes_hash = bytes.fromhex(hash_hex)
    freq = {}
    
    for byte in bytes_hash:
        freq[byte] = freq.get(byte, 0) + 1
    
    # Calcular entropia
    total = len(bytes_hash)
    entropia = 0
    
    for count in freq.values():
        p = count / total
        if p > 0:
            entropia -= p * (p.bit_length() - 1)  # Aproximação de log2
    
    return entropia

def analisar_distribuicao_bytes(carteiras):
    """
    Analisa distribuição de bytes nos hashes
    """
    print("📊 ANÁLISE DE DISTRIBUIÇÃO DE BYTES")
    print("=" * 40)
    
    # Coletar todos os bytes
    bytes_correto = []
    bytes_errado = []
    
    for carteira in carteiras:
        hash_correto = carteira['hash160_correto']
        hash_errado = carteira['hash160_errado']
        
        bytes_correto.extend(bytes.fromhex(hash_correto))
        bytes_errado.extend(bytes.fromhex(hash_errado))
    
    # Estatísticas
    print(f"Total de bytes analisados: {len(bytes_correto)} (cada tipo)")
    
    print(f"\nHash Correto:")
    print(f"  Média: {statistics.mean(bytes_correto):.2f}")
    print(f"  Mediana: {statistics.median(bytes_correto):.2f}")
    print(f"  Desvio padrão: {statistics.stdev(bytes_correto):.2f}")
    print(f"  Min: {min(bytes_correto)}, Max: {max(bytes_correto)}")
    
    print(f"\nHash Errado:")
    print(f"  Média: {statistics.mean(bytes_errado):.2f}")
    print(f"  Mediana: {statistics.median(bytes_errado):.2f}")
    print(f"  Desvio padrão: {statistics.stdev(bytes_errado):.2f}")
    print(f"  Min: {min(bytes_errado)}, Max: {max(bytes_errado)}")
    
    # Distribuição por valor
    print(f"\n📈 DISTRIBUIÇÃO POR VALOR (0-255):")
    
    freq_correto = [0] * 256
    freq_errado = [0] * 256
    
    for byte in bytes_correto:
        freq_correto[byte] += 1
    
    for byte in bytes_errado:
        freq_errado[byte] += 1
    
    print(f"{'Valor':>5} | {'Freq Correto':>12} | {'Freq Errado':>11} | {'Diferença':>10}")
    print("-" * 50)
    
    for i in range(0, 256, 16):  # Mostrar a cada 16 valores
        total_correto = sum(freq_correto[i:i+16])
        total_errado = sum(freq_errado[i:i+16])
        diferenca = abs(total_correto - total_errado)
        
        print(f"{i:3d}-{i+15:3d} | {total_correto:12d} | {total_errado:11d} | {diferenca:10d}")

def analisar_transformacao_matematica(carteiras):
    """
    Analisa se há transformação matemática entre hash correto e errado
    """
    print("\n🔢 ANÁLISE DE TRANSFORMAÇÃO MATEMÁTICA")
    print("=" * 45)
    
    print("Testando possíveis transformações matemáticas...")
    
    transformacoes = []
    
    for carteira in carteiras:
        chave = carteira['chave_privada']
        hash_correto = carteira['hash160_correto']
        hash_errado = carteira['hash160_errado']
        
        # Converter para inteiros
        int_correto = int(hash_correto, 16)
        int_errado = int(hash_errado, 16)
        
        # Testar diferentes transformações
        diferenca = int_errado - int_correto
        razao = int_errado / int_correto if int_correto != 0 else 0
        xor = int_correto ^ int_errado
        
        transformacao = {
            'chave': chave,
            'diferenca': diferenca,
            'razao': razao,
            'xor': xor,
            'int_correto': int_correto,
            'int_errado': int_errado
        }
        
        transformacoes.append(transformacao)
    
    # Analisar padrões nas transformações
    diferencas = [t['diferenca'] for t in transformacoes]
    razoes = [t['razao'] for t in transformacoes if t['razao'] > 0]
    xors = [t['xor'] for t in transformacoes]
    
    print(f"\n📊 ESTATÍSTICAS DAS TRANSFORMAÇÕES:")
    
    if diferencas:
        print(f"Diferenças (errado - correto):")
        print(f"  Média: {statistics.mean(diferencas):.2e}")
        print(f"  Mediana: {statistics.median(diferencas):.2e}")
        print(f"  Desvio padrão: {statistics.stdev(diferencas):.2e}")
    
    if razoes:
        print(f"\nRazões (errado / correto):")
        print(f"  Média: {statistics.mean(razoes):.6f}")
        print(f"  Mediana: {statistics.median(razoes):.6f}")
        print(f"  Desvio padrão: {statistics.stdev(razoes):.6f}")
    
    if xors:
        print(f"\nXOR (correto XOR errado):")
        print(f"  Média: {statistics.mean(xors):.2e}")
        print(f"  Mediana: {statistics.median(xors):.2e}")
        print(f"  Desvio padrão: {statistics.stdev(xors):.2e}")

def analisar_dependencia_chave_privada(carteiras):
    """
    Analisa dependência do hash errado com a chave privada
    """
    print("\n🔑 DEPENDÊNCIA COM CHAVE PRIVADA")
    print("=" * 35)
    
    print(f"{'Chave':>5} | {'Hash Errado (primeiros 8 chars)':>30} | {'Padrão':>10}")
    print("-" * 55)
    
    padroes = {}
    
    for carteira in carteiras:
        chave = carteira['chave_privada']
        hash_errado = carteira['hash160_errado']
        
        # Analisar primeiros 8 caracteres
        prefixo = hash_errado[:8]
        
        # Verificar se há relação com a chave
        chave_hex = f"{chave:08x}"  # Chave em hex com 8 dígitos
        
        # Calcular "distância" entre chave e prefixo do hash
        distancia = 0
        for i in range(min(len(chave_hex), len(prefixo))):
            if chave_hex[i] != prefixo[i]:
                distancia += 1
        
        padrao = f"dist={distancia}"
        padroes[padrao] = padroes.get(padrao, 0) + 1
        
        print(f"{chave:5d} | {prefixo:>30} | {padrao:>10}")
    
    print(f"\n📊 DISTRIBUIÇÃO DE PADRÕES:")
    for padrao, count in sorted(padroes.items()):
        print(f"  {padrao}: {count} ocorrências")

def testar_reversibilidade(carteiras):
    """
    Testa se é possível reverter hash errado para correto
    """
    print("\n🔄 TESTE DE REVERSIBILIDADE")
    print("=" * 30)
    
    print("Testando se hash errado pode ser revertido para correto...")
    
    # Tentar encontrar função de mapeamento
    mapeamentos = []
    
    for i, carteira in enumerate(carteiras):
        hash_correto = carteira['hash160_correto']
        hash_errado = carteira['hash160_errado']
        
        # Testar mapeamento byte a byte
        mapeamento_bytes = []
        
        for j in range(0, 40, 2):
            byte_correto = int(hash_correto[j:j+2], 16)
            byte_errado = int(hash_errado[j:j+2], 16)
            
            # Calcular transformação
            diferenca = (byte_errado - byte_correto) % 256
            mapeamento_bytes.append(diferenca)
        
        mapeamentos.append(mapeamento_bytes)
    
    # Analisar consistência dos mapeamentos
    print(f"\n📊 CONSISTÊNCIA DOS MAPEAMENTOS:")
    
    for pos in range(20):  # 20 bytes
        valores = [m[pos] for m in mapeamentos]
        
        if len(set(valores)) == 1:
            print(f"  Posição {pos:2d}: CONSTANTE ({valores[0]})")
        else:
            print(f"  Posição {pos:2d}: VARIÁVEL ({min(valores)}-{max(valores)})")

def gerar_relatorio_completo(carteiras):
    """
    Gera relatório completo da análise
    """
    print("\n📋 RELATÓRIO COMPLETO")
    print("=" * 25)
    
    total_carteiras = len(carteiras)
    
    # Calcular estatísticas gerais
    distancias_hamming = []
    entropias_correto = []
    entropias_errado = []
    
    for carteira in carteiras:
        hash_correto = carteira['hash160_correto']
        hash_errado = carteira['hash160_errado']
        
        # Distância de Hamming
        bytes_correto = bytes.fromhex(hash_correto)
        bytes_errado = bytes.fromhex(hash_errado)
        
        distancia = 0
        for b1, b2 in zip(bytes_correto, bytes_errado):
            xor = b1 ^ b2
            distancia += bin(xor).count('1')
        
        distancias_hamming.append(distancia)
        
        # Entropias
        entropias_correto.append(calcular_entropia(hash_correto))
        entropias_errado.append(calcular_entropia(hash_errado))
    
    print(f"📊 RESUMO ESTATÍSTICO:")
    print(f"  Total de carteiras analisadas: {total_carteiras}")
    print(f"  Distância de Hamming média: {statistics.mean(distancias_hamming):.1f} bits")
    print(f"  Entropia média (correto): {statistics.mean(entropias_correto):.3f}")
    print(f"  Entropia média (errado): {statistics.mean(entropias_errado):.3f}")
    
    print(f"\n🎯 CONCLUSÕES:")
    
    # Verificar se há padrões
    if statistics.stdev(distancias_hamming) < 5:
        print("  ✅ Distância de Hamming consistente - possível padrão")
    else:
        print("  ❌ Distância de Hamming variável - sem padrão claro")
    
    if abs(statistics.mean(entropias_correto) - statistics.mean(entropias_errado)) < 0.1:
        print("  ✅ Entropias similares - transformação preserva aleatoriedade")
    else:
        print("  ❌ Entropias diferentes - transformação altera aleatoriedade")

def main():
    """Função principal"""
    print("📊 ANÁLISE ESTATÍSTICA AVANÇADA - HASH PATTERNS")
    print("=" * 60)
    
    # Gerar dados das 20 primeiras carteiras
    print("Gerando dados das 20 primeiras carteiras...")
    
    carteiras = []
    for chave in range(1, 21):
        try:
            endereco = private_key_to_address(chave)
            hash_correto = calculate_target_hash160(endereco).hex()
            hash_errado = simular_gpu_errada_para_chave(chave).hex()
            
            carteiras.append({
                'chave_privada': chave,
                'endereco': endereco,
                'hash160_correto': hash_correto,
                'hash160_errado': hash_errado
            })
            
        except Exception as e:
            print(f"Erro na chave {chave}: {e}")
    
    print(f"✅ {len(carteiras)} carteiras processadas")
    
    # Executar análises
    analisar_distribuicao_bytes(carteiras)
    analisar_transformacao_matematica(carteiras)
    analisar_dependencia_chave_privada(carteiras)
    testar_reversibilidade(carteiras)
    gerar_relatorio_completo(carteiras)
    
    print(f"\n🎊 ANÁLISE ESTATÍSTICA CONCLUÍDA!")

if __name__ == "__main__":
    main()
