#!/usr/bin/env python3
"""
REDE NEURAL ULTIMATE CORRIGIDA
Versão com dimensões corretas e técnicas avançadas
"""

import numpy as np
import time
import json
import random
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed

# Tentar importar CuPy
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("✅ CuPy disponível - GPU habilitada")
except ImportError:
    import numpy as cp
    GPU_AVAILABLE = False
    print("⚠️  CuPy não encontrado - usando CPU")

# Importar funções necessárias
try:
    from test_magic_number import simular_gpu_errada_para_chave
    from main import private_key_to_address
    from bitcoin_conversions import calculate_target_hash160
    print("✅ Módulos Bitcoin importados")
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    exit(1)

def hex_to_enhanced_features_fixed(hex_string):
    """Converte hex para features melhoradas com dimensão fixa"""
    hex_string = hex_string.ljust(40, '0')[:40]
    
    features = []
    
    # 1. One-hot encoding (640 features)
    one_hot = np.zeros(640, dtype=np.float32)
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            one_hot[i * 16 + int(char, 16)] = 1.0
    features.extend(one_hot)
    
    # 2. Valores numéricos (40 features)
    numeric = np.zeros(40, dtype=np.float32)
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            numeric[i] = int(char, 16) / 15.0
    features.extend(numeric)
    
    # 3. Valores binários (160 features - 4 bits por char)
    binary = np.zeros(160, dtype=np.float32)
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            val = int(char, 16)
            for bit in range(4):
                binary[i * 4 + bit] = float((val >> bit) & 1)
    features.extend(binary)
    
    # 4. Diferenças entre caracteres adjacentes (39 features)
    diffs = np.zeros(39, dtype=np.float32)
    for i in range(39):
        char1 = hex_string[i] if hex_string[i] in '0123456789abcdef' else '0'
        char2 = hex_string[i+1] if hex_string[i+1] in '0123456789abcdef' else '0'
        diff = (int(char2, 16) - int(char1, 16)) % 16
        diffs[i] = diff / 15.0
    features.extend(diffs)
    
    # 5. Somas de grupos (20 features - grupos de 2 chars)
    sums = np.zeros(20, dtype=np.float32)
    for i in range(0, 40, 2):
        if i+1 < 40:
            char1 = hex_string[i] if hex_string[i] in '0123456789abcdef' else '0'
            char2 = hex_string[i+1] if hex_string[i+1] in '0123456789abcdef' else '0'
            sum_val = (int(char1, 16) + int(char2, 16)) % 16
            sums[i//2] = sum_val / 15.0
    features.extend(sums)
    
    # 6. XOR de grupos (20 features)
    xors = np.zeros(20, dtype=np.float32)
    for i in range(0, 40, 2):
        if i+1 < 40:
            char1 = hex_string[i] if hex_string[i] in '0123456789abcdef' else '0'
            char2 = hex_string[i+1] if hex_string[i+1] in '0123456789abcdef' else '0'
            xor_val = int(char1, 16) ^ int(char2, 16)
            xors[i//2] = xor_val / 15.0
    features.extend(xors)
    
    # 7. Paridade (20 features)
    parity = np.zeros(20, dtype=np.float32)
    for i in range(0, 40, 2):
        if i+1 < 40:
            char1 = hex_string[i] if hex_string[i] in '0123456789abcdef' else '0'
            char2 = hex_string[i+1] if hex_string[i+1] in '0123456789abcdef' else '0'
            par_val = (int(char1, 16) + int(char2, 16)) % 2
            parity[i//2] = float(par_val)
    features.extend(parity)
    
    # 8. Estatísticas globais (19 features para completar 958)
    values = [int(c, 16) if c in '0123456789abcdef' else 0 for c in hex_string]
    stats = [
        np.mean(values) / 15.0,           # média
        np.std(values) / 15.0,            # desvio padrão
        np.min(values) / 15.0,            # mínimo
        np.max(values) / 15.0,            # máximo
        np.median(values) / 15.0,         # mediana
        len(set(values)) / 16.0,          # diversidade
        sum(1 for v in values if v > 7) / 40.0,  # proporção alta
        sum(1 for v in values if v < 8) / 40.0,  # proporção baixa
        sum(1 for i in range(1, 40) if values[i] > values[i-1]) / 39.0,  # tendência crescente
        sum(1 for i in range(1, 40) if values[i] == values[i-1]) / 39.0,  # repetições
        sum(1 for v in values if v % 2 == 0) / 40.0,  # pares
        sum(1 for v in values if v % 2 == 1) / 40.0,  # ímpares
        sum(values[:20]) / (15.0 * 20),   # soma primeira metade
        sum(values[20:]) / (15.0 * 20),   # soma segunda metade
        np.var(values[:20]) / 225.0,      # variância primeira metade
        np.var(values[20:]) / 225.0,      # variância segunda metade
        sum(1 for i in range(0, 40, 4) if values[i] == values[i+2] if i+2 < 40) / 10.0,  # padrão 4
        sum(1 for i in range(0, 40, 8) if values[i] == values[i+4] if i+4 < 40) / 5.0,   # padrão 8
        float(sum(values) % 16) / 15.0    # checksum
    ]
    features.extend(stats)
    
    # Garantir exatamente 958 features
    result = np.array(features, dtype=np.float32)
    if len(result) != 958:
        # Ajustar para 958
        if len(result) > 958:
            result = result[:958]
        else:
            padding = np.zeros(958 - len(result), dtype=np.float32)
            result = np.concatenate([result, padding])
    
    return result

def enhanced_features_to_hex_fixed(features):
    """Converte features de volta para hex usando one-hot"""
    one_hot = features[:640].reshape(40, 16)
    hex_chars = '0123456789abcdef'
    return ''.join(hex_chars[np.argmax(row)] for row in one_hot)

def generate_ultimate_sample_fixed(private_key):
    """Gera amostra com features corrigidas"""
    try:
        endereco = private_key_to_address(private_key)
        if not endereco:
            return None, None, None
        
        hash160_correto = calculate_target_hash160(endereco)
        if not hash160_correto:
            return None, None, None
        
        numero_magico = simular_gpu_errada_para_chave(private_key)
        if not numero_magico:
            return None, None, None
        
        input_features = hex_to_enhanced_features_fixed(hash160_correto.hex())
        output_features = hex_to_enhanced_features_fixed(numero_magico.hex())
        
        return input_features, output_features, private_key
        
    except Exception:
        return None, None, None

def generate_batch_ultimate_fixed(key_batch):
    """Gera batch com features corrigidas"""
    inputs = []
    outputs = []
    successful_keys = []
    
    for private_key in key_batch:
        input_vec, output_vec, key = generate_ultimate_sample_fixed(private_key)
        
        if input_vec is not None and output_vec is not None:
            inputs.append(input_vec)
            outputs.append(output_vec)
            successful_keys.append(key)
    
    return inputs, outputs, successful_keys

class FixedUltimateNeuralNetwork:
    """Rede neural ultimate com dimensões corrigidas"""
    
    def __init__(self, use_gpu=True):
        self.use_gpu = use_gpu and GPU_AVAILABLE
        self.xp = cp if self.use_gpu else np
        
        # Arquitetura corrigida
        self.layers = [958, 1024, 1536, 1024, 958]  # Mais simples mas eficiente
        
        print(f"🧠 Fixed Ultimate Neural Network")
        print(f"🔧 Dispositivo: {'GPU' if self.use_gpu else 'CPU'}")
        print(f"📊 Arquitetura: {' → '.join(map(str, self.layers))}")
        print(f"📊 Features: 958 (corrigidas)")
        
        # Inicialização Xavier
        self.weights = []
        self.biases = []
        
        for i in range(len(self.layers) - 1):
            fan_in = self.layers[i]
            fan_out = self.layers[i + 1]
            
            limit = self.xp.sqrt(6.0 / (fan_in + fan_out))
            w = self.xp.random.uniform(-limit, limit, (fan_in, fan_out)).astype(self.xp.float32)
            b = self.xp.zeros((1, fan_out), dtype=self.xp.float32)
            
            self.weights.append(w)
            self.biases.append(b)
        
        total_params = sum(w.size for w in self.weights) + sum(b.size for b in self.biases)
        print(f"📊 Parâmetros: {total_params:,}")
    
    def swish(self, x):
        """Swish activation"""
        return x / (1 + self.xp.exp(-self.xp.clip(x, -10, 10)))
    
    def swish_derivative(self, x):
        """Derivada Swish"""
        sigmoid = 1 / (1 + self.xp.exp(-self.xp.clip(x, -10, 10)))
        return sigmoid + x * sigmoid * (1 - sigmoid)
    
    def layer_norm(self, x, eps=1e-6):
        """Layer normalization"""
        mean = self.xp.mean(x, axis=1, keepdims=True)
        var = self.xp.var(x, axis=1, keepdims=True)
        return (x - mean) / self.xp.sqrt(var + eps)
    
    def forward(self, X):
        """Forward pass corrigido"""
        if self.use_gpu and not isinstance(X, cp.ndarray):
            X = cp.asarray(X)
        elif not self.use_gpu and isinstance(X, cp.ndarray):
            X = cp.asnumpy(X)
        
        activations = [X]
        z_values = []
        
        for i, (w, b) in enumerate(zip(self.weights, self.biases)):
            z = self.xp.dot(activations[-1], w) + b
            z_values.append(z)
            
            if i < len(self.weights) - 1:
                # Camadas ocultas: Swish + Layer Norm
                a = self.swish(z)
                a = self.layer_norm(a)
                
                # Dropout simples
                if i > 0:
                    dropout_mask = self.xp.random.random(a.shape) > 0.1
                    a = a * dropout_mask / 0.9
                
            else:
                # Camada de saída
                batch_size = z.shape[0]
                
                # One-hot part (primeiros 640): softmax agrupado
                one_hot_part = z[:, :640].reshape(batch_size, 40, 16)
                exp_vals = self.xp.exp(one_hot_part - self.xp.max(one_hot_part, axis=2, keepdims=True))
                softmax_part = exp_vals / self.xp.sum(exp_vals, axis=2, keepdims=True)
                softmax_flat = softmax_part.reshape(batch_size, 640)
                
                # Outras features: tanh
                other_part = self.xp.tanh(z[:, 640:])
                
                a = self.xp.concatenate([softmax_flat, other_part], axis=1)
            
            activations.append(a)
        
        return activations, z_values
    
    def compute_loss(self, y_pred, y_true):
        """Loss function corrigida"""
        batch_size = y_pred.shape[0]
        
        # One-hot part: cross-entropy
        y_pred_oh = y_pred[:, :640]
        y_true_oh = y_true[:, :640]
        
        y_pred_oh_clipped = self.xp.clip(y_pred_oh, 1e-15, 1 - 1e-15)
        ce_loss = -self.xp.sum(y_true_oh * self.xp.log(y_pred_oh_clipped)) / batch_size
        
        # Outras features: MSE
        y_pred_other = y_pred[:, 640:]
        y_true_other = y_true[:, 640:]
        mse_loss = self.xp.mean((y_pred_other - y_true_other) ** 2)
        
        # Loss combinada
        total_loss = 0.7 * ce_loss + 0.3 * mse_loss
        
        return total_loss, ce_loss, mse_loss
    
    def backward(self, X, y, activations, z_values, learning_rate, l2_reg=0.0001):
        """Backward pass corrigido"""
        m = X.shape[0]
        
        # Inicializar momentum se necessário
        if not hasattr(self, 'momentum_w'):
            self.momentum_w = [self.xp.zeros_like(w) for w in self.weights]
            self.momentum_b = [self.xp.zeros_like(b) for b in self.biases]
        
        # Erro da saída
        output_error = activations[-1] - y
        
        # Backpropagation
        errors = [output_error]
        
        for i in range(len(self.weights) - 1, 0, -1):
            error = self.xp.dot(errors[-1], self.weights[i].T)
            error *= self.swish_derivative(z_values[i-1])
            errors.append(error)
        
        errors.reverse()
        
        # Atualizar pesos com momentum
        for i in range(len(self.weights)):
            dw = self.xp.dot(activations[i].T, errors[i]) / m
            db = self.xp.mean(errors[i], axis=0, keepdims=True)
            
            # Regularização L2
            dw += l2_reg * self.weights[i]
            
            # Gradient clipping
            grad_norm = self.xp.sqrt(self.xp.sum(dw ** 2))
            if grad_norm > 1.0:
                dw = dw / grad_norm
            
            # Momentum
            self.momentum_w[i] = 0.9 * self.momentum_w[i] + 0.1 * dw
            self.momentum_b[i] = 0.9 * self.momentum_b[i] + 0.1 * db
            
            self.weights[i] -= learning_rate * self.momentum_w[i]
            self.biases[i] -= learning_rate * self.momentum_b[i]
        
        # Loss
        total_loss, ce_loss, mse_loss = self.compute_loss(activations[-1], y)
        l2_loss = sum(self.xp.sum(w ** 2) for w in self.weights) * l2_reg
        
        return float(total_loss + l2_loss), float(ce_loss), float(mse_loss)

    def train(self, X, y, epochs=800, initial_lr=0.001, batch_size=32):
        """Treinamento corrigido"""
        print(f"\n🚀 TREINAMENTO FIXED ULTIMATE")
        print("=" * 35)

        if self.use_gpu:
            if not isinstance(X, cp.ndarray):
                X = cp.asarray(X)
            if not isinstance(y, cp.ndarray):
                y = cp.asarray(y)

        num_batches = len(X) // batch_size
        best_loss = float('inf')
        patience = 120
        patience_counter = 0

        print(f"Configuração:")
        print(f"  Amostras: {len(X):,}")
        print(f"  Batch size: {batch_size}")
        print(f"  Learning rate: {initial_lr}")
        print(f"  Técnicas: Swish + LayerNorm + Momentum")

        for epoch in range(epochs):
            # Learning rate schedule
            if epoch < 100:
                lr = initial_lr * (epoch + 1) / 100  # Warm-up
            elif epoch < 300:
                lr = initial_lr
            elif epoch < 600:
                lr = initial_lr * 0.5
            else:
                lr = initial_lr * 0.2

            # Embaralhar dados
            if self.use_gpu:
                indices = cp.random.permutation(len(X))
            else:
                indices = np.random.permutation(len(X))

            X_shuffled = X[indices]
            y_shuffled = y[indices]

            epoch_total_loss = 0.0
            epoch_ce_loss = 0.0
            epoch_mse_loss = 0.0

            # Treinar por batches
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = start_idx + batch_size

                X_batch = X_shuffled[start_idx:end_idx]
                y_batch = y_shuffled[start_idx:end_idx]

                # Forward pass
                activations, z_values = self.forward(X_batch)

                # Backward pass
                total_loss, ce_loss, mse_loss = self.backward(
                    X_batch, y_batch, activations, z_values, lr
                )

                epoch_total_loss += total_loss
                epoch_ce_loss += ce_loss
                epoch_mse_loss += mse_loss

            epoch_total_loss /= num_batches
            epoch_ce_loss /= num_batches
            epoch_mse_loss /= num_batches

            # Early stopping
            if epoch_total_loss < best_loss:
                best_loss = epoch_total_loss
                patience_counter = 0
            else:
                patience_counter += 1

            if patience_counter >= patience:
                print(f"\n⏰ Early stopping na época {epoch+1}")
                break

            # Log progresso
            if (epoch + 1) % 40 == 0:
                print(f"Época {epoch+1:3d}/{epochs} | "
                      f"Total: {epoch_total_loss:.6f} | "
                      f"CE: {epoch_ce_loss:.6f} | "
                      f"MSE: {epoch_mse_loss:.6f} | "
                      f"LR: {lr:.6f}")

        print(f"\n✅ Treinamento concluído!")
        print(f"📈 Melhor loss: {best_loss:.6f}")

        return best_loss

    def predict(self, X):
        """Predição"""
        activations, _ = self.forward(X)
        result = activations[-1]

        if self.use_gpu and isinstance(result, cp.ndarray):
            result = cp.asnumpy(result)

        return result

class FixedUltimateHashPredictor:
    """Preditor ultimate corrigido"""

    def __init__(self):
        self.model = None
        self.use_gpu = GPU_AVAILABLE

        print(f"🚀 Fixed Ultimate Hash Predictor")
        print(f"🔧 GPU: {'Habilitada' if self.use_gpu else 'Desabilitada'}")

    def generate_dataset(self, num_samples=6000, start_key=1, end_key=20000):
        """Gera dataset corrigido"""
        print(f"🚀 GERANDO DATASET FIXED ULTIMATE: {num_samples:,} AMOSTRAS")
        print("=" * 60)

        # Usar chaves menores para padrões mais claros
        all_keys = random.sample(range(start_key, min(end_key, 15000)),
                                min(num_samples, 14999))

        # Processamento paralelo
        num_processes = min(mp.cpu_count(), 6)
        batch_size = len(all_keys) // num_processes

        key_batches = [all_keys[i:i + batch_size]
                      for i in range(0, len(all_keys), batch_size)]

        print(f"🔄 Processamento: {num_processes} processos")
        print(f"📊 Chaves: {start_key} a {min(end_key, 15000)}")

        all_inputs = []
        all_outputs = []
        all_keys = []

        start_time = time.time()

        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            futures = [executor.submit(generate_batch_ultimate_fixed, batch)
                      for batch in key_batches]

            for i, future in enumerate(as_completed(futures)):
                try:
                    inputs, outputs, keys = future.result()
                    all_inputs.extend(inputs)
                    all_outputs.extend(outputs)
                    all_keys.extend(keys)

                    print(f"   Batch {i+1}/{len(key_batches)}: {len(inputs)} amostras")

                except Exception as e:
                    print(f"   ❌ Erro batch {i+1}: {e}")

        generation_time = time.time() - start_time

        print(f"\n📊 DATASET FIXED ULTIMATE:")
        print(f"✅ Amostras: {len(all_inputs):,}")
        print(f"⏱️  Tempo: {generation_time:.1f}s")
        print(f"📊 Features por amostra: {len(all_inputs[0]) if all_inputs else 0}")

        if len(all_inputs) == 0:
            return None, None, None

        return np.array(all_inputs, dtype=np.float32), np.array(all_outputs, dtype=np.float32), all_keys

    def evaluate(self, X_test, y_test, test_keys):
        """Avaliação corrigida"""
        print(f"\n📊 AVALIAÇÃO FIXED ULTIMATE")
        print("=" * 30)

        predictions = self.model.predict(X_test)

        print(f"🔍 AMOSTRAS DE TESTE (primeiras 8):")
        print("-" * 90)

        accuracies = []
        perfect_matches = 0
        high_accuracy = 0  # >60%
        medium_accuracy = 0  # 30-60%

        for i in range(min(8, len(predictions))):
            key = test_keys[i]
            input_hash = enhanced_features_to_hex_fixed(X_test[i])
            expected_output = enhanced_features_to_hex_fixed(y_test[i])
            predicted_output = enhanced_features_to_hex_fixed(predictions[i])

            # Acurácia por caractere
            correct_chars = sum(1 for a, b in zip(expected_output, predicted_output) if a == b)
            accuracy = (correct_chars / 40) * 100
            accuracies.append(accuracy)

            if accuracy == 100:
                perfect_matches += 1
            elif accuracy > 60:
                high_accuracy += 1
            elif accuracy > 30:
                medium_accuracy += 1

            print(f"Chave {key:x}:")
            print(f"  INPUT (hash correto):     {input_hash}")
            print(f"  OUTPUT esperado (errado): {expected_output}")
            print(f"  OUTPUT predito (errado):  {predicted_output}")
            print(f"  Acurácia: {accuracy:5.1f}% ({correct_chars:2d}/40)")

            # Mostrar erros
            if accuracy < 100:
                errors = [(j, expected_output[j], predicted_output[j]) for j in range(40)
                         if expected_output[j] != predicted_output[j]]
                if len(errors) <= 8:
                    error_str = ", ".join([f"{pos}:{exp}→{pred}" for pos, exp, pred in errors])
                    print(f"  Erros:    {error_str}")
                else:
                    print(f"  Erros:    {len(errors)} posições diferentes")
            print()

        # Estatísticas completas
        all_accuracies = []
        all_perfect = 0
        all_high = 0
        all_medium = 0

        for i in range(len(predictions)):
            expected_output = enhanced_features_to_hex_fixed(y_test[i])
            predicted_output = enhanced_features_to_hex_fixed(predictions[i])
            correct_chars = sum(1 for a, b in zip(expected_output, predicted_output) if a == b)
            accuracy = (correct_chars / 40) * 100
            all_accuracies.append(accuracy)

            if accuracy == 100:
                all_perfect += 1
            elif accuracy > 60:
                all_high += 1
            elif accuracy > 30:
                all_medium += 1

        avg_accuracy = np.mean(all_accuracies)
        median_accuracy = np.median(all_accuracies)
        max_accuracy = np.max(all_accuracies)

        print(f"📈 ESTATÍSTICAS FIXED ULTIMATE:")
        print(f"   Acurácia média:      {avg_accuracy:6.1f}%")
        print(f"   Acurácia mediana:    {median_accuracy:6.1f}%")
        print(f"   Acurácia máxima:     {max_accuracy:6.1f}%")
        print(f"   Perfeitas (100%):    {all_perfect:4d} ({all_perfect/len(predictions)*100:5.1f}%)")
        print(f"   Altas (60-99%):      {all_high:4d} ({all_high/len(predictions)*100:5.1f}%)")
        print(f"   Médias (30-59%):     {all_medium:4d} ({all_medium/len(predictions)*100:5.1f}%)")
        print(f"   Baixas (<30%):       {len(predictions)-all_perfect-all_high-all_medium:4d}")

        return avg_accuracy

    def run_training(self, num_samples=6000):
        """Pipeline completo corrigido"""
        print("🚀 FIXED ULTIMATE HASH PREDICTOR")
        print("=" * 40)

        total_start = time.time()

        # 1. Gerar dataset
        X, y, keys = self.generate_dataset(num_samples=num_samples)

        if X is None:
            print("❌ Falha na geração do dataset")
            return 0

        # 2. Dividir dados
        test_size = min(600, len(X) // 8)

        indices = np.random.permutation(len(X))
        X_test = X[indices[:test_size]]
        y_test = y[indices[:test_size]]
        test_keys = [keys[i] for i in indices[:test_size]]

        X_train = X[indices[test_size:]]
        y_train = y[indices[test_size:]]

        print(f"\n📊 DIVISÃO:")
        print(f"Treino: {len(X_train):,} amostras")
        print(f"Teste: {len(X_test):,} amostras")

        # 3. Criar e treinar modelo
        self.model = FixedUltimateNeuralNetwork(use_gpu=self.use_gpu)
        self.model.train(X_train, y_train, epochs=600, batch_size=16)

        # 4. Avaliar
        accuracy = self.evaluate(X_test, y_test, test_keys)

        total_time = time.time() - total_start

        print(f"\n🎊 TREINAMENTO FIXED ULTIMATE CONCLUÍDO!")
        print(f"⏱️  Tempo total: {total_time:.1f} segundos")
        print(f"📈 Acurácia final: {accuracy:.1f}%")

        if accuracy > 30:
            print("🎉 EXCELENTE! Breakthrough alcançado!")
        elif accuracy > 20:
            print("🚀 MUITO BOM! Progresso significativo!")
        elif accuracy > 12:
            print("📈 BOM! Melhor que versões anteriores!")
        else:
            print("⚠️  Ainda precisa melhorar")

        return accuracy

    def predict_magic_number(self, hash160_correto_hex):
        """Predição corrigida"""
        if self.model is None:
            print("❌ Modelo não treinado!")
            return None

        input_features = hex_to_enhanced_features_fixed(hash160_correto_hex).reshape(1, -1)
        prediction = self.model.predict(input_features)

        return enhanced_features_to_hex_fixed(prediction[0])

def main():
    """Função principal corrigida"""
    print("🚀 FIXED ULTIMATE NEURAL HASH PREDICTOR")
    print("=" * 50)

    # Verificar status
    if GPU_AVAILABLE:
        print("✅ CUDA disponível")
    else:
        print("⚠️  Usando CPU")

    print(f"\n🔧 CORREÇÕES IMPLEMENTADAS:")
    print("• Dimensões fixas: 958 features exatas")
    print("• Arquitetura simplificada: 5 camadas")
    print("• Features melhoradas: múltiplas representações")
    print("• Técnicas avançadas: Swish + LayerNorm + Momentum")
    print("• Treinamento estável: sem erros de dimensão")

    # Configuração
    num_samples = int(input("\nNúmero de amostras (recomendado: 6000): ") or "6000")

    # Executar
    predictor = FixedUltimateHashPredictor()
    accuracy = predictor.run_training(num_samples=num_samples)

    if accuracy > 0:
        print(f"\n🎯 RESULTADO FIXED ULTIMATE: {accuracy:.1f}% de acurácia")

        if accuracy > 15:
            # Teste interativo
            print(f"\n🧪 TESTE FIXED ULTIMATE:")
            while True:
                hash_input = input("\nHash160 correto (40 chars) ou 'quit': ").strip()

                if hash_input.lower() == 'quit':
                    break

                if len(hash_input) != 40:
                    print("❌ Hash deve ter 40 caracteres")
                    continue

                if not all(c in '0123456789abcdef' for c in hash_input.lower()):
                    print("❌ Hash deve conter apenas caracteres hexadecimais")
                    continue

                magic_number = predictor.predict_magic_number(hash_input.lower())
                print(f"Hash errado/mágico: {magic_number}")
                print(f"🚀 Comando: ./buscar_chave_por_hash_errado {magic_number} 1:1000000")

if __name__ == "__main__":
    main()
