/*
DESCOBRIR HASH ERRADO - Programa CUDA
Encontra a chave privada no range e calcula seu hash errado
*/

#include <cuda_runtime.h>
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <time.h>

#ifdef _WIN32
    #include <windows.h>
    #define sleep_ms(ms) Sleep(ms)
#else
    #include <unistd.h>
    #define sleep_ms(ms) usleep((ms) * 1000)
#endif

// Constantes
#define GOLDEN_RATIO 0x9E3779B9ULL
#define MULT_CONST 0x9E
#define HASH_SIZE 20

// Coordenadas do ponto gerador secp256k1
__constant__ uint64_t gx[4] = {
    0x79BE667EF9DCBBACULL, 0x55A06295CE870B07ULL, 
    0x029BFCDB2DCE28D9ULL, 0x59F2815B16F81798ULL
};

// Resultado global
__device__ uint64_t d_chave_encontrada = 0;
__device__ int d_encontrou = 0;
__device__ uint8_t d_hash_errado_resultado[HASH_SIZE];

// Contadores de progresso
__device__ uint64_t d_chaves_testadas = 0;
__device__ uint64_t d_ultimo_progresso = 0;

// Função para calcular endereço Bitcoin (versão simplificada GPU)
__device__ bool gerar_endereco_gpu(uint64_t private_key, uint8_t* target_hash160) {
    // Esta é uma versão MUITO simplificada
    // Na prática, seria necessário implementar ECDSA completo na GPU
    // Por agora, vamos usar uma aproximação baseada na chave privada
    
    // Gerar coordenadas do ponto público
    uint64_t px[4];
    for (int i = 0; i < 4; i++) {
        px[i] = gx[i];
        // Multiplicação escalar simplificada (não é ECDSA real)
        for (int j = 0; j < 64; j++) {
            if ((private_key >> j) & 1) {
                px[i] ^= (px[i] << 1) ^ (px[i] >> 63);
            }
        }
    }
    
    // Simular SHA256 + RIPEMD160 (versão simplificada)
    uint8_t hash160_calculado[20];
    for (int i = 0; i < 20; i++) {
        hash160_calculado[i] = 0;
        for (int j = 0; j < 4; j++) {
            hash160_calculado[i] ^= (px[j] >> (i * 8)) & 0xFF;
        }
        // Adicionar dependência da chave privada
        hash160_calculado[i] ^= (private_key >> (i % 8)) & 0xFF;
        hash160_calculado[i] ^= (private_key >> ((i + 8) % 16)) & 0xFF;
    }
    
    // Comparar com target
    for (int i = 0; i < 20; i++) {
        if (hash160_calculado[i] != target_hash160[i]) {
            return false;
        }
    }
    
    return true;
}

__device__ void calcular_hash_errado_gpu(uint64_t private_key, uint8_t* hash_errado) {
    // Mesma função do programa anterior
    uint64_t px[4];
    for (int i = 0; i < 4; i++) {
        uint64_t rotacao = (private_key << (i * 8)) | (private_key >> (56 - i * 8));
        px[i] = gx[i] ^ rotacao;
        px[i] = ((px[i] << 1) ^ (px[i] >> 63));
    }
    
    uint8_t y_parity = 2 + ((private_key ^ px[0]) & 1);
    
    for (int i = 0; i < HASH_SIZE; i++) {
        uint32_t hash_val = 0;
        
        hash_val ^= (y_parity + i) & 0xFF;
        
        for (int j = 0; j < 4; j++) {
            for (int k = 0; k < 8; k++) {
                uint8_t coord_byte = (px[j] >> (56 - k * 8)) & 0xFF;
                hash_val ^= (coord_byte + i + j * 8 + k + 1) & 0xFF;
            }
        }
        
        hash_val ^= (private_key >> (i & 7)) & 0xFF;
        hash_val ^= (private_key >> ((i + 8) & 15)) & 0xFF;
        hash_val ^= (private_key * (i + 1)) & 0xFF;
        hash_val ^= ((private_key + i) * GOLDEN_RATIO) & 0xFF;
        
        hash_val &= 0xFF;
        hash_errado[i] = ((hash_val * MULT_CONST) ^ (hash_val >> 4)) & 0xFF;
    }
}

__global__ void descobrir_hash_errado_kernel(
    uint64_t start_range,
    uint64_t end_range,
    uint8_t* target_hash160
) {
    uint64_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    uint64_t stride = gridDim.x * blockDim.x;
    uint64_t thread_id = blockIdx.x * blockDim.x + threadIdx.x;

    for (uint64_t private_key = start_range + idx;
         private_key < end_range && !d_encontrou;
         private_key += stride) {

        // Atualizar contador de progresso (apenas thread 0)
        if (thread_id == 0) {
            atomicAdd((unsigned long long*)&d_chaves_testadas, stride);
        }

        // Verificar se esta chave gera o endereço alvo
        if (gerar_endereco_gpu(private_key, target_hash160)) {

            // ENCONTROU A CHAVE! Calcular hash errado
            uint8_t hash_errado_local[HASH_SIZE];
            calcular_hash_errado_gpu(private_key, hash_errado_local);

            // Salvar resultado global
            if (atomicCAS((unsigned long long*)&d_chave_encontrada, 0ULL, private_key) == 0ULL) {
                d_encontrou = 1;

                for (int i = 0; i < HASH_SIZE; i++) {
                    d_hash_errado_resultado[i] = hash_errado_local[i];
                }

                printf("🎉 GPU DESCOBRIU! Chave: %llu (0x%llx)\n", private_key, private_key);

                // Mostrar hash errado descoberto
                printf("🎯 Hash errado: ");
                for (int i = 0; i < HASH_SIZE; i++) {
                    printf("%02x", hash_errado_local[i]);
                }
                printf("\n");
            }
        }

        // Progresso detalhado a cada 10M chaves (apenas algumas threads)
        if ((private_key & 0x9FFFFF) == 0 && thread_id < 10) {
            printf("Thread %llu: Testando chave %llu (0x%llx)\n",
                   thread_id, private_key, private_key);
        }
    }
}

int main(int argc, char* argv[]) {
    printf("🔍 DESCOBRIR HASH ERRADO - PROGRAMA CUDA\n");
    printf("========================================\n");
    
    if (argc < 4) {
        printf("Uso: %s <hash160_correto> <start_range> <end_range>\n", argv[0]);
        printf("Exemplo: %s f6f5431d25bbf7b12e8add9af5e3475c44a0a5b8 4611686018427387904 9223372036854775807\n", argv[0]);
        return 1;
    }
    
    const char* target_hash160_hex = argv[1];
    uint64_t start_range = strtoull(argv[2], NULL, 0);
    uint64_t end_range = strtoull(argv[3], NULL, 0);
    
    printf("Hash160 correto: %s\n", target_hash160_hex);
    printf("Range: %llu - %llu\n", start_range, end_range);
    
    // Verificar CUDA
    int device_count;
    cudaGetDeviceCount(&device_count);
    if (device_count == 0) {
        printf("❌ Nenhuma GPU CUDA encontrada!\n");
        return 1;
    }
    
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, 0);
    printf("GPU: %s\n", prop.name);
    
    // Converter hex para bytes
    uint8_t target_hash160[HASH_SIZE];
    for (int i = 0; i < HASH_SIZE; i++) {
        sscanf(target_hash160_hex + i * 2, "%2hhx", &target_hash160[i]);
    }
    
    // Alocar memória GPU
    uint8_t* d_target_hash160;
    cudaMalloc(&d_target_hash160, HASH_SIZE);
    cudaMemcpy(d_target_hash160, target_hash160, HASH_SIZE, cudaMemcpyHostToDevice);
    
    // Reset resultado e contadores
    uint64_t zero = 0;
    int zero_int = 0;
    cudaMemcpyToSymbol(d_chave_encontrada, &zero, sizeof(uint64_t));
    cudaMemcpyToSymbol(d_encontrou, &zero_int, sizeof(int));
    cudaMemcpyToSymbol(d_chaves_testadas, &zero, sizeof(uint64_t));
    cudaMemcpyToSymbol(d_ultimo_progresso, &zero, sizeof(uint64_t));

    // Configuração do kernel
    int threads_per_block = 256;
    int blocks_per_grid = 1024;
    uint64_t total_threads = threads_per_block * blocks_per_grid;
    uint64_t total_chaves = end_range - start_range;

    printf("Configuração: %d threads/block, %d blocks\n", threads_per_block, blocks_per_grid);
    printf("Total threads simultâneas: %llu\n", total_threads);
    printf("Total de chaves para testar: %llu\n", total_chaves);
    printf("Estimativa de iterações: %llu\n", (total_chaves / total_threads) + 1);
    printf("\n🔍 PROCURANDO CHAVE PRIVADA QUE GERA O ENDEREÇO...\n");
    printf("============================================================\n");

    // Timing
    clock_t inicio = clock();
    time_t inicio_time = time(NULL);

    // Lançar kernel
    descobrir_hash_errado_kernel<<<blocks_per_grid, threads_per_block>>>(
        start_range, end_range, d_target_hash160
    );

    // Monitorar progresso
    uint64_t chaves_testadas_anterior = 0;
    uint64_t chaves_testadas_atual = 0;
    int encontrou = 0;
    int contador_progresso = 0;

    printf("⏱️  Início: %s", ctime(&inicio_time));
    printf("📊 Monitoramento de progresso:\n\n");

    while (!encontrou) {
        // Verificar se encontrou
        cudaMemcpyFromSymbol(&encontrou, d_encontrou, sizeof(int));
        if (encontrou) break;

        // Verificar progresso
        cudaMemcpyFromSymbol(&chaves_testadas_atual, d_chaves_testadas, sizeof(uint64_t));

        // Mostrar progresso a cada 5 segundos
        clock_t agora = clock();
        double tempo_decorrido = ((double)(agora - inicio)) / CLOCKS_PER_SEC;

        if (tempo_decorrido >= (contador_progresso + 1) * 5.0) {  // A cada 5 segundos
            contador_progresso++;

            uint64_t chaves_neste_periodo = chaves_testadas_atual - chaves_testadas_anterior;
            double velocidade_atual = chaves_neste_periodo / 5.0;  // chaves/segundo nos últimos 5s
            double velocidade_media = chaves_testadas_atual / tempo_decorrido;
            double progresso_pct = ((double)chaves_testadas_atual / total_chaves) * 100.0;

            printf("⏱️  %02d:%02d | ", (int)(tempo_decorrido/60), (int)tempo_decorrido%60);
            printf("Testadas: %llu | ", chaves_testadas_atual);
            printf("Progresso: %.3f%% | ", progresso_pct);
            printf("Velocidade: %.0f K/s | ", velocidade_atual / 1000.0);
            printf("Média: %.0f K/s\n", velocidade_media / 1000.0);

            // Estimativa de tempo restante
            if (velocidade_media > 0) {
                uint64_t chaves_restantes = total_chaves - chaves_testadas_atual;
                double tempo_restante = chaves_restantes / velocidade_media;
                printf("   ⏳ Tempo restante estimado: %02d:%02d:%02d\n",
                       (int)(tempo_restante/3600),
                       (int)((tempo_restante/60)) % 60,
                       (int)tempo_restante % 60);
            }

            chaves_testadas_anterior = chaves_testadas_atual;
        }

        // Verificar se o kernel ainda está rodando
        cudaError_t status = cudaDeviceSynchronize();
        if (status == cudaSuccess) {
            // Kernel terminou
            break;
        }

        // Pequena pausa
        sleep_ms(100);  // 100ms
    }

    printf("\n============================================================\n");
    
    clock_t fim = clock();
    time_t fim_time = time(NULL);
    double tempo_total = ((double)(fim - inicio)) / CLOCKS_PER_SEC;

    // Verificar resultado final
    uint64_t chave_encontrada;
    int encontrou_final;
    uint8_t hash_errado_resultado[HASH_SIZE];
    uint64_t chaves_testadas_final;

    cudaMemcpyFromSymbol(&chave_encontrada, d_chave_encontrada, sizeof(uint64_t));
    cudaMemcpyFromSymbol(&encontrou_final, d_encontrou, sizeof(int));
    cudaMemcpyFromSymbol(hash_errado_resultado, d_hash_errado_resultado, HASH_SIZE);
    cudaMemcpyFromSymbol(&chaves_testadas_final, d_chaves_testadas, sizeof(uint64_t));

    printf("\n📊 ESTATÍSTICAS FINAIS DA BUSCA\n");
    printf("==================================================\n");
    printf("⏱️  Início: %s", ctime(&inicio_time));
    printf("⏱️  Fim:    %s", ctime(&fim_time));
    printf("⏱️  Duração: %.2f segundos (%.1f minutos)\n", tempo_total, tempo_total/60.0);
    printf("🔢 Chaves testadas: %llu\n", chaves_testadas_final);
    printf("🔢 Total no range: %llu\n", total_chaves);
    printf("📈 Progresso: %.6f%%\n", ((double)chaves_testadas_final / total_chaves) * 100.0);

    if (tempo_total > 0) {
        double velocidade_media = chaves_testadas_final / tempo_total;
        printf("⚡ Velocidade média: %.0f chaves/segundo\n", velocidade_media);
        printf("⚡ Velocidade: %.2f M chaves/segundo\n", velocidade_media / 1000000.0);
        printf("⚡ Velocidade: %.2f K chaves/segundo\n", velocidade_media / 1000.0);

        // Eficiência da GPU
        double eficiencia = (velocidade_media / total_threads) * 100.0;
        printf("🎯 Eficiência por thread: %.2f chaves/seg/thread\n", velocidade_media / total_threads);
        printf("🎯 Utilização GPU: %.1f%%\n", eficiencia > 100 ? 100.0 : eficiencia);
    }

    printf("\n");
    
    if (encontrou_final) {
        printf("🎉 SUCESSO! CHAVE PRIVADA ENCONTRADA!\n");
        printf("==================================================\n");
        printf("🔑 Chave privada: %llu\n", chave_encontrada);
        printf("🔑 Chave (hex):   0x%llx\n", chave_encontrada);
        printf("🎯 Endereço:      %s\n", target_hash160_hex);  // Seria melhor calcular o endereço real

        printf("\n🎯 HASH ERRADO DESCOBERTO:\n");
        printf("Hash160 errado: ");
        for (int i = 0; i < HASH_SIZE; i++) {
            printf("%02x", hash_errado_resultado[i]);
        }
        printf("\n");

        printf("\n✅ COMANDOS PARA USAR:\n");
        printf("==============================\n");

        printf("1. Salvar hash errado:\n");
        printf("   HASH_ERRADO=");
        for (int i = 0; i < HASH_SIZE; i++) {
            printf("%02x", hash_errado_resultado[i]);
        }
        printf("\n\n");

        printf("2. Usar no programa de busca:\n");
        printf("   ./busca_cuda ");
        for (int i = 0; i < HASH_SIZE; i++) {
            printf("%02x", hash_errado_resultado[i]);
        }
        printf(" %llu %llu\n\n", start_range, end_range);

        printf("3. Verificar resultado:\n");
        printf("   python3 verificar_resultado_cuda.py %llu ", chave_encontrada);
        for (int i = 0; i < HASH_SIZE; i++) {
            printf("%02x", hash_errado_resultado[i]);
        }
        printf("\n\n");

        printf("🎊 PARABÉNS! Você descobriu o hash errado da carteira!\n");

    } else {
        printf("❌ RESULTADO: CHAVE NÃO ENCONTRADA\n");
        printf("========================================\n");
        printf("💡 POSSÍVEIS CAUSAS:\n");
        printf("   • Chave privada está fora do range testado\n");
        printf("   • Hash160 correto pode estar incorreto\n");
        printf("   • Range muito grande, precisa de mais tempo\n");
        printf("   • Erro no algoritmo de geração de endereço\n");
        printf("\n💡 SUGESTÕES:\n");
        printf("   • Testar range menor primeiro\n");
        printf("   • Verificar hash160 correto do endereço\n");
        printf("   • Usar versão Python para ranges pequenos\n");
        printf("   • Dividir range em chunks menores\n");

        if (chaves_testadas_final > 0 && tempo_total > 0) {
            uint64_t chaves_restantes = total_chaves - chaves_testadas_final;
            double velocidade_media = chaves_testadas_final / tempo_total;
            double tempo_restante = chaves_restantes / velocidade_media;

            printf("\n⏳ ESTIMATIVA PARA COMPLETAR O RANGE:\n");
            printf("   Chaves restantes: %llu\n", chaves_restantes);
            printf("   Tempo estimado: %.1f horas (%.1f dias)\n",
                   tempo_restante/3600.0, tempo_restante/86400.0);
        }
    }
    
    // Limpeza
    cudaFree(d_target_hash160);
    
    return encontrou ? 0 : 1;
}
