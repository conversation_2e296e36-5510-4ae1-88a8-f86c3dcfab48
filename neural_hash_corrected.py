#!/usr/bin/env python3
"""
REDE NEURAL CORRIGIDA - TREINAMENTO CORRETO
INPUT: Hash correto (da chave privada)
OUTPUT: Hash errado/mágico (resultado do test_magic_number)
"""

import numpy as np
import time
import json
import random
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed

# Tentar importar CuPy
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("✅ CuPy disponível - GPU habilitada")
except ImportError:
    import numpy as cp
    GPU_AVAILABLE = False
    print("⚠️  CuPy não encontrado - usando CPU")

# Importar funções necessárias
try:
    from test_magic_number import simular_gpu_errada_para_chave
    from main import private_key_to_address
    from bitcoin_conversions import calculate_target_hash160
    print("✅ Módulos Bitcoin importados")
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    exit(1)

def hex_to_onehot_corrected(hex_string):
    """Converte hex para one-hot (40 chars -> 640 dims)"""
    hex_string = hex_string.ljust(40, '0')[:40]
    vector = np.zeros(640, dtype=np.float32)  # 40 * 16
    
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            vector[i * 16 + int(char, 16)] = 1.0
    
    return vector

def onehot_to_hex_corrected(vector):
    """Converte one-hot para hex"""
    hex_chars = '0123456789abcdef'
    vector_reshaped = vector.reshape(40, 16)
    return ''.join(hex_chars[np.argmax(row)] for row in vector_reshaped)

def generate_corrected_sample(private_key):
    """
    Gera amostra CORRETA:
    INPUT: Hash correto (da chave privada)
    OUTPUT: Hash errado/mágico (do test_magic_number)
    """
    try:
        # 1. Obter endereço da chave privada
        endereco = private_key_to_address(private_key)
        if not endereco:
            return None, None, None
        
        # 2. Calcular hash160 correto (INPUT)
        hash160_correto = calculate_target_hash160(endereco)
        if not hash160_correto:
            return None, None, None
        
        # 3. Calcular hash errado/mágico (OUTPUT)
        numero_magico = simular_gpu_errada_para_chave(private_key)
        if not numero_magico:
            return None, None, None
        
        # 4. Converter para vetores
        input_vector = hex_to_onehot_corrected(hash160_correto.hex())   # Hash correto -> INPUT
        output_vector = hex_to_onehot_corrected(numero_magico.hex())    # Hash errado -> OUTPUT
        
        return input_vector, output_vector, private_key
        
    except Exception as e:
        return None, None, None

def generate_batch_corrected(key_batch):
    """Gera batch de amostras com treinamento correto"""
    inputs = []
    outputs = []
    successful_keys = []
    
    for private_key in key_batch:
        input_vec, output_vec, key = generate_corrected_sample(private_key)
        
        if input_vec is not None and output_vec is not None:
            inputs.append(input_vec)
            outputs.append(output_vec)
            successful_keys.append(key)
    
    return inputs, outputs, successful_keys

def verify_training_data(inputs, outputs, keys, num_verify=5):
    """Verifica se os dados de treinamento estão corretos"""
    print(f"\n🔍 VERIFICANDO DADOS DE TREINAMENTO:")
    print("=" * 50)
    
    for i in range(min(num_verify, len(inputs))):
        key = keys[i]
        input_hash = onehot_to_hex_corrected(inputs[i])
        output_hash = onehot_to_hex_corrected(outputs[i])
        
        print(f"Chave privada {key:x}:")
        print(f"  INPUT (hash correto):  {input_hash}")
        print(f"  OUTPUT (hash errado):  {output_hash}")
        
        # Verificar se está correto
        try:
            endereco = private_key_to_address(key)
            hash160_correto = calculate_target_hash160(endereco)
            numero_magico = simular_gpu_errada_para_chave(key)
            
            expected_input = hash160_correto.hex() if hash160_correto else "ERRO"
            expected_output = numero_magico.hex() if numero_magico else "ERRO"
            
            input_match = input_hash == expected_input
            output_match = output_hash == expected_output
            
            print(f"  Esperado INPUT:        {expected_input}")
            print(f"  Esperado OUTPUT:       {expected_output}")
            print(f"  INPUT correto:         {'✅' if input_match else '❌'}")
            print(f"  OUTPUT correto:        {'✅' if output_match else '❌'}")
            
        except Exception as e:
            print(f"  Erro na verificação: {e}")
        
        print()
    
    print("✅ Verificação concluída!")

class CorrectedNeuralNetwork:
    """Rede neural com treinamento correto"""
    
    def __init__(self, use_gpu=True):
        self.use_gpu = use_gpu and GPU_AVAILABLE
        self.xp = cp if self.use_gpu else np
        
        # Arquitetura otimizada: 640 -> 1024 -> 1536 -> 1024 -> 640
        self.layers = [640, 1024, 1536, 1024, 640]
        
        print(f"🧠 Rede Neural Corrigida")
        print(f"🔧 Dispositivo: {'GPU' if self.use_gpu else 'CPU'}")
        print(f"📊 Arquitetura: {' → '.join(map(str, self.layers))}")
        print(f"📊 Treinamento: Hash correto → Hash errado")
        
        # Inicialização Xavier
        self.weights = []
        self.biases = []
        
        for i in range(len(self.layers) - 1):
            fan_in = self.layers[i]
            fan_out = self.layers[i + 1]
            
            limit = self.xp.sqrt(6.0 / (fan_in + fan_out))
            w = self.xp.random.uniform(-limit, limit, (fan_in, fan_out)).astype(self.xp.float32)
            b = self.xp.zeros((1, fan_out), dtype=self.xp.float32)
            
            self.weights.append(w)
            self.biases.append(b)
        
        total_params = sum(w.size for w in self.weights) + sum(b.size for b in self.biases)
        print(f"📊 Parâmetros: {total_params:,}")
    
    def leaky_relu(self, x, alpha=0.01):
        """Leaky ReLU"""
        return self.xp.where(x > 0, x, alpha * x)
    
    def leaky_relu_derivative(self, x, alpha=0.01):
        """Derivada Leaky ReLU"""
        return self.xp.where(x > 0, 1, alpha)
    
    def softmax_grouped(self, x):
        """Softmax agrupado para cada caractere (40 grupos de 16)"""
        batch_size = x.shape[0]
        x_reshaped = x.reshape(batch_size, 40, 16)
        
        # Softmax estável
        exp_x = self.xp.exp(x_reshaped - self.xp.max(x_reshaped, axis=2, keepdims=True))
        softmax_x = exp_x / self.xp.sum(exp_x, axis=2, keepdims=True)
        
        return softmax_x.reshape(batch_size, 640)
    
    def forward(self, X):
        """Forward pass"""
        if self.use_gpu and not isinstance(X, cp.ndarray):
            X = cp.asarray(X)
        elif not self.use_gpu and isinstance(X, cp.ndarray):
            X = cp.asnumpy(X)
        
        activations = [X]
        z_values = []
        
        for i, (w, b) in enumerate(zip(self.weights, self.biases)):
            z = self.xp.dot(activations[-1], w) + b
            z_values.append(z)
            
            if i < len(self.weights) - 1:
                # Camadas ocultas: Leaky ReLU
                a = self.leaky_relu(z)
            else:
                # Camada de saída: Softmax agrupado
                a = self.softmax_grouped(z)
            
            activations.append(a)
        
        return activations, z_values
    
    def cross_entropy_loss(self, y_pred, y_true):
        """Cross-entropy loss para classificação"""
        y_pred_clipped = self.xp.clip(y_pred, 1e-15, 1 - 1e-15)
        loss = -self.xp.sum(y_true * self.xp.log(y_pred_clipped))
        return loss / y_pred.shape[0]
    
    def backward(self, X, y, activations, z_values, learning_rate, l2_reg=0.0001):
        """Backward pass"""
        m = X.shape[0]
        
        # Erro da saída (cross-entropy + softmax)
        output_error = activations[-1] - y
        
        # Backpropagation
        errors = [output_error]
        
        for i in range(len(self.weights) - 1, 0, -1):
            error = self.xp.dot(errors[-1], self.weights[i].T)
            error *= self.leaky_relu_derivative(z_values[i-1])
            errors.append(error)
        
        errors.reverse()
        
        # Atualizar pesos
        for i in range(len(self.weights)):
            dw = self.xp.dot(activations[i].T, errors[i]) / m
            db = self.xp.mean(errors[i], axis=0, keepdims=True)
            
            # Regularização L2
            dw += l2_reg * self.weights[i]
            
            # Gradient clipping
            dw = self.xp.clip(dw, -1.0, 1.0)
            db = self.xp.clip(db, -1.0, 1.0)
            
            self.weights[i] -= learning_rate * dw
            self.biases[i] -= learning_rate * db
        
        # Loss
        loss = self.cross_entropy_loss(activations[-1], y)
        l2_loss = sum(self.xp.sum(w ** 2) for w in self.weights) * l2_reg
        
        return float(loss + l2_loss)
    
    def train_corrected(self, X, y, epochs=1000, initial_lr=0.001, batch_size=64):
        """Treinamento correto"""
        print(f"\n🚀 TREINAMENTO CORRETO")
        print("=" * 25)
        
        if self.use_gpu:
            if not isinstance(X, cp.ndarray):
                X = cp.asarray(X)
            if not isinstance(y, cp.ndarray):
                y = cp.asarray(y)
        
        num_batches = len(X) // batch_size
        best_loss = float('inf')
        patience = 150
        patience_counter = 0
        
        print(f"Configuração:")
        print(f"  Amostras: {len(X):,}")
        print(f"  Batch size: {batch_size}")
        print(f"  Learning rate: {initial_lr}")
        print(f"  Direção: Hash correto → Hash errado")
        
        for epoch in range(epochs):
            # Learning rate schedule
            if epoch < 200:
                lr = initial_lr
            elif epoch < 500:
                lr = initial_lr * 0.5
            elif epoch < 800:
                lr = initial_lr * 0.2
            else:
                lr = initial_lr * 0.1
            
            # Embaralhar dados
            if self.use_gpu:
                indices = cp.random.permutation(len(X))
            else:
                indices = np.random.permutation(len(X))
            
            X_shuffled = X[indices]
            y_shuffled = y[indices]
            
            epoch_loss = 0.0
            
            # Treinar por batches
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = start_idx + batch_size
                
                X_batch = X_shuffled[start_idx:end_idx]
                y_batch = y_shuffled[start_idx:end_idx]
                
                # Forward pass
                activations, z_values = self.forward(X_batch)
                
                # Backward pass
                batch_loss = self.backward(X_batch, y_batch, activations, z_values, lr)
                epoch_loss += batch_loss
            
            epoch_loss /= num_batches
            
            # Early stopping
            if epoch_loss < best_loss:
                best_loss = epoch_loss
                patience_counter = 0
            else:
                patience_counter += 1
            
            if patience_counter >= patience:
                print(f"\n⏰ Early stopping na época {epoch+1}")
                break
            
            # Log progresso
            if (epoch + 1) % 50 == 0:
                print(f"Época {epoch+1:4d}/{epochs} | "
                      f"Loss: {epoch_loss:.6f} | "
                      f"LR: {lr:.6f} | "
                      f"Best: {best_loss:.6f}")
        
        print(f"\n✅ Treinamento correto concluído!")
        print(f"📈 Melhor loss: {best_loss:.6f}")
        
        return best_loss
    
    def predict(self, X):
        """Predição"""
        activations, _ = self.forward(X)
        result = activations[-1]
        
        if self.use_gpu and isinstance(result, cp.ndarray):
            result = cp.asnumpy(result)
        
        return result

class CorrectedHashPredictor:
    """Preditor com treinamento correto"""
    
    def __init__(self):
        self.model = None
        self.use_gpu = GPU_AVAILABLE
        
        print(f"🚀 Corrected Hash Predictor")
        print(f"🔧 GPU: {'Habilitada' if self.use_gpu else 'Desabilitada'}")
        print(f"📊 Treinamento: INPUT=Hash correto, OUTPUT=Hash errado")
    
    def generate_dataset_corrected(self, num_samples=10000, start_key=1, end_key=100000):
        """Gera dataset com treinamento correto"""
        print(f"🚀 GERANDO DATASET CORRETO: {num_samples:,} AMOSTRAS")
        print("=" * 55)
        
        # Usar chaves menores para padrões mais claros
        all_keys = random.sample(range(start_key, min(end_key, 50000)), 
                                min(num_samples, 49999))
        
        # Processamento paralelo
        num_processes = min(mp.cpu_count(), 8)
        batch_size = len(all_keys) // num_processes
        
        key_batches = [all_keys[i:i + batch_size] 
                      for i in range(0, len(all_keys), batch_size)]
        
        print(f"🔄 Processamento: {num_processes} processos")
        print(f"📊 Chaves: {start_key} a {min(end_key, 50000)}")
        
        all_inputs = []
        all_outputs = []
        all_keys = []
        
        start_time = time.time()
        
        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            futures = [executor.submit(generate_batch_corrected, batch) 
                      for batch in key_batches]
            
            for i, future in enumerate(as_completed(futures)):
                try:
                    inputs, outputs, keys = future.result()
                    all_inputs.extend(inputs)
                    all_outputs.extend(outputs)
                    all_keys.extend(keys)
                    
                    print(f"   Batch {i+1}/{len(key_batches)}: {len(inputs)} amostras")
                    
                except Exception as e:
                    print(f"   ❌ Erro batch {i+1}: {e}")
        
        generation_time = time.time() - start_time
        
        print(f"\n📊 DATASET CORRETO:")
        print(f"✅ Amostras: {len(all_inputs):,}")
        print(f"⏱️  Tempo: {generation_time:.1f}s")
        print(f"⚡ Velocidade: {len(all_inputs) / generation_time:.0f} amostras/s")
        
        if len(all_inputs) == 0:
            return None, None, None
        
        # Verificar dados
        verify_training_data(all_inputs, all_outputs, all_keys)
        
        return np.array(all_inputs, dtype=np.float32), np.array(all_outputs, dtype=np.float32), all_keys

    def evaluate_corrected(self, X_test, y_test, test_keys):
        """Avaliação com treinamento correto"""
        print(f"\n📊 AVALIAÇÃO CORRETO")
        print("=" * 25)

        predictions = self.model.predict(X_test)

        print(f"🔍 AMOSTRAS DE TESTE (primeiras 10):")
        print("-" * 90)

        accuracies = []
        perfect_matches = 0
        high_accuracy = 0  # >80%

        for i in range(min(10, len(predictions))):
            key = test_keys[i]
            input_hash = onehot_to_hex_corrected(X_test[i])      # Hash correto (INPUT)
            expected_output = onehot_to_hex_corrected(y_test[i])  # Hash errado esperado
            predicted_output = onehot_to_hex_corrected(predictions[i])  # Hash errado predito

            # Acurácia por caractere
            correct_chars = sum(1 for a, b in zip(expected_output, predicted_output) if a == b)
            accuracy = (correct_chars / 40) * 100
            accuracies.append(accuracy)

            if accuracy == 100:
                perfect_matches += 1
            elif accuracy > 80:
                high_accuracy += 1

            print(f"Chave privada {key:x}:")
            print(f"  INPUT (hash correto):     {input_hash}")
            print(f"  OUTPUT esperado (errado): {expected_output}")
            print(f"  OUTPUT predito (errado):  {predicted_output}")
            print(f"  Acurácia: {accuracy:5.1f}% ({correct_chars:2d}/40)")

            # Verificar se está realmente correto
            try:
                endereco = private_key_to_address(key)
                hash160_correto = calculate_target_hash160(endereco)
                numero_magico = simular_gpu_errada_para_chave(key)

                real_input = hash160_correto.hex() if hash160_correto else "ERRO"
                real_output = numero_magico.hex() if numero_magico else "ERRO"

                input_correct = input_hash == real_input
                output_correct = expected_output == real_output

                print(f"  Verificação INPUT:  {'✅' if input_correct else '❌'}")
                print(f"  Verificação OUTPUT: {'✅' if output_correct else '❌'}")

            except Exception as e:
                print(f"  Erro verificação: {e}")

            print()

        # Estatísticas completas
        all_accuracies = []
        all_perfect = 0
        all_high = 0

        for i in range(len(predictions)):
            expected_output = onehot_to_hex_corrected(y_test[i])
            predicted_output = onehot_to_hex_corrected(predictions[i])
            correct_chars = sum(1 for a, b in zip(expected_output, predicted_output) if a == b)
            accuracy = (correct_chars / 40) * 100
            all_accuracies.append(accuracy)

            if accuracy == 100:
                all_perfect += 1
            elif accuracy > 80:
                all_high += 1

        avg_accuracy = np.mean(all_accuracies)
        median_accuracy = np.median(all_accuracies)
        max_accuracy = np.max(all_accuracies)

        print(f"📈 ESTATÍSTICAS CORRETAS:")
        print(f"   Acurácia média:      {avg_accuracy:6.1f}%")
        print(f"   Acurácia mediana:    {median_accuracy:6.1f}%")
        print(f"   Acurácia máxima:     {max_accuracy:6.1f}%")
        print(f"   Predições perfeitas: {all_perfect:4d} ({all_perfect/len(predictions)*100:5.1f}%)")
        print(f"   Predições > 80%:     {all_high:4d} ({all_high/len(predictions)*100:5.1f}%)")

        return avg_accuracy

    def run_corrected_training(self, num_samples=10000):
        """Pipeline correto completo"""
        print("🚀 CORRECTED HASH PREDICTOR")
        print("=" * 35)

        total_start = time.time()

        # 1. Gerar dataset correto
        X, y, keys = self.generate_dataset_corrected(num_samples=num_samples)

        if X is None:
            print("❌ Falha na geração do dataset")
            return 0

        # 2. Dividir dados
        test_size = min(1000, len(X) // 8)

        indices = np.random.permutation(len(X))
        X_test = X[indices[:test_size]]
        y_test = y[indices[:test_size]]
        test_keys = [keys[i] for i in indices[:test_size]]

        X_train = X[indices[test_size:]]
        y_train = y[indices[test_size:]]
        train_keys = [keys[i] for i in indices[test_size:]]

        print(f"\n📊 DIVISÃO CORRETA:")
        print(f"Treino: {len(X_train):,} amostras")
        print(f"Teste: {len(X_test):,} amostras")

        # 3. Criar e treinar modelo
        self.model = CorrectedNeuralNetwork(use_gpu=self.use_gpu)
        self.model.train_corrected(X_train, y_train, epochs=800, batch_size=32)

        # 4. Avaliar
        accuracy = self.evaluate_corrected(X_test, y_test, test_keys)

        # 5. Salvar
        self.save_corrected_model()

        total_time = time.time() - total_start

        print(f"\n🎊 TREINAMENTO CORRETO CONCLUÍDO!")
        print(f"⏱️  Tempo total: {total_time:.1f} segundos")
        print(f"📈 Acurácia final: {accuracy:.1f}%")

        if accuracy > 50:
            print("🎉 EXCELENTE! Treinamento correto funcionou!")
        elif accuracy > 30:
            print("🎯 MUITO BOM! Progresso significativo!")
        elif accuracy > 15:
            print("📈 BOM! Melhor que versões anteriores!")
        else:
            print("⚠️  Ainda baixo, mas direção correta")

        return accuracy

    def save_corrected_model(self):
        """Salva modelo correto"""
        weights_cpu = []
        biases_cpu = []

        for w, b in zip(self.model.weights, self.model.biases):
            if self.use_gpu:
                weights_cpu.append(cp.asnumpy(w).tolist())
                biases_cpu.append(cp.asnumpy(b).tolist())
            else:
                weights_cpu.append(w.tolist())
                biases_cpu.append(b.tolist())

        model_data = {
            'layers': self.model.layers,
            'weights': weights_cpu,
            'biases': biases_cpu,
            'training_direction': 'hash_correto_to_hash_errado',
            'input': 'hash160_correto',
            'output': 'numero_magico',
            'encoding': 'one_hot_640'
        }

        with open('corrected_neural_hash_model.json', 'w') as f:
            json.dump(model_data, f)

        print(f"💾 Modelo correto salvo em 'corrected_neural_hash_model.json'")

    def predict_magic_number_corrected(self, hash160_correto_hex):
        """
        Predição correta:
        INPUT: Hash correto
        OUTPUT: Hash errado/mágico
        """
        if self.model is None:
            print("❌ Modelo não treinado!")
            return None

        input_vector = hex_to_onehot_corrected(hash160_correto_hex).reshape(1, -1)
        prediction = self.model.predict(input_vector)

        return onehot_to_hex_corrected(prediction[0])

def test_with_known_examples():
    """Testa com os exemplos fornecidos pelo usuário"""
    print(f"\n🧪 TESTANDO COM EXEMPLOS CONHECIDOS")
    print("=" * 40)

    # Exemplos fornecidos pelo usuário
    examples = [
        (1, "751e76e8199196d454941c45d1b3a323f1433bd6", "36df2f22295784ab7f81989f9247bfd99bb00c03"),
        (2, "06afd46bcdfd22ef94ac122aa11f241244a37ecc", "5fed51813a4b0353320dbee6fc24a63c5f695181"),
        (3, "7dd65592d0ab2fe0d0257d571abf032cd9db93dc", "b0548c85212204a8a9555adbbdb6dab85b77afa4"),
        (7, "5dedfbf9ea599dd4e3ca6a80b333c472fd0b3f69", "db18cb5bad4e6901c80bbf24bd5579dbf23b6672"),
        (8, "9652d86bedf43ad264362e6e6eba6eb764508127", "db18cb5bad4e6901c80bbf24bd5579dbf23b6672"),
        (9, "b46abf4d9e1746e33bcc39cea3de876c29c4adf3", "65e2679a06e208f8541598a6b519b7e9c2cb44f7"),
        (0xa, "185140bb54704a9e735016faa7a8dbee4449bddc", "f4328c476b138fb6d5c53a682ad5603278b6ca85")
    ]

    print("Verificando exemplos fornecidos:")
    print("Formato: Chave -> INPUT (hash correto) -> OUTPUT (hash errado)")
    print()

    for key, expected_input, expected_output in examples:
        try:
            # Gerar amostra
            input_vec, output_vec, _ = generate_corrected_sample(key)

            if input_vec is not None and output_vec is not None:
                actual_input = onehot_to_hex_corrected(input_vec)
                actual_output = onehot_to_hex_corrected(output_vec)

                input_match = actual_input == expected_input
                output_match = actual_output == expected_output

                print(f"Chave {key:x}:")
                print(f"  INPUT esperado:  {expected_input}")
                print(f"  INPUT gerado:    {actual_input} {'✅' if input_match else '❌'}")
                print(f"  OUTPUT esperado: {expected_output}")
                print(f"  OUTPUT gerado:   {actual_output} {'✅' if output_match else '❌'}")
                print()
            else:
                print(f"Chave {key:x}: ❌ Erro na geração")
                print()

        except Exception as e:
            print(f"Chave {key:x}: ❌ Erro: {e}")
            print()

def main():
    """Função principal corrigida"""
    print("🚀 CORRECTED NEURAL HASH PREDICTOR")
    print("=" * 45)

    # Verificar status
    if GPU_AVAILABLE:
        print("✅ CUDA disponível")
    else:
        print("⚠️  Usando CPU")

    print(f"\n🔧 CORREÇÃO IMPLEMENTADA:")
    print("• INPUT: Hash correto (da chave privada)")
    print("• OUTPUT: Hash errado/mágico (do test_magic_number)")
    print("• Treinamento: Aprender relação hash correto → hash errado")
    print("• Verificação: Dados testados com exemplos conhecidos")

    # Testar com exemplos conhecidos primeiro
    test_with_known_examples()

    # Configuração
    num_samples = int(input("\nNúmero de amostras (recomendado: 10000): ") or "10000")

    # Executar
    predictor = CorrectedHashPredictor()
    accuracy = predictor.run_corrected_training(num_samples=num_samples)

    if accuracy > 0:
        print(f"\n🎯 RESULTADO CORRETO: {accuracy:.1f}% de acurácia")

        # Teste interativo
        print(f"\n🧪 TESTE CORRETO:")
        print("Digite um hash160 correto e obtenha o hash errado/mágico")

        while True:
            hash_input = input("\nHash160 correto (40 chars) ou 'quit': ").strip()

            if hash_input.lower() == 'quit':
                break

            if len(hash_input) != 40:
                print("❌ Hash deve ter 40 caracteres")
                continue

            if not all(c in '0123456789abcdef' for c in hash_input.lower()):
                print("❌ Hash deve conter apenas caracteres hexadecimais")
                continue

            magic_number = predictor.predict_magic_number_corrected(hash_input.lower())
            print(f"Hash errado/mágico predito: {magic_number}")
            print(f"🚀 Comando: ./buscar_chave_por_hash_errado {magic_number} 1:1000000")

if __name__ == "__main__":
    main()
