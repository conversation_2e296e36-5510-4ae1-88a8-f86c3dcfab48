#!/usr/bin/env python3
"""
REDE NEURAL ULTRA-RÁPIDA
Versão minimalista que inicia em segundos
"""

import numpy as np
import time
import random

# Tentar importar CuPy
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("✅ GPU disponível")
except ImportError:
    import numpy as cp
    GPU_AVAILABLE = False
    print("⚠️  CPU apenas")

# Importar funções necessárias
try:
    from test_magic_number import simular_gpu_errada_para_chave
    from main import private_key_to_address
    from bitcoin_conversions import calculate_target_hash160
    print("✅ Módulos importados")
except ImportError as e:
    print(f"❌ Erro: {e}")
    exit(1)

def hex_to_onehot(hex_string):
    """Converte hex para one-hot rapidamente"""
    hex_string = hex_string.ljust(40, '0')[:40]
    vector = np.zeros(640, dtype=np.float32)  # 40 * 16
    
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            vector[i * 16 + int(char, 16)] = 1.0
    
    return vector

def onehot_to_hex(vector):
    """Converte one-hot para hex"""
    hex_chars = '0123456789abcdef'
    vector_reshaped = vector.reshape(40, 16)
    return ''.join(hex_chars[np.argmax(row)] for row in vector_reshaped)

class QuickNeuralNet:
    """Rede neural ultra-simples"""
    
    def __init__(self, use_gpu=True):
        self.use_gpu = use_gpu and GPU_AVAILABLE
        self.xp = cp if self.use_gpu else np
        
        # Arquitetura mínima: 640 → 512 → 640
        self.w1 = self.xp.random.normal(0, 0.1, (640, 512)).astype(self.xp.float32)
        self.b1 = self.xp.zeros((1, 512), dtype=self.xp.float32)
        self.w2 = self.xp.random.normal(0, 0.1, (512, 640)).astype(self.xp.float32)
        self.b2 = self.xp.zeros((1, 640), dtype=self.xp.float32)
        
        print(f"🧠 Rede Ultra-Rápida: 640 → 512 → 640")
        print(f"📊 Parâmetros: {(640*512 + 512 + 512*640 + 640):,}")
    
    def forward(self, X):
        """Forward pass mínimo"""
        if self.use_gpu and not isinstance(X, cp.ndarray):
            X = cp.asarray(X)
        
        # Camada 1: ReLU
        z1 = self.xp.dot(X, self.w1) + self.b1
        a1 = self.xp.maximum(0, z1)
        
        # Camada 2: Softmax agrupado
        z2 = self.xp.dot(a1, self.w2) + self.b2
        
        # Softmax por grupo de 16
        batch_size = z2.shape[0]
        z2_grouped = z2.reshape(batch_size, 40, 16)
        exp_z = self.xp.exp(z2_grouped - self.xp.max(z2_grouped, axis=2, keepdims=True))
        a2 = exp_z / self.xp.sum(exp_z, axis=2, keepdims=True)
        
        return a1, a2.reshape(batch_size, 640)
    
    def train_quick(self, X, y, epochs=200, lr=0.01, batch_size=64):
        """Treinamento ultra-rápido"""
        print(f"\n🚀 TREINAMENTO ULTRA-RÁPIDO")
        print(f"Épocas: {epochs} | Batch: {batch_size} | LR: {lr}")
        
        if self.use_gpu:
            X = cp.asarray(X)
            y = cp.asarray(y)
        
        n_batches = len(X) // batch_size
        
        for epoch in range(epochs):
            # Embaralhar
            indices = self.xp.random.permutation(len(X))
            X_shuffled = X[indices]
            y_shuffled = y[indices]
            
            epoch_loss = 0
            
            for i in range(n_batches):
                start = i * batch_size
                end = start + batch_size
                
                X_batch = X_shuffled[start:end]
                y_batch = y_shuffled[start:end]
                
                # Forward
                a1, a2 = self.forward(X_batch)
                
                # Loss (cross-entropy)
                loss = -self.xp.mean(y_batch * self.xp.log(self.xp.clip(a2, 1e-15, 1)))
                epoch_loss += float(loss)
                
                # Backward (simplificado)
                m = X_batch.shape[0]
                
                # Gradientes camada 2
                dz2 = (a2 - y_batch) / m
                dw2 = self.xp.dot(a1.T, dz2)
                db2 = self.xp.mean(dz2, axis=0, keepdims=True)
                
                # Gradientes camada 1
                da1 = self.xp.dot(dz2, self.w2.T)
                dz1 = da1 * (a1 > 0)  # Derivada ReLU
                dw1 = self.xp.dot(X_batch.T, dz1)
                db1 = self.xp.mean(dz1, axis=0, keepdims=True)
                
                # Atualizar pesos
                self.w2 -= lr * dw2
                self.b2 -= lr * db2
                self.w1 -= lr * dw1
                self.b1 -= lr * db1
            
            # Decay LR
            if epoch % 50 == 0:
                lr *= 0.9
            
            # Log
            if (epoch + 1) % 25 == 0:
                avg_loss = epoch_loss / n_batches
                print(f"Época {epoch+1:3d} | Loss: {avg_loss:.4f} | LR: {lr:.4f}")
        
        print("✅ Treinamento concluído!")
    
    def predict(self, X):
        """Predição rápida"""
        _, output = self.forward(X)
        
        if self.use_gpu and isinstance(output, cp.ndarray):
            output = cp.asnumpy(output)
        
        return output

def generate_quick_data(n_samples=5000):
    """Gera dados rapidamente"""
    print(f"🔄 Gerando {n_samples} amostras...")
    
    inputs = []
    outputs = []
    
    # Usar chaves pequenas para velocidade
    keys = random.sample(range(1, 50000), n_samples)
    
    start_time = time.time()
    
    for i, key in enumerate(keys):
        try:
            endereco = private_key_to_address(key)
            if not endereco:
                continue
            
            hash160_correto = calculate_target_hash160(endereco)
            if not hash160_correto:
                continue
            
            numero_magico = simular_gpu_errada_para_chave(key)
            if not numero_magico:
                continue
            
            input_vec = hex_to_onehot(hash160_correto.hex())
            output_vec = hex_to_onehot(numero_magico.hex())
            
            inputs.append(input_vec)
            outputs.append(output_vec)
            
            if (i + 1) % 1000 == 0:
                print(f"   {i+1}/{n_samples} processadas...")
            
        except:
            continue
    
    gen_time = time.time() - start_time
    
    print(f"✅ {len(inputs)} amostras geradas em {gen_time:.1f}s")
    
    return np.array(inputs, dtype=np.float32), np.array(outputs, dtype=np.float32)

def evaluate_quick(model, X_test, y_test):
    """Avaliação rápida"""
    print(f"\n📊 AVALIAÇÃO")
    
    predictions = model.predict(X_test)
    
    accuracies = []
    
    for i in range(min(5, len(predictions))):
        hash_real = onehot_to_hex(y_test[i])
        hash_pred = onehot_to_hex(predictions[i])
        
        correct = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
        accuracy = (correct / 40) * 100
        accuracies.append(accuracy)
        
        print(f"Amostra {i+1}:")
        print(f"  Real:    {hash_real}")
        print(f"  Predito: {hash_pred}")
        print(f"  Acerto:  {accuracy:.1f}% ({correct}/40)")
    
    # Acurácia geral (amostra)
    sample_size = min(500, len(predictions))
    sample_accs = []
    
    for i in range(sample_size):
        hash_real = onehot_to_hex(y_test[i])
        hash_pred = onehot_to_hex(predictions[i])
        correct = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
        sample_accs.append((correct / 40) * 100)
    
    avg_acc = np.mean(sample_accs)
    max_acc = np.max(sample_accs)
    perfect = sum(1 for a in sample_accs if a == 100)
    
    print(f"\n📈 RESULTADO (amostra de {sample_size}):")
    print(f"   Acurácia média: {avg_acc:.1f}%")
    print(f"   Acurácia máxima: {max_acc:.1f}%")
    print(f"   Predições perfeitas: {perfect}")
    
    return avg_acc

def main():
    """Função principal ultra-rápida"""
    print("🚀 NEURAL HASH ULTRA-RÁPIDO")
    print("=" * 35)
    
    # Configuração mínima
    n_samples = int(input("Amostras (recomendado: 5000): ") or "5000")
    
    if n_samples > 10000:
        print("⚠️  Muitas amostras podem demorar")
        n_samples = 5000
    
    total_start = time.time()
    
    # 1. Gerar dados
    X, y = generate_quick_data(n_samples)
    
    if len(X) == 0:
        print("❌ Nenhuma amostra gerada")
        return
    
    # 2. Dividir dados
    test_size = min(500, len(X) // 5)
    
    indices = np.random.permutation(len(X))
    X_test = X[indices[:test_size]]
    y_test = y[indices[:test_size]]
    X_train = X[indices[test_size:]]
    y_train = y[indices[test_size:]]
    
    print(f"\nTreino: {len(X_train)} | Teste: {len(X_test)}")
    
    # 3. Treinar
    model = QuickNeuralNet(use_gpu=GPU_AVAILABLE)
    model.train_quick(X_train, y_train, epochs=150, batch_size=32)
    
    # 4. Avaliar
    accuracy = evaluate_quick(model, X_test, y_test)
    
    total_time = time.time() - total_start
    
    print(f"\n🎊 CONCLUÍDO EM {total_time:.1f} SEGUNDOS!")
    print(f"📈 Acurácia: {accuracy:.1f}%")
    
    # Teste interativo
    if accuracy > 20:
        print(f"\n🧪 TESTE:")
        while True:
            hash_input = input("\nHash (40 chars) ou 'quit': ").strip()
            
            if hash_input.lower() == 'quit':
                break
            
            if len(hash_input) != 40:
                print("❌ 40 caracteres necessários")
                continue
            
            input_vec = hex_to_onehot(hash_input.lower()).reshape(1, -1)
            prediction = model.predict(input_vec)
            magic_number = onehot_to_hex(prediction[0])
            
            print(f"Número mágico: {magic_number}")
            print(f"🚀 ./buscar_chave_por_hash_errado {magic_number} 1:1000000")

if __name__ == "__main__":
    main()
