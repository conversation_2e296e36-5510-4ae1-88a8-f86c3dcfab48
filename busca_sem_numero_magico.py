#!/usr/bin/env python3
"""
BUSCA SEM NÚMERO MÁGICO - SOLUÇÃO PRÁTICA
Implementa a melhor estratégia para encontrar chave privada sem número mágico
"""

from main import private_key_to_address, simular_gpu_errada_para_chave
from bitcoin_conversions import calculate_target_hash160, private_key_to_hash160
import time
import sys

def gerar_numeros_magicos_aproximados(endereco_alvo):
    """
    Gera números mágicos aproximados usando diferentes métodos matemáticos
    """
    print("🧮 GERANDO NÚMEROS MÁGICOS APROXIMADOS")
    print("=" * 50)
    
    # Calcular hash160 correto
    hash160_correto = calculate_target_hash160(endereco_alvo)
    print(f"Endereço: {endereco_alvo}")
    print(f"Hash160 correto: {hash160_correto.hex()}")
    
    numeros_magicos = []
    
    # Método 1: Primeiros 8 bytes como chave
    chave_1 = int.from_bytes(hash160_correto[:8], 'big')
    numero_magico_1 = simular_gpu_errada_para_chave(chave_1)
    numeros_magicos.append(numero_magico_1.hex())
    print(f"Método 1 (8 bytes): {numero_magico_1.hex()}")
    
    # Método 2: XOR de todos os bytes
    chave_2 = 0
    for byte in hash160_correto:
        chave_2 ^= byte
    chave_2 = chave_2 * 0x123456789ABCDEF
    numero_magico_2 = simular_gpu_errada_para_chave(chave_2)
    numeros_magicos.append(numero_magico_2.hex())
    print(f"Método 2 (XOR): {numero_magico_2.hex()}")
    
    # Método 3: Soma com multiplicador
    chave_3 = sum(hash160_correto) * 0x9E3779B9
    numero_magico_3 = simular_gpu_errada_para_chave(chave_3)
    numeros_magicos.append(numero_magico_3.hex())
    print(f"Método 3 (soma): {numero_magico_3.hex()}")
    
    # Método 4: Hash160 como número inteiro
    chave_4 = int.from_bytes(hash160_correto, 'big') % (2**64)
    numero_magico_4 = simular_gpu_errada_para_chave(chave_4)
    numeros_magicos.append(numero_magico_4.hex())
    print(f"Método 4 (int): {numero_magico_4.hex()}")
    
    # Método 5: Rotação de bits
    chave_5 = int.from_bytes(hash160_correto[:8], 'big')
    chave_5 = ((chave_5 << 13) | (chave_5 >> 51)) & 0xFFFFFFFFFFFFFFFF
    numero_magico_5 = simular_gpu_errada_para_chave(chave_5)
    numeros_magicos.append(numero_magico_5.hex())
    print(f"Método 5 (rotação): {numero_magico_5.hex()}")
    
    return numeros_magicos

def busca_reversa_otimizada(endereco_alvo, start_range, end_range, max_chaves=5000000):
    """
    Busca reversa otimizada - compara hash160 diretamente
    """
    print(f"\n⚡ BUSCA REVERSA OTIMIZADA")
    print("=" * 40)
    
    hash160_alvo = calculate_target_hash160(endereco_alvo)
    print(f"Hash160 alvo: {hash160_alvo.hex()}")
    print(f"Range: {start_range:,} - {end_range:,}")
    print(f"Máximo: {max_chaves:,} chaves")
    
    inicio = time.time()
    chaves_testadas = 0
    
    for chave in range(start_range, min(start_range + max_chaves, end_range)):
        try:
            # Comparar hash160 diretamente (mais rápido)
            hash160_gerado = private_key_to_hash160(chave)
            chaves_testadas += 1
            
            if hash160_gerado and hash160_gerado == hash160_alvo:
                tempo_total = time.time() - inicio
                velocidade = chaves_testadas / tempo_total
                
                print(f"\n🎉 CHAVE PRIVADA ENCONTRADA!")
                print(f"Chave: {chave}")
                print(f"Tempo: {tempo_total:.2f} segundos")
                print(f"Velocidade: {velocidade:.0f} chaves/segundo")
                
                # Calcular número mágico real
                numero_magico_real = simular_gpu_errada_para_chave(chave)
                print(f"Número mágico real: {numero_magico_real.hex()}")
                
                return chave, numero_magico_real.hex()
            
            # Progresso
            if chaves_testadas % 100000 == 0:
                tempo_decorrido = time.time() - inicio
                velocidade = chaves_testadas / tempo_decorrido
                progresso = (chaves_testadas / max_chaves) * 100
                
                print(f"Progresso: {progresso:.1f}% | {chaves_testadas:,} chaves | {velocidade:.0f} chaves/seg")
                
        except Exception as e:
            continue
    
    print(f"\n❌ Chave não encontrada em {chaves_testadas:,} tentativas")
    return None, None

def estrategia_completa(endereco_alvo, start_range, end_range):
    """
    Estratégia completa: aproximação + busca reversa
    """
    print("🎯 ESTRATÉGIA COMPLETA")
    print("=" * 30)
    
    # FASE 1: Gerar números mágicos aproximados
    print("FASE 1: Números mágicos aproximados")
    numeros_magicos = gerar_numeros_magicos_aproximados(endereco_alvo)
    
    print(f"\n🚀 TESTE ESTES NÚMEROS MÁGICOS PRIMEIRO:")
    for i, numero in enumerate(numeros_magicos, 1):
        print(f"{i}. ./buscar_chave_por_hash_errado {numero} {start_range}:{end_range}")
    
    # FASE 2: Se aproximação não funcionar, busca reversa
    print(f"\nFASE 2: Se aproximação falhar, busca reversa")
    
    continuar = input("\nExecutar busca reversa agora? (s/N): ")
    if continuar.lower().startswith('s'):
        chave, numero_real = busca_reversa_otimizada(endereco_alvo, start_range, end_range)
        
        if chave:
            print(f"\n✅ SUCESSO! Use este comando:")
            print(f"./buscar_chave_por_hash_errado {numero_real} {start_range}:{end_range}")
        else:
            print(f"\n💡 Tente aumentar o range ou usar busca probabilística")
    
    return numeros_magicos

def busca_probabilistica_inteligente(endereco_alvo, start_range, end_range, num_samples=1000000):
    """
    Busca probabilística com foco em ranges mais prováveis
    """
    print(f"\n🎲 BUSCA PROBABILÍSTICA INTELIGENTE")
    print("=" * 45)
    
    import random
    
    hash160_alvo = calculate_target_hash160(endereco_alvo)
    print(f"Hash160 alvo: {hash160_alvo.hex()}")
    print(f"Amostras: {num_samples:,}")
    
    # Definir sub-ranges mais prováveis
    sub_ranges = [
        (start_range, start_range + 1000000),  # Primeiro milhão
        (end_range - 1000000, end_range),      # Último milhão
        (start_range + (end_range - start_range) // 4, start_range + (end_range - start_range) // 2),  # Meio
    ]
    
    inicio = time.time()
    chaves_testadas = 0
    
    for i in range(num_samples):
        # Escolher sub-range aleatoriamente (com peso)
        if i % 3 == 0:
            range_escolhido = sub_ranges[0]  # Primeiro milhão (mais provável)
        elif i % 3 == 1:
            range_escolhido = sub_ranges[1]  # Último milhão
        else:
            range_escolhido = (start_range, end_range)  # Range completo
        
        # Gerar chave aleatória no sub-range
        chave = random.randint(max(range_escolhido[0], start_range), 
                              min(range_escolhido[1], end_range))
        
        try:
            hash160_gerado = private_key_to_hash160(chave)
            chaves_testadas += 1
            
            if hash160_gerado and hash160_gerado == hash160_alvo:
                tempo_total = time.time() - inicio
                
                print(f"\n🎉 CHAVE ENCONTRADA (PROBABILÍSTICA)!")
                print(f"Chave: {chave}")
                print(f"Tentativa: {i + 1}")
                print(f"Tempo: {tempo_total:.2f} segundos")
                
                numero_magico = simular_gpu_errada_para_chave(chave)
                print(f"Número mágico: {numero_magico.hex()}")
                
                return chave, numero_magico.hex()
            
            # Progresso
            if (i + 1) % 50000 == 0:
                tempo_decorrido = time.time() - inicio
                velocidade = chaves_testadas / tempo_decorrido
                progresso = ((i + 1) / num_samples) * 100
                
                print(f"Progresso: {progresso:.1f}% | {i + 1:,} tentativas | {velocidade:.0f} chaves/seg")
                
        except Exception as e:
            continue
    
    print(f"\n❌ Chave não encontrada em {num_samples:,} tentativas probabilísticas")
    return None, None

def main():
    """Função principal"""
    if len(sys.argv) >= 3:
        endereco_alvo = sys.argv[1]
        
        if ':' in sys.argv[2]:
            start_str, end_str = sys.argv[2].split(':')
            start_range = int(start_str, 16) if any(c in start_str.lower() for c in 'abcdef') else int(start_str)
            end_range = int(end_str, 16) if any(c in end_str.lower() for c in 'abcdef') else int(end_str)
        else:
            start_range = int(sys.argv[2])
            end_range = int(sys.argv[3]) if len(sys.argv) > 3 else start_range + 10000000
        
        print("🎯 BUSCA SEM NÚMERO MÁGICO")
        print("=" * 40)
        print(f"Endereço: {endereco_alvo}")
        print(f"Range: {start_range:,} - {end_range:,}")
        
        # Executar estratégia completa
        estrategia_completa(endereco_alvo, start_range, end_range)
        
    else:
        print("🎯 BUSCA SEM NÚMERO MÁGICO - DEMONSTRAÇÃO")
        print("=" * 50)
        
        print("Uso:")
        print("python3 busca_sem_numero_magico.py <endereco> <start:end>")
        print("python3 busca_sem_numero_magico.py <endereco> <start> <end>")
        print()
        print("Exemplos:")
        print("python3 busca_sem_numero_magico.py 1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2 1:1000")
        print("python3 busca_sem_numero_magico.py 1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU 4611686018427387904:9223372036854775807")
        
        # Demonstração com endereço conhecido
        print(f"\n🧪 DEMONSTRAÇÃO COM ENDEREÇO CONHECIDO:")
        endereco_demo = "1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2"  # Chave privada 1
        numeros_magicos = gerar_numeros_magicos_aproximados(endereco_demo)
        
        print(f"\n🎯 TESTE COM CHAVE CONHECIDA:")
        print("Sabemos que a chave privada é 1, então o número mágico real é:")
        numero_real = simular_gpu_errada_para_chave(1)
        print(f"Número mágico real: {numero_real.hex()}")
        
        print(f"\n📊 COMPARAÇÃO:")
        print(f"Real:     {numero_real.hex()}")
        for i, numero in enumerate(numeros_magicos, 1):
            match = "✅ MATCH!" if numero == numero_real.hex() else "❌"
            print(f"Método {i}: {numero} {match}")

if __name__ == "__main__":
    main()
