#!/usr/bin/env python3
"""
BUSCA ULTRA-RÁPIDA - Encontrar <PERSON><PERSON><PERSON> da Carteira Alvo

Este programa implementa a busca mais rápida possível para encontrar
o hash160 errado (número mágico) de qualquer carteira Bitcoin.
"""

import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from main import (
    private_key_to_address, 
    simular_gpu_errada_para_chave,
    calculate_target_hash160
)

class BuscaUltraRapida:
    def __init__(self, endereco_alvo):
        self.endereco_alvo = endereco_alvo
        self.hash160_correto = calculate_target_hash160(endereco_alvo)
        self.encontrado = False
        self.resultado = None
        self.chave_encontrada = None
        self.tentativas = 0
        self.inicio_tempo = None
        
    def testar_range(self, start, end, thread_id):
        """Testa um range específico de chaves privadas"""
        for chave_privada in range(start, end):
            if self.encontrado:
                return None
                
            try:
                # Gerar endereço para esta chave privada
                endereco_gerado = private_key_to_address(chave_privada)
                
                if endereco_gerado == self.endereco_alvo:
                    # ENCONTROU! Calcular número mágico
                    numero_magico = simular_gpu_errada_para_chave(chave_privada)
                    
                    self.encontrado = True
                    self.resultado = numero_magico.hex()
                    self.chave_encontrada = chave_privada
                    
                    print(f"\n🎉 ENCONTRADO! Thread {thread_id}")
                    print(f"   Chave privada: 0x{chave_privada:x} ({chave_privada})")
                    print(f"   Endereço: {endereco_gerado}")
                    print(f"   Hash160 correto: {self.hash160_correto.hex()}")
                    print(f"   Hash160 errado:  {self.resultado}")
                    
                    return {
                        'chave_privada': chave_privada,
                        'numero_magico': self.resultado,
                        'thread_id': thread_id
                    }
                
                self.tentativas += 1
                
                # Progresso a cada 10k tentativas
                if self.tentativas % 10000 == 0:
                    tempo_decorrido = time.time() - self.inicio_tempo
                    velocidade = self.tentativas / tempo_decorrido
                    print(f"   Thread {thread_id}: {self.tentativas:,} tentativas | {velocidade:.0f} chaves/seg")
                    
            except Exception as e:
                continue
                
        return None
    
    def buscar_paralelo(self, max_chaves=10000000, num_threads=8):
        """Busca paralela ultra-rápida"""
        print(f"🚀 INICIANDO BUSCA ULTRA-RÁPIDA")
        print(f"   Endereço alvo: {self.endereco_alvo}")
        print(f"   Hash160 correto: {self.hash160_correto.hex()}")
        print(f"   Máximo de chaves: {max_chaves:,}")
        print(f"   Threads: {num_threads}")
        
        self.inicio_tempo = time.time()
        
        # Dividir o range entre threads
        chunk_size = max_chaves // num_threads
        ranges = []
        
        for i in range(num_threads):
            start = i * chunk_size + 1
            end = (i + 1) * chunk_size + 1 if i < num_threads - 1 else max_chaves + 1
            ranges.append((start, end, i))
        
        print(f"\n📊 RANGES DE BUSCA:")
        for start, end, thread_id in ranges:
            print(f"   Thread {thread_id}: {start:,} - {end-1:,}")
        
        # Executar busca paralela
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [
                executor.submit(self.testar_range, start, end, thread_id)
                for start, end, thread_id in ranges
            ]
            
            try:
                for future in as_completed(futures):
                    resultado = future.result()
                    if resultado:
                        # Cancelar outras threads
                        for f in futures:
                            f.cancel()
                        
                        tempo_total = time.time() - self.inicio_tempo
                        velocidade_media = self.tentativas / tempo_total
                        
                        print(f"\n✅ BUSCA CONCLUÍDA!")
                        print(f"   Tempo total: {tempo_total:.2f} segundos")
                        print(f"   Tentativas: {self.tentativas:,}")
                        print(f"   Velocidade média: {velocidade_media:.0f} chaves/seg")
                        
                        return resultado
                        
            except KeyboardInterrupt:
                print(f"\n⏹️  Busca interrompida pelo usuário")
                return None
        
        # Se chegou aqui, não encontrou
        tempo_total = time.time() - self.inicio_tempo
        velocidade_media = self.tentativas / tempo_total if tempo_total > 0 else 0
        
        print(f"\n❌ NÃO ENCONTRADO")
        print(f"   Tempo total: {tempo_total:.2f} segundos")
        print(f"   Tentativas: {self.tentativas:,}")
        print(f"   Velocidade média: {velocidade_media:.0f} chaves/seg")
        
        return None
    
    def buscar_ranges_inteligentes(self):
        """Busca em ranges onde chaves Bitcoin costumam estar"""
        print(f"🧠 BUSCA INTELIGENTE EM RANGES CONHECIDOS")
        
        # Ranges onde chaves Bitcoin costumam estar
        ranges_inteligentes = [
            (1, 1000),                      # Chaves muito pequenas
            (1000, 100000),                 # Chaves pequenas
            (0x10000, 0x100000),           # 64K - 1M
            (0x100000, 0x1000000),         # 1M - 16M
            (0x1000000, 0x10000000),       # 16M - 256M
            (0x10000000, 0x100000000),     # 256M - 4G
            (0x100000000, 0x1000000000),   # 4G - 64G
        ]
        
        for i, (start, end) in enumerate(ranges_inteligentes):
            if self.encontrado:
                break
                
            print(f"\n📍 TESTANDO RANGE {i+1}/{len(ranges_inteligentes)}")
            print(f"   Range: 0x{start:x} - 0x{end:x} ({start:,} - {end:,})")
            
            # Buscar neste range com múltiplas threads
            resultado = self.buscar_paralelo(max_chaves=end-start, num_threads=4)
            
            if resultado:
                return resultado
        
        return None

def buscar_hash_errado_ultra_rapido(endereco_alvo):
    """Função principal para busca ultra-rápida"""
    print("="*80)
    print("⚡ BUSCA ULTRA-RÁPIDA DE HASH ERRADO")
    print("="*80)
    
    busca = BuscaUltraRapida(endereco_alvo)
    
    # Método 1: Busca em ranges inteligentes
    print(f"\n🎯 MÉTODO 1: RANGES INTELIGENTES")
    resultado = busca.buscar_ranges_inteligentes()
    
    if resultado:
        return resultado['numero_magico']
    
    # Método 2: Busca massiva paralela
    print(f"\n🎯 MÉTODO 2: BUSCA MASSIVA PARALELA")
    resultado = busca.buscar_paralelo(max_chaves=100000000, num_threads=16)
    
    if resultado:
        return resultado['numero_magico']
    
    print(f"\n❌ HASH ERRADO NÃO ENCONTRADO")
    print(f"   A chave privada pode estar em um range muito alto")
    return None

def main():
    """Função principal"""
    print("⚡ BUSCA ULTRA-RÁPIDA - HASH ERRADO DA CARTEIRA ALVO")
    
    # Endereço alvo
    endereco_alvo = "1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU"
    
    print(f"\n🎯 ENDEREÇO ALVO: {endereco_alvo}")
    
    # Iniciar busca ultra-rápida
    inicio = time.time()
    numero_magico = buscar_hash_errado_ultra_rapido(endereco_alvo)
    tempo_total = time.time() - inicio
    
    print(f"\n" + "="*80)
    print(f"📋 RESULTADO FINAL")
    print(f"="*80)
    
    if numero_magico:
        print(f"✅ SUCESSO!")
        print(f"   Endereço: {endereco_alvo}")
        print(f"   Hash160 errado: {numero_magico}")
        print(f"   Tempo total: {tempo_total:.2f} segundos")
        print(f"   🎩 Use este hash160 errado no programa CUDA!")
    else:
        print(f"❌ FALHOU!")
        print(f"   Não foi possível encontrar a chave privada")
        print(f"   Tempo gasto: {tempo_total:.2f} segundos")
        print(f"   💡 Sugestão: A chave pode estar em um range muito alto")

if __name__ == "__main__":
    main()
