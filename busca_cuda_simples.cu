/*
BUSCA CUDA SIMPLES - Hash Errado
<PERSON>ão simplificada e compatível para máxima compatibilidade
*/

#include <cuda_runtime.h>
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <time.h>

// Constantes
#define GOLDEN_RATIO 0x9E3779B9ULL
#define MULT_CONST 0x9E
#define HASH_SIZE 20

// Coordenadas do ponto gerador secp256k1
__constant__ uint64_t gx[4] = {
    0x79BE667EF9DCBBACULL, 0x55A06295CE870B07ULL, 
    0x029BFCDB2DCE28D9ULL, 0x59F2815B16F81798ULL
};

// Resultado global
__device__ uint64_t d_result = 0;
__device__ int d_found = 0;

__device__ void calcular_hash_errado_simples(uint64_t private_key, uint8_t* hash_errado) {
    // Versão simplificada da função hash errado
    
    // PASSO 1: Transformar coordenadas
    uint64_t px[4];
    for (int i = 0; i < 4; i++) {
        uint64_t rotacao = (private_key << (i * 8)) | (private_key >> (56 - i * 8));
        px[i] = gx[i] ^ rotacao;
        px[i] = ((px[i] << 1) ^ (px[i] >> 63));
    }
    
    // PASSO 2: Paridade Y
    uint8_t y_parity = 2 + ((private_key ^ px[0]) & 1);
    
    // PASSO 3: Gerar hash errado
    for (int i = 0; i < HASH_SIZE; i++) {
        uint32_t hash_val = 0;
        
        // Simular chave pública
        hash_val ^= (y_parity + i) & 0xFF;
        
        // Adicionar coordenadas
        for (int j = 0; j < 4; j++) {
            for (int k = 0; k < 8; k++) {
                uint8_t coord_byte = (px[j] >> (56 - k * 8)) & 0xFF;
                hash_val ^= (coord_byte + i + j * 8 + k + 1) & 0xFF;
            }
        }
        
        // Operações com chave privada
        hash_val ^= (private_key >> (i & 7)) & 0xFF;
        hash_val ^= (private_key >> ((i + 8) & 15)) & 0xFF;
        hash_val ^= (private_key * (i + 1)) & 0xFF;
        hash_val ^= ((private_key + i) * GOLDEN_RATIO) & 0xFF;
        
        // Transformação final
        hash_val &= 0xFF;
        hash_errado[i] = ((hash_val * MULT_CONST) ^ (hash_val >> 4)) & 0xFF;
    }
}

__global__ void busca_kernel_simples(
    uint64_t start_range, 
    uint64_t end_range,
    uint8_t* target_hash
) {
    uint64_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    uint64_t stride = gridDim.x * blockDim.x;
    
    uint8_t hash_calculado[HASH_SIZE];
    
    for (uint64_t private_key = start_range + idx; 
         private_key < end_range && !d_found; 
         private_key += stride) {
        
        // Calcular hash errado
        calcular_hash_errado_simples(private_key, hash_calculado);
        
        // Comparar com target
        bool match = true;
        for (int i = 0; i < HASH_SIZE; i++) {
            if (hash_calculado[i] != target_hash[i]) {
                match = false;
                break;
            }
        }
        
        // Se encontrou
        if (match && !d_found) {
            d_result = private_key;
            d_found = 1;
            printf("GPU ENCONTROU! Chave: %llu (0x%llx)\n", private_key, private_key);
        }
    }
}

int main(int argc, char* argv[]) {
    printf("🚀 BUSCA CUDA SIMPLES - HASH ERRADO\n");
    printf("===================================\n");
    
    if (argc < 2) {
        printf("Uso: %s <hash_errado_hex> [start] [end]\n", argv[0]);
        printf("Exemplo: %s 36df2f22295784ab7f81989f9247bfd99bb00c03\n", argv[0]);
        return 1;
    }
    
    const char* target_hash_hex = argv[1];
    uint64_t start_range = (argc > 2) ? strtoull(argv[2], NULL, 0) : 1;
    uint64_t end_range = (argc > 3) ? strtoull(argv[3], NULL, 0) : 10000000;
    
    printf("Target hash: %s\n", target_hash_hex);
    printf("Range: %llu - %llu\n", start_range, end_range);
    
    // Verificar CUDA
    int device_count;
    cudaGetDeviceCount(&device_count);
    if (device_count == 0) {
        printf("❌ Nenhuma GPU CUDA encontrada!\n");
        return 1;
    }
    
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, 0);
    printf("GPU: %s\n", prop.name);
    
    // Converter hex para bytes
    uint8_t target_hash[HASH_SIZE];
    for (int i = 0; i < HASH_SIZE; i++) {
        sscanf(target_hash_hex + i * 2, "%2hhx", &target_hash[i]);
    }
    
    // Alocar memória GPU
    uint8_t* d_target_hash;
    cudaMalloc(&d_target_hash, HASH_SIZE);
    cudaMemcpy(d_target_hash, target_hash, HASH_SIZE, cudaMemcpyHostToDevice);
    
    // Reset resultado
    uint64_t zero = 0;
    int zero_int = 0;
    cudaMemcpyToSymbol(d_result, &zero, sizeof(uint64_t));
    cudaMemcpyToSymbol(d_found, &zero_int, sizeof(int));
    
    // Configuração do kernel
    int threads_per_block = 256;
    int blocks_per_grid = 1024;
    
    printf("Configuração: %d threads/block, %d blocks\n", threads_per_block, blocks_per_grid);
    printf("Iniciando busca...\n");
    
    // Timing
    clock_t inicio = clock();
    
    // Lançar kernel
    busca_kernel_simples<<<blocks_per_grid, threads_per_block>>>(
        start_range, end_range, d_target_hash
    );
    
    // Aguardar conclusão
    cudaDeviceSynchronize();
    
    clock_t fim = clock();
    double tempo_total = ((double)(fim - inicio)) / CLOCKS_PER_SEC;
    
    // Verificar resultado
    uint64_t resultado;
    int encontrado;
    cudaMemcpyFromSymbol(&resultado, d_result, sizeof(uint64_t));
    cudaMemcpyFromSymbol(&encontrado, d_found, sizeof(int));
    
    printf("\n📊 RESULTADOS:\n");
    printf("Tempo: %.2f segundos\n", tempo_total);
    printf("Chaves testadas: %llu\n", end_range - start_range);
    
    if (tempo_total > 0) {
        double velocidade = (end_range - start_range) / tempo_total;
        printf("Velocidade: %.0f chaves/segundo\n", velocidade);
        printf("Velocidade: %.2f M chaves/segundo\n", velocidade / 1000000.0);
    }
    
    if (encontrado) {
        printf("✅ CHAVE ENCONTRADA: %llu (0x%llx)\n", resultado, resultado);
    } else {
        printf("❌ Chave não encontrada no range\n");
    }
    
    // Limpeza
    cudaFree(d_target_hash);
    
    return encontrado ? 0 : 1;
}
