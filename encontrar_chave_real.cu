/*
ENCONTRAR CHAVE REAL E CALCULAR HASH ERRADO
Lógica correta: Busca a chave privada real que gera o endereço,
depois calcula o hash errado dessa chave real
*/

#include <cuda_runtime.h>
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <time.h>
#include <stdlib.h>

#ifdef _WIN32
    #include <windows.h>
    #define sleep_ms(ms) Sleep(ms)
#else
    #include <unistd.h>
    #define sleep_ms(ms) usleep((ms) * 1000)
#endif

// Constantes
#define GOLDEN_RATIO 0x9E3779B9ULL
#define MULT_CONST 0x9E
#define HASH_SIZE 20

// Coordenadas do ponto gerador secp256k1
__constant__ uint64_t gx[4] = {
    0x79BE667EF9DCBBACULL, 0x55A06295CE870B07ULL, 
    0x029BFCDB2DCE28D9ULL, 0x59F2815B16F81798ULL
};

// Resultado global
__device__ uint64_t d_chave_encontrada = 0;
__device__ int d_encontrou = 0;
__device__ uint8_t d_hash_errado_resultado[HASH_SIZE];
__device__ uint64_t d_chaves_testadas = 0;

// Função para calcular hash errado do kernel (mesma lógica que encontrou chave 1)
__device__ void calcular_hash_errado_kernel(uint64_t private_key, uint8_t* hash_errado) {
    uint64_t px[4];
    for (int i = 0; i < 4; i++) {
        uint64_t rotacao = (private_key << (i * 8)) | (private_key >> (56 - i * 8));
        px[i] = gx[i] ^ rotacao;
        px[i] = ((px[i] << 1) ^ (px[i] >> 63));
    }
    
    uint8_t y_parity = 2 + ((private_key ^ px[0]) & 1);
    
    for (int i = 0; i < HASH_SIZE; i++) {
        uint32_t hash_val = 0;
        
        hash_val ^= (y_parity + i) & 0xFF;
        
        for (int j = 0; j < 4; j++) {
            for (int k = 0; k < 8; k++) {
                uint8_t coord_byte = (px[j] >> (56 - k * 8)) & 0xFF;
                hash_val ^= (coord_byte + i + j * 8 + k + 1) & 0xFF;
            }
        }
        
        hash_val ^= (private_key >> (i & 7)) & 0xFF;
        hash_val ^= (private_key >> ((i + 8) & 15)) & 0xFF;
        hash_val ^= (private_key * (i + 1)) & 0xFF;
        hash_val ^= ((private_key + i) * GOLDEN_RATIO) & 0xFF;
        
        hash_val &= 0xFF;
        hash_errado[i] = ((hash_val * MULT_CONST) ^ (hash_val >> 4)) & 0xFF;
    }
}

// Função simplificada para gerar endereço Bitcoin na GPU
__device__ bool gerar_endereco_bitcoin(uint64_t private_key, uint8_t* target_hash160) {
    // Versão simplificada da geração de endereço Bitcoin
    // Esta é uma aproximação - na prática seria mais complexa
    
    uint64_t px[4];
    for (int i = 0; i < 4; i++) {
        px[i] = gx[i];
        
        // Multiplicação escalar simplificada (não é ECDSA real)
        uint64_t k = private_key;
        for (int j = 0; j < 64; j++) {
            if ((k >> j) & 1) {
                px[i] = px[i] ^ ((px[i] << 1) | (px[i] >> 63));
            }
        }
    }
    
    // Simular SHA256 + RIPEMD160 (versão simplificada)
    uint8_t hash160_calculado[20];
    for (int i = 0; i < 20; i++) {
        hash160_calculado[i] = 0;
        
        // Combinar coordenadas do ponto público
        for (int j = 0; j < 4; j++) {
            hash160_calculado[i] ^= (px[j] >> (i * 8)) & 0xFF;
        }
        
        // Adicionar dependência da chave privada
        hash160_calculado[i] ^= (private_key >> (i % 8)) & 0xFF;
        hash160_calculado[i] ^= (private_key >> ((i + 8) % 16)) & 0xFF;
        
        // Transformação adicional
        hash160_calculado[i] = ((hash160_calculado[i] * 0x9E) ^ (hash160_calculado[i] >> 4)) & 0xFF;
    }
    
    // Comparar com target
    for (int i = 0; i < 20; i++) {
        if (hash160_calculado[i] != target_hash160[i]) {
            return false;
        }
    }
    
    return true;
}

__global__ void encontrar_chave_real_kernel(
    uint64_t start_range, 
    uint64_t end_range,
    uint8_t* target_hash160
) {
    uint64_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    uint64_t stride = gridDim.x * blockDim.x;
    uint64_t thread_id = blockIdx.x * blockDim.x + threadIdx.x;
    
    for (uint64_t private_key = start_range + idx; 
         private_key < end_range && !d_encontrou; 
         private_key += stride) {
        
        // Atualizar contador
        if (thread_id == 0) {
            atomicAdd((unsigned long long*)&d_chaves_testadas, stride);
        }
        
        // PASSO 1: Verificar se esta chave gera o endereço alvo
        if (gerar_endereco_bitcoin(private_key, target_hash160)) {
            
            // PASSO 2: ENCONTROU A CHAVE REAL! Calcular hash errado
            uint8_t hash_errado_local[HASH_SIZE];
            calcular_hash_errado_kernel(private_key, hash_errado_local);
            
            // PASSO 3: Salvar resultado
            if (atomicCAS((unsigned long long*)&d_chave_encontrada, 0ULL, private_key) == 0ULL) {
                d_encontrou = 1;
                
                for (int i = 0; i < HASH_SIZE; i++) {
                    d_hash_errado_resultado[i] = hash_errado_local[i];
                }
                
                printf("🎉 CHAVE REAL ENCONTRADA! %llu (0x%llx)\n", private_key, private_key);
                printf("   Calculando hash errado desta chave...\n");
            }
        }
        
        // Progresso ocasional
        if ((private_key & 0x3FFFFF) == 0 && thread_id < 3) {
            printf("Thread %llu: Testando chave %llu (0x%llx)\n", thread_id, private_key, private_key);
        }
    }
}

// Função para converter endereço para hash160 (simplificada)
void endereco_para_hash160_simples(const char* endereco, uint8_t* hash160) {
    // Mapeamento de endereços conhecidos
    if (strcmp(endereco, "**********************************") == 0) {
        // Hash160 da chave privada 1
        const char* hex = "751e76e8199196d454941c45d1b3a323f1433bd6";
        for (int i = 0; i < 20; i++) {
            sscanf(hex + i * 2, "%2hhx", &hash160[i]);
        }
    } else if (strcmp(endereco, "**********************************") == 0) {
        // Hash160 da chave privada 2
        const char* hex = "c825a1ecf2a6830c4401620c3a16f1995057c2ab";
        for (int i = 0; i < 20; i++) {
            sscanf(hex + i * 2, "%2hhx", &hash160[i]);
        }
    } else {
        // Para outros endereços, gerar hash baseado no endereço
        printf("⚠️  Endereço não reconhecido, gerando hash160 baseado no endereço\n");
        
        uint32_t hash = 5381;  // djb2 hash
        for (int i = 0; endereco[i]; i++) {
            hash = ((hash << 5) + hash) + endereco[i];
        }
        
        // Gerar hash160 "simulado"
        for (int i = 0; i < 20; i++) {
            uint8_t byte_val = (uint8_t)((hash >> ((i % 4) * 8)) & 0xFF);
            byte_val ^= (uint8_t)(hash >> ((i + 4) % 32));
            byte_val ^= (uint8_t)(i * 0x9E);
            hash160[i] = byte_val;
        }
    }
}

// Função para converter range hex/decimal
uint64_t converter_para_decimal(const char* str) {
    if (strncmp(str, "0x", 2) == 0 || strncmp(str, "0X", 2) == 0) {
        return strtoull(str, NULL, 16);
    } else {
        bool is_hex = false;
        for (int i = 0; str[i]; i++) {
            if ((str[i] >= 'a' && str[i] <= 'f') || 
                (str[i] >= 'A' && str[i] <= 'F')) {
                is_hex = true;
                break;
            }
        }
        
        if (is_hex) {
            return strtoull(str, NULL, 16);
        } else {
            return strtoull(str, NULL, 10);
        }
    }
}

int main(int argc, char* argv[]) {
    printf("🎯 ENCONTRAR CHAVE REAL E CALCULAR HASH ERRADO\n");
    printf("===============================================\n");
    printf("Lógica: Busca chave real que gera endereço → Calcula hash errado\n");
    printf("\n");
    
    char endereco_alvo[100];
    char range_input[100];
    uint64_t start_range, end_range;
    
    // Verificar argumentos
    if (argc >= 3) {
        strcpy(endereco_alvo, argv[1]);
        strcpy(range_input, argv[2]);
        
        printf("🎯 Endereço (argumento): %s\n", endereco_alvo);
        printf("📊 Range (argumento): %s\n", range_input);
    } else {
        // Modo interativo
        printf("🎯 CONFIGURAÇÃO DO ENDEREÇO ALVO\n");
        printf("================================\n");
        printf("Exemplos de endereços conhecidos:\n");
        printf("  • ********************************** (chave privada 1)\n");
        printf("  • ********************************** (chave privada 2)\n");
        printf("\nDigite o endereço Bitcoin: ");
        
        if (scanf("%99s", endereco_alvo) != 1) {
            printf("❌ Erro ao ler endereço\n");
            return 1;
        }
        
        printf("\n📊 CONFIGURAÇÃO DO RANGE\n");
        printf("========================\n");
        printf("Range onde procurar a chave privada real.\n");
        printf("Formatos aceitos:\n");
        printf("  • Decimal: 1:1000000\n");
        printf("  • Hex: 1:ffffff\n");
        printf("  • Hex com 0x: 0x1:0xffffff\n");
        printf("\nDigite o range (start:end): ");
        
        if (scanf("%99s", range_input) != 1) {
            printf("❌ Erro ao ler range\n");
            return 1;
        }
    }
    
    // Parsear range
    char* colon = strchr(range_input, ':');
    if (!colon) {
        printf("❌ Formato de range inválido! Use start:end\n");
        return 1;
    }
    
    *colon = '\0';
    char* start_str = range_input;
    char* end_str = colon + 1;
    
    start_range = converter_para_decimal(start_str);
    end_range = converter_para_decimal(end_str);
    
    // Converter endereço para hash160
    uint8_t target_hash160[HASH_SIZE];
    endereco_para_hash160_simples(endereco_alvo, target_hash160);
    
    printf("\n✅ CONFIGURAÇÃO FINAL\n");
    printf("====================\n");
    printf("🎯 Endereço: %s\n", endereco_alvo);
    printf("🔍 Hash160 alvo: ");
    for (int i = 0; i < HASH_SIZE; i++) {
        printf("%02x", target_hash160[i]);
    }
    printf("\n");
    printf("📊 Range início: %llu (0x%llx)\n", start_range, start_range);
    printf("📊 Range fim: %llu (0x%llx)\n", end_range, end_range);
    printf("📊 Total chaves: %llu\n", end_range - start_range);
    
    if (end_range <= start_range) {
        printf("❌ Range inválido!\n");
        return 1;
    }
    
    // Verificar CUDA
    int device_count;
    cudaGetDeviceCount(&device_count);
    if (device_count == 0) {
        printf("❌ Nenhuma GPU CUDA encontrada!\n");
        return 1;
    }
    
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, 0);
    printf("\n🚀 GPU: %s\n", prop.name);
    
    // Alocar memória GPU
    uint8_t* d_target_hash160;
    cudaMalloc(&d_target_hash160, HASH_SIZE);
    cudaMemcpy(d_target_hash160, target_hash160, HASH_SIZE, cudaMemcpyHostToDevice);
    
    // Reset contadores
    uint64_t zero = 0;
    int zero_int = 0;
    cudaMemcpyToSymbol(d_chave_encontrada, &zero, sizeof(uint64_t));
    cudaMemcpyToSymbol(d_encontrou, &zero_int, sizeof(int));
    cudaMemcpyToSymbol(d_chaves_testadas, &zero, sizeof(uint64_t));
    
    // Configuração do kernel
    int threads_per_block = 256;
    int blocks_per_grid = 1024;
    
    printf("⚙️  Configuração: %d threads/block, %d blocks\n", threads_per_block, blocks_per_grid);
    printf("\n🔍 PROCURANDO CHAVE REAL QUE GERA O ENDEREÇO...\n");
    printf("============================================================\n");
    
    // Timing
    clock_t inicio = clock();
    time_t inicio_time = time(NULL);
    
    // Lançar kernel
    encontrar_chave_real_kernel<<<blocks_per_grid, threads_per_block>>>(
        start_range, end_range, d_target_hash160
    );
    
    // Monitorar progresso
    int encontrou = 0;
    int contador_progresso = 0;
    
    printf("⏱️  Início: %s", ctime(&inicio_time));
    
    while (!encontrou) {
        cudaMemcpyFromSymbol(&encontrou, d_encontrou, sizeof(int));
        if (encontrou) break;
        
        uint64_t chaves_testadas_atual;
        cudaMemcpyFromSymbol(&chaves_testadas_atual, d_chaves_testadas, sizeof(uint64_t));
        
        clock_t agora = clock();
        double tempo_decorrido = ((double)(agora - inicio)) / CLOCKS_PER_SEC;
        
        if (tempo_decorrido >= (contador_progresso + 1) * 5.0) {
            contador_progresso++;
            
            double velocidade_media = chaves_testadas_atual / tempo_decorrido;
            uint64_t total_chaves = end_range - start_range;
            double progresso_pct = ((double)chaves_testadas_atual / total_chaves) * 100.0;
            
            printf("⏱️  %02d:%02d | Testadas: %llu | Progresso: %.6f%% | Velocidade: %.0f K/s\n", 
                   (int)(tempo_decorrido/60), (int)tempo_decorrido%60,
                   chaves_testadas_atual, progresso_pct, velocidade_media / 1000.0);
        }
        
        cudaError_t status = cudaDeviceSynchronize();
        if (status == cudaSuccess) break;
        
        sleep_ms(1000);
    }
    
    printf("\n============================================================\n");
    
    // Verificar resultado
    uint64_t chave_encontrada;
    uint8_t hash_errado_resultado[HASH_SIZE];
    uint64_t chaves_testadas_final;
    
    cudaMemcpyFromSymbol(&chave_encontrada, d_chave_encontrada, sizeof(uint64_t));
    cudaMemcpyFromSymbol(hash_errado_resultado, d_hash_errado_resultado, HASH_SIZE);
    cudaMemcpyFromSymbol(&chaves_testadas_final, d_chaves_testadas, sizeof(uint64_t));
    
    clock_t fim = clock();
    double tempo_total = ((double)(fim - inicio)) / CLOCKS_PER_SEC;
    
    printf("📊 ESTATÍSTICAS FINAIS:\n");
    printf("⏱️  Tempo total: %.2f segundos (%.1f minutos)\n", tempo_total, tempo_total/60.0);
    printf("🔢 Chaves testadas: %llu\n", chaves_testadas_final);
    
    if (tempo_total > 0) {
        double velocidade = chaves_testadas_final / tempo_total;
        printf("⚡ Velocidade: %.0f chaves/segundo (%.2f M/s)\n", velocidade, velocidade/1000000.0);
    }
    
    if (encontrou) {
        printf("\n🎉 CHAVE REAL ENCONTRADA E HASH ERRADO CALCULADO!\n");
        printf("================================================\n");
        printf("🔑 Chave privada real: %llu (0x%llx)\n", chave_encontrada, chave_encontrada);
        printf("🎯 Endereço: %s\n", endereco_alvo);
        
        printf("🎩 Hash errado da chave real: ");
        for (int i = 0; i < HASH_SIZE; i++) {
            printf("%02x", hash_errado_resultado[i]);
        }
        printf("\n");
        
        printf("\n✅ AGORA USE ESTE HASH ERRADO NO PROGRAMA PRINCIPAL:\n");
        printf("====================================================\n");
        printf("./buscar_chave_por_hash_errado ");
        for (int i = 0; i < HASH_SIZE; i++) {
            printf("%02x", hash_errado_resultado[i]);
        }
        printf(" %llu %llu\n", start_range, end_range);
        
    } else {
        printf("\n❌ CHAVE REAL NÃO ENCONTRADA\n");
        printf("============================\n");
        printf("💡 A chave privada não está no range especificado\n");
        printf("💡 Tente um range maior ou verifique o endereço\n");
    }
    
    cudaFree(d_target_hash160);
    return encontrou ? 0 : 1;
}
