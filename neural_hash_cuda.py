#!/usr/bin/env python3
"""
REDE NEURAL CUDA PARA HASH PREDICTOR
Treinamento ultra-rápido em GPU com CuPy e integração com test_magic_number.py
"""

import numpy as np
import time
import json
import random
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed

# Tentar importar CuPy para GPU
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("✅ CuPy disponível - Treinamento em GPU habilitado")
except ImportError:
    import numpy as cp
    GPU_AVAILABLE = False
    print("⚠️  CuPy não encontrado - Usando CPU (instale: pip install cupy-cuda12x)")

# Importar funções necessárias
try:
    from test_magic_number import simular_gpu_errada_para_chave
    from main import private_key_to_address
    from bitcoin_conversions import calculate_target_hash160
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    exit(1)

def generate_sample_batch_cuda(key_batch):
    """Gera batch de amostras para CUDA"""
    inputs = []
    outputs = []
    successful_keys = []
    
    for private_key in key_batch:
        try:
            endereco = private_key_to_address(private_key)
            if not endereco:
                continue
            
            hash160_correto = calculate_target_hash160(endereco)
            if not hash160_correto:
                continue
            
            numero_magico = simular_gpu_errada_para_chave(private_key)
            if not numero_magico:
                continue
            
            input_vector = hex_to_vector_cuda(hash160_correto.hex())
            output_vector = hex_to_vector_cuda(numero_magico.hex())
            
            inputs.append(input_vector)
            outputs.append(output_vector)
            successful_keys.append(private_key)
            
        except Exception:
            continue
    
    return inputs, outputs, successful_keys

def hex_to_vector_cuda(hex_string):
    """Converte hex para vetor otimizado para CUDA"""
    hex_string = hex_string.ljust(40, '0')[:40]
    
    vector = np.zeros(40, dtype=np.float32)
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            vector[i] = int(char, 16) / 15.0
        else:
            vector[i] = 0.0
    
    return vector

class CudaNeuralNetwork:
    """Rede Neural otimizada para CUDA"""
    
    def __init__(self, layers=[40, 512, 1024, 2048, 1024, 512, 40], use_gpu=True):
        self.layers = layers
        self.use_gpu = use_gpu and GPU_AVAILABLE
        self.xp = cp if self.use_gpu else np
        
        # Inicializar pesos na GPU/CPU
        self.weights = []
        self.biases = []
        
        print(f"🧠 Rede Neural CUDA: {' → '.join(map(str, layers))}")
        print(f"🔧 Dispositivo: {'GPU (CUDA)' if self.use_gpu else 'CPU'}")
        
        # Inicialização He otimizada
        for i in range(len(layers) - 1):
            fan_in = layers[i]
            std = self.xp.sqrt(2.0 / fan_in)
            
            w = self.xp.random.normal(0, std, (fan_in, layers[i+1])).astype(self.xp.float32)
            b = self.xp.zeros((1, layers[i+1]), dtype=self.xp.float32)
            
            self.weights.append(w)
            self.biases.append(b)
        
        total_params = sum(w.size for w in self.weights) + sum(b.size for b in self.biases)
        print(f"📊 Parâmetros: {total_params:,}")
        
        if self.use_gpu:
            try:
                device = cp.cuda.Device()
                # Tentar diferentes métodos para obter nome da GPU
                try:
                    gpu_name = device.name
                except AttributeError:
                    try:
                        gpu_name = cp.cuda.runtime.getDeviceProperties(device.id)['name'].decode()
                    except:
                        gpu_name = f"GPU {device.id}"

                print(f"🚀 GPU: {gpu_name}")

                # Tentar obter informações de memória
                try:
                    mem_info = device.mem_info
                    print(f"💾 Memória GPU: {mem_info[1] / 1024**3:.1f} GB")
                except:
                    print(f"💾 Memória GPU: Informação não disponível")
            except Exception as e:
                print(f"⚠️  Erro ao obter info da GPU: {e}")
                print(f"🚀 GPU: Disponível (detalhes não acessíveis)")
    
    def swish_cuda(self, x):
        """Função Swish otimizada para CUDA"""
        return x * (1.0 / (1.0 + self.xp.exp(-self.xp.clip(x, -10, 10))))
    
    def swish_derivative_cuda(self, x):
        """Derivada Swish otimizada"""
        sigmoid_x = 1.0 / (1.0 + self.xp.exp(-self.xp.clip(x, -10, 10)))
        return sigmoid_x + x * sigmoid_x * (1.0 - sigmoid_x)
    
    def forward_cuda(self, X):
        """Forward pass otimizado para CUDA"""
        activations = [X]
        z_values = []
        
        for i, (w, b) in enumerate(zip(self.weights, self.biases)):
            # Multiplicação matricial otimizada
            z = self.xp.dot(activations[-1], w) + b
            z_values.append(z)
            
            # Ativação
            if i < len(self.weights) - 1:
                a = self.swish_cuda(z)
            else:
                a = 1.0 / (1.0 + self.xp.exp(-self.xp.clip(z, -10, 10)))  # Sigmoid
            
            activations.append(a)
        
        return activations, z_values
    
    def backward_cuda(self, X, y, activations, z_values, learning_rate, l2_reg):
        """Backward pass otimizado para CUDA"""
        m = X.shape[0]
        
        # Erro da saída
        output_error = activations[-1] - y
        
        # Backpropagation
        errors = [output_error]
        
        for i in range(len(self.weights) - 1, 0, -1):
            error = self.xp.dot(errors[-1], self.weights[i].T)
            error *= self.swish_derivative_cuda(z_values[i-1])
            errors.append(error)
        
        errors.reverse()
        
        # Atualizar pesos com operações vetorizadas
        for i in range(len(self.weights)):
            dw = self.xp.dot(activations[i].T, errors[i]) / m
            db = self.xp.mean(errors[i], axis=0, keepdims=True)
            
            # Regularização L2
            dw += l2_reg * self.weights[i]
            
            # Atualização otimizada
            self.weights[i] -= learning_rate * dw
            self.biases[i] -= learning_rate * db
        
        # Loss
        mse_loss = self.xp.mean(output_error ** 2)
        l2_loss = sum(self.xp.sum(w ** 2) for w in self.weights) * l2_reg
        
        return float(mse_loss + l2_loss)
    
    def train_cuda_batch(self, X_batch, y_batch, learning_rate, l2_reg):
        """Treina um batch em CUDA"""
        # Converter para GPU se necessário
        if self.use_gpu:
            if not isinstance(X_batch, cp.ndarray):
                X_batch = cp.asarray(X_batch)
            if not isinstance(y_batch, cp.ndarray):
                y_batch = cp.asarray(y_batch)
        
        # Forward e backward pass
        activations, z_values = self.forward_cuda(X_batch)
        loss = self.backward_cuda(X_batch, y_batch, activations, z_values, learning_rate, l2_reg)
        
        return loss
    
    def predict_cuda(self, X):
        """Predição otimizada"""
        if self.use_gpu and not isinstance(X, cp.ndarray):
            X = cp.asarray(X)
        
        activations, _ = self.forward_cuda(X)
        result = activations[-1]
        
        # Converter de volta para CPU se necessário
        if self.use_gpu:
            result = cp.asnumpy(result)
        
        return result

class CudaHashPredictor:
    """Preditor de hash com aceleração CUDA"""
    
    def __init__(self, use_gpu=True):
        self.model = None
        self.use_gpu = use_gpu and GPU_AVAILABLE
        self.training_history = []
        
        print(f"🚀 CUDA Hash Predictor")
        print(f"🔧 GPU: {'Habilitada' if self.use_gpu else 'Desabilitada'}")
    
    def vector_to_hex(self, vector):
        """Converte vetor para hex"""
        hex_chars = '0123456789abcdef'
        hex_string = ''
        
        for val in vector:
            idx = int(round(float(val) * 15))
            idx = max(0, min(15, idx))
            hex_string += hex_chars[idx]
        
        return hex_string
    
    def generate_cuda_dataset(self, num_samples=50000, start_key=1, end_key=1000000):
        """Gera dataset otimizado para CUDA"""
        print(f"🚀 GERANDO DATASET CUDA: {num_samples:,} AMOSTRAS")
        print("=" * 50)
        
        # Chaves aleatórias
        all_keys = random.sample(range(start_key, end_key + 1), 
                                min(num_samples, end_key - start_key + 1))
        
        # Processamento paralelo
        num_processes = min(mp.cpu_count(), 12)
        batch_size = len(all_keys) // num_processes
        
        key_batches = [all_keys[i:i + batch_size] 
                      for i in range(0, len(all_keys), batch_size)]
        
        print(f"🔄 Processamento: {num_processes} processos paralelos")
        print(f"📦 Batches: {len(key_batches)}")
        
        all_inputs = []
        all_outputs = []
        
        start_time = time.time()
        
        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            futures = [executor.submit(generate_sample_batch_cuda, batch) 
                      for batch in key_batches]
            
            for i, future in enumerate(as_completed(futures)):
                try:
                    inputs, outputs, keys = future.result()
                    all_inputs.extend(inputs)
                    all_outputs.extend(outputs)
                    
                    print(f"   Batch {i+1}/{len(key_batches)}: {len(inputs)} amostras")
                    
                except Exception as e:
                    print(f"   ❌ Erro batch {i+1}: {e}")
        
        generation_time = time.time() - start_time
        
        print(f"\n📊 DATASET GERADO:")
        print(f"✅ Amostras: {len(all_inputs):,}")
        print(f"⏱️  Tempo: {generation_time:.1f}s")
        print(f"⚡ Velocidade: {len(all_inputs) / generation_time:.0f} amostras/s")
        
        if len(all_inputs) == 0:
            return None, None
        
        return np.array(all_inputs, dtype=np.float32), np.array(all_outputs, dtype=np.float32)
    
    def train_cuda_model(self, X, y, epochs=2000, batch_size=512):
        """Treinamento CUDA ultra-otimizado"""
        print(f"\n🚀 TREINAMENTO CUDA ULTRA-RÁPIDO")
        print("=" * 40)
        
        # Criar modelo
        self.model = CudaNeuralNetwork(
            layers=[40, 512, 1024, 2048, 1024, 512, 40],
            use_gpu=self.use_gpu
        )
        
        # Configuração otimizada
        initial_lr = 0.002
        l2_reg = 0.0001
        
        # Estatísticas
        num_batches = len(X) // batch_size
        total_samples = len(X)
        
        print(f"\n📊 CONFIGURAÇÃO CUDA:")
        print(f"   Amostras: {total_samples:,}")
        print(f"   Batch size: {batch_size}")
        print(f"   Batches/época: {num_batches}")
        print(f"   Épocas: {epochs}")
        print(f"   Learning rate: {initial_lr}")
        
        # Mover dados para GPU se disponível
        if self.use_gpu:
            print("🔄 Movendo dados para GPU...")
            X_gpu = cp.asarray(X)
            y_gpu = cp.asarray(y)
            print("✅ Dados na GPU")
        else:
            X_gpu, y_gpu = X, y
        
        # Treinamento
        best_loss = float('inf')
        patience = 150
        patience_counter = 0
        
        print(f"\n🚀 INICIANDO TREINAMENTO CUDA...")
        train_start = time.time()
        
        for epoch in range(epochs):
            # Learning rate schedule agressivo
            if epoch < 300:
                lr = initial_lr
            elif epoch < 800:
                lr = initial_lr * 0.7
            elif epoch < 1500:
                lr = initial_lr * 0.5
            else:
                lr = initial_lr * 0.3
            
            # Embaralhar dados
            if self.use_gpu:
                indices = cp.random.permutation(len(X_gpu))
                X_shuffled = X_gpu[indices]
                y_shuffled = y_gpu[indices]
            else:
                indices = np.random.permutation(len(X))
                X_shuffled = X[indices]
                y_shuffled = y[indices]
            
            epoch_loss = 0.0
            epoch_start = time.time()
            
            # Treinar por batches
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = start_idx + batch_size
                
                X_batch = X_shuffled[start_idx:end_idx]
                y_batch = y_shuffled[start_idx:end_idx]
                
                batch_loss = self.model.train_cuda_batch(X_batch, y_batch, lr, l2_reg)
                epoch_loss += batch_loss
            
            epoch_loss /= num_batches
            epoch_time = time.time() - epoch_start
            
            self.training_history.append(epoch_loss)
            
            # Early stopping
            if epoch_loss < best_loss:
                best_loss = epoch_loss
                patience_counter = 0
            else:
                patience_counter += 1
            
            if patience_counter >= patience:
                print(f"\n⏰ Early stopping na época {epoch+1}")
                break
            
            # Log progresso otimizado
            if (epoch + 1) % 50 == 0:
                samples_per_sec = (num_batches * batch_size) / epoch_time
                
                print(f"Época {epoch+1:4d}/{epochs} | "
                      f"Loss: {epoch_loss:.6f} | "
                      f"Tempo: {epoch_time:.2f}s | "
                      f"Velocidade: {samples_per_sec:.0f} samples/s | "
                      f"LR: {lr:.6f}")
        
        total_train_time = time.time() - train_start
        
        print(f"\n🎊 TREINAMENTO CUDA CONCLUÍDO!")
        print(f"⏱️  Tempo total: {total_train_time:.1f} segundos")
        print(f"📈 Melhor loss: {best_loss:.6f}")
        
        return self.training_history
    
    def evaluate_cuda_model(self, X_test, y_test):
        """Avaliação otimizada em CUDA"""
        print(f"\n📊 AVALIAÇÃO CUDA")
        print("=" * 20)
        
        # Predições em batch para eficiência
        predictions = self.model.predict_cuda(X_test)
        
        # Calcular métricas
        mse = np.mean((y_test - predictions) ** 2)
        mae = np.mean(np.abs(y_test - predictions))
        
        print(f"MSE: {mse:.6f}")
        print(f"MAE: {mae:.6f}")
        
        # Acurácia por caractere
        accuracies = []
        
        print(f"\n🔍 AMOSTRAS DE TESTE (primeiras 5):")
        print("-" * 80)
        
        for i in range(min(5, len(predictions))):
            hash_real = self.vector_to_hex(y_test[i])
            hash_pred = self.vector_to_hex(predictions[i])
            
            correct_chars = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            accuracies.append(accuracy)
            
            print(f"Amostra {i+1}:")
            print(f"  Real:     {hash_real}")
            print(f"  Predito:  {hash_pred}")
            print(f"  Acurácia: {accuracy:.1f}% ({correct_chars}/40)")
            print()
        
        # Acurácia geral
        all_accuracies = []
        for i in range(len(predictions)):
            hash_real = self.vector_to_hex(y_test[i])
            hash_pred = self.vector_to_hex(predictions[i])
            correct_chars = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            all_accuracies.append(accuracy)
        
        avg_accuracy = np.mean(all_accuracies)
        print(f"📈 ACURÁCIA MÉDIA CUDA: {avg_accuracy:.1f}%")
        
        return avg_accuracy
    
    def run_cuda_training(self, num_samples=50000):
        """Pipeline completo CUDA"""
        print("🚀 REDE NEURAL CUDA - HASH PREDICTOR")
        print("=" * 45)
        
        total_start = time.time()
        
        # 1. Gerar dataset
        X, y = self.generate_cuda_dataset(num_samples=num_samples)
        
        if X is None:
            print("❌ Falha na geração do dataset")
            return 0
        
        # 2. Dividir dados
        test_size = min(2000, len(X) // 10)
        
        indices = np.random.permutation(len(X))
        X_test = X[indices[:test_size]]
        y_test = y[indices[:test_size]]
        X_train = X[indices[test_size:]]
        y_train = y[indices[test_size:]]
        
        print(f"\n📊 DIVISÃO CUDA:")
        print(f"Treino: {len(X_train):,} amostras")
        print(f"Teste: {len(X_test):,} amostras")
        
        # 3. Treinar
        self.train_cuda_model(X_train, y_train)
        
        # 4. Avaliar
        accuracy = self.evaluate_cuda_model(X_test, y_test)
        
        # 5. Salvar
        self.save_cuda_model()
        
        total_time = time.time() - total_start
        
        print(f"\n🎊 PIPELINE CUDA CONCLUÍDO!")
        print(f"⏱️  Tempo total: {total_time:.1f} segundos")
        print(f"📈 Acurácia final: {accuracy:.1f}%")
        print(f"🚀 Speedup estimado: {10 if self.use_gpu else 1}x vs CPU")
        
        return accuracy
    
    def save_cuda_model(self):
        """Salva modelo CUDA"""
        # Converter pesos para CPU para salvar
        weights_cpu = []
        biases_cpu = []
        
        for w, b in zip(self.model.weights, self.model.biases):
            if self.use_gpu:
                weights_cpu.append(cp.asnumpy(w).tolist())
                biases_cpu.append(cp.asnumpy(b).tolist())
            else:
                weights_cpu.append(w.tolist())
                biases_cpu.append(b.tolist())
        
        model_data = {
            'layers': self.model.layers,
            'weights': weights_cpu,
            'biases': biases_cpu,
            'training_history': self.training_history,
            'use_gpu': self.use_gpu
        }
        
        with open('cuda_neural_hash_model.json', 'w') as f:
            json.dump(model_data, f)
        
        print(f"💾 Modelo CUDA salvo em 'cuda_neural_hash_model.json'")
    
    def predict_magic_number_cuda(self, hash160_correto_hex):
        """Predição CUDA otimizada"""
        if self.model is None:
            print("❌ Modelo não treinado!")
            return None
        
        input_vector = hex_to_vector_cuda(hash160_correto_hex).reshape(1, -1)
        prediction = self.model.predict_cuda(input_vector)
        
        return self.vector_to_hex(prediction[0])

def main():
    """Função principal CUDA"""
    print("🚀 REDE NEURAL CUDA - HASH PREDICTOR ULTRA-RÁPIDO")
    print("=" * 60)
    
    # Verificar CUDA
    if GPU_AVAILABLE:
        print(f"✅ CUDA disponível")
        try:
            device = cp.cuda.Device()
            try:
                gpu_name = device.name
            except AttributeError:
                try:
                    gpu_name = cp.cuda.runtime.getDeviceProperties(device.id)['name'].decode()
                except:
                    gpu_name = f"GPU {device.id}"

            print(f"🔧 GPU: {gpu_name}")

            try:
                mem_info = device.mem_info
                print(f"💾 Memória: {mem_info[1] / 1024**3:.1f} GB")
            except:
                print(f"💾 Memória: Informação não disponível")
        except Exception as e:
            print(f"⚠️  Erro ao obter info da GPU: {e}")
    else:
        print("⚠️  CUDA não disponível - usando CPU")
        print("💡 Para GPU: pip install cupy-cuda12x")
    
    # Configuração
    num_samples = int(input("Número de amostras (recomendado: 50000): ") or "50000")
    
    # Executar treinamento CUDA
    predictor = CudaHashPredictor(use_gpu=GPU_AVAILABLE)
    accuracy = predictor.run_cuda_training(num_samples=num_samples)
    
    if accuracy > 0:
        # Teste interativo
        print(f"\n🧪 TESTE INTERATIVO CUDA:")
        while True:
            hash_input = input("\nDigite hash160 correto (40 chars) ou 'quit': ").strip()
            
            if hash_input.lower() == 'quit':
                break
            
            if len(hash_input) != 40:
                print("❌ Hash deve ter 40 caracteres")
                continue
            
            magic_number = predictor.predict_magic_number_cuda(hash_input)
            print(f"Número mágico CUDA: {magic_number}")
            print(f"🚀 Comando: ./buscar_chave_por_hash_errado {magic_number} 1:1000000")

if __name__ == "__main__":
    main()
