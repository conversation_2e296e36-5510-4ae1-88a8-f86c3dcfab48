#!/usr/bin/env python3
"""
REDE NEURAL PYTORCH + CUDA PARA HASH PREDICTOR
Versão híbrida com PyTorch para máxima performance e facilidade
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, TensorDataset
import numpy as np
import time
import json
import random
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed

# Importar funções necessárias
try:
    from test_magic_number import simular_gpu_errada_para_chave
    from main import private_key_to_address
    from bitcoin_conversions import calculate_target_hash160
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    exit(1)

# Configurar PyTorch para usar CUDA se disponível
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"🔧 Dispositivo PyTorch: {device}")

if torch.cuda.is_available():
    print(f"🚀 GPU: {torch.cuda.get_device_name()}")
    print(f"💾 Memória GPU: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

def generate_sample_batch_pytorch(key_batch):
    """Gera batch de amostras para PyTorch"""
    inputs = []
    outputs = []
    successful_keys = []
    
    for private_key in key_batch:
        try:
            endereco = private_key_to_address(private_key)
            if not endereco:
                continue
            
            hash160_correto = calculate_target_hash160(endereco)
            if not hash160_correto:
                continue
            
            numero_magico = simular_gpu_errada_para_chave(private_key)
            if not numero_magico:
                continue
            
            input_vector = hex_to_vector_pytorch(hash160_correto.hex())
            output_vector = hex_to_vector_pytorch(numero_magico.hex())
            
            inputs.append(input_vector)
            outputs.append(output_vector)
            successful_keys.append(private_key)
            
        except Exception:
            continue
    
    return inputs, outputs, successful_keys

def hex_to_vector_pytorch(hex_string):
    """Converte hex para vetor PyTorch"""
    hex_string = hex_string.ljust(40, '0')[:40]
    
    vector = np.zeros(40, dtype=np.float32)
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            vector[i] = int(char, 16) / 15.0
        else:
            vector[i] = 0.0
    
    return vector

class PyTorchHashNet(nn.Module):
    """Rede Neural PyTorch otimizada para CUDA"""
    
    def __init__(self, input_size=40, hidden_sizes=[512, 1024, 2048, 1024, 512], output_size=40, dropout=0.3):
        super(PyTorchHashNet, self).__init__()
        
        layers = []
        prev_size = input_size
        
        # Camadas ocultas com BatchNorm e Dropout
        for i, hidden_size in enumerate(hidden_sizes):
            layers.extend([
                nn.Linear(prev_size, hidden_size),
                nn.BatchNorm1d(hidden_size),
                nn.SiLU(),  # Swish activation
                nn.Dropout(dropout)
            ])
            prev_size = hidden_size
        
        # Camada de saída
        layers.extend([
            nn.Linear(prev_size, output_size),
            nn.Sigmoid()
        ])
        
        self.network = nn.Sequential(*layers)
        
        # Inicialização Xavier
        self.apply(self._init_weights)
        
        # Contar parâmetros
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        print(f"🧠 PyTorch Hash Net: {input_size} → {' → '.join(map(str, hidden_sizes))} → {output_size}")
        print(f"📊 Parâmetros: {total_params:,} (treináveis: {trainable_params:,})")
    
    def _init_weights(self, module):
        """Inicialização Xavier/Glorot"""
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                nn.init.zeros_(module.bias)
    
    def forward(self, x):
        return self.network(x)

class PyTorchHashPredictor:
    """Preditor PyTorch + CUDA"""
    
    def __init__(self):
        self.model = None
        self.device = device
        self.training_history = {'train_loss': [], 'val_loss': []}
        
        print(f"🚀 PyTorch Hash Predictor")
        print(f"🔧 Dispositivo: {self.device}")
    
    def vector_to_hex(self, vector):
        """Converte tensor/array para hex"""
        if torch.is_tensor(vector):
            vector = vector.cpu().numpy()
        
        hex_chars = '0123456789abcdef'
        hex_string = ''
        
        for val in vector:
            idx = int(round(float(val) * 15))
            idx = max(0, min(15, idx))
            hex_string += hex_chars[idx]
        
        return hex_string
    
    def generate_pytorch_dataset(self, num_samples=50000, start_key=1, end_key=1000000):
        """Gera dataset para PyTorch"""
        print(f"🚀 GERANDO DATASET PYTORCH: {num_samples:,} AMOSTRAS")
        print("=" * 55)
        
        # Chaves aleatórias
        all_keys = random.sample(range(start_key, end_key + 1), 
                                min(num_samples, end_key - start_key + 1))
        
        # Processamento paralelo
        num_processes = min(mp.cpu_count(), 12)
        batch_size = len(all_keys) // num_processes
        
        key_batches = [all_keys[i:i + batch_size] 
                      for i in range(0, len(all_keys), batch_size)]
        
        print(f"🔄 Processamento: {num_processes} processos")
        print(f"📦 Batches: {len(key_batches)}")
        
        all_inputs = []
        all_outputs = []
        
        start_time = time.time()
        
        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            futures = [executor.submit(generate_sample_batch_pytorch, batch) 
                      for batch in key_batches]
            
            for i, future in enumerate(as_completed(futures)):
                try:
                    inputs, outputs, keys = future.result()
                    all_inputs.extend(inputs)
                    all_outputs.extend(outputs)
                    
                    print(f"   Batch {i+1}/{len(key_batches)}: {len(inputs)} amostras")
                    
                except Exception as e:
                    print(f"   ❌ Erro batch {i+1}: {e}")
        
        generation_time = time.time() - start_time
        
        print(f"\n📊 DATASET PYTORCH:")
        print(f"✅ Amostras: {len(all_inputs):,}")
        print(f"⏱️  Tempo: {generation_time:.1f}s")
        print(f"⚡ Velocidade: {len(all_inputs) / generation_time:.0f} amostras/s")
        
        if len(all_inputs) == 0:
            return None, None
        
        # Converter para tensores PyTorch
        X = torch.FloatTensor(np.array(all_inputs))
        y = torch.FloatTensor(np.array(all_outputs))
        
        return X, y
    
    def create_data_loaders(self, X, y, batch_size=512, val_split=0.1):
        """Cria DataLoaders PyTorch"""
        # Dividir dados
        dataset_size = len(X)
        val_size = int(dataset_size * val_split)
        train_size = dataset_size - val_size
        
        # Embaralhar índices
        indices = torch.randperm(dataset_size)
        train_indices = indices[:train_size]
        val_indices = indices[train_size:]
        
        # Criar datasets
        train_dataset = TensorDataset(X[train_indices], y[train_indices])
        val_dataset = TensorDataset(X[val_indices], y[val_indices])
        
        # Criar data loaders
        train_loader = DataLoader(
            train_dataset, 
            batch_size=batch_size, 
            shuffle=True, 
            num_workers=4 if torch.cuda.is_available() else 2,
            pin_memory=torch.cuda.is_available()
        )
        
        val_loader = DataLoader(
            val_dataset, 
            batch_size=batch_size, 
            shuffle=False, 
            num_workers=4 if torch.cuda.is_available() else 2,
            pin_memory=torch.cuda.is_available()
        )
        
        print(f"📊 DataLoaders criados:")
        print(f"   Treino: {len(train_dataset):,} amostras")
        print(f"   Validação: {len(val_dataset):,} amostras")
        print(f"   Batch size: {batch_size}")
        
        return train_loader, val_loader
    
    def train_pytorch_model(self, train_loader, val_loader, epochs=1000):
        """Treinamento PyTorch otimizado"""
        print(f"\n🚀 TREINAMENTO PYTORCH + CUDA")
        print("=" * 35)
        
        # Criar modelo
        self.model = PyTorchHashNet().to(self.device)
        
        # Otimizador e loss
        optimizer = optim.AdamW(
            self.model.parameters(), 
            lr=0.001, 
            weight_decay=1e-4,
            betas=(0.9, 0.999)
        )
        
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, 
            mode='min', 
            factor=0.7, 
            patience=50, 
            min_lr=1e-6,
            verbose=True
        )
        
        criterion = nn.MSELoss()
        
        # Configuração
        best_val_loss = float('inf')
        patience = 100
        patience_counter = 0
        
        print(f"📊 Configuração:")
        print(f"   Épocas: {epochs}")
        print(f"   Otimizador: AdamW")
        print(f"   Learning rate: 0.001")
        print(f"   Scheduler: ReduceLROnPlateau")
        print(f"   Early stopping: {patience} épocas")
        
        # Treinamento
        print(f"\n🚀 INICIANDO TREINAMENTO...")
        train_start = time.time()
        
        for epoch in range(epochs):
            # Treino
            self.model.train()
            train_loss = 0.0
            train_batches = 0
            
            for batch_X, batch_y in train_loader:
                batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                optimizer.step()
                
                train_loss += loss.item()
                train_batches += 1
            
            train_loss /= train_batches
            
            # Validação
            self.model.eval()
            val_loss = 0.0
            val_batches = 0
            
            with torch.no_grad():
                for batch_X, batch_y in val_loader:
                    batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)
                    outputs = self.model(batch_X)
                    loss = criterion(outputs, batch_y)
                    
                    val_loss += loss.item()
                    val_batches += 1
            
            val_loss /= val_batches
            
            # Salvar histórico
            self.training_history['train_loss'].append(train_loss)
            self.training_history['val_loss'].append(val_loss)
            
            # Scheduler
            scheduler.step(val_loss)
            
            # Early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # Salvar melhor modelo
                torch.save(self.model.state_dict(), 'best_pytorch_hash_model.pth')
            else:
                patience_counter += 1
            
            # Log progresso
            if (epoch + 1) % 25 == 0:
                current_lr = optimizer.param_groups[0]['lr']
                print(f"Época {epoch+1:4d}/{epochs} | "
                      f"Train: {train_loss:.6f} | "
                      f"Val: {val_loss:.6f} | "
                      f"LR: {current_lr:.2e} | "
                      f"Best: {best_val_loss:.6f}")
            
            # Early stopping
            if patience_counter >= patience:
                print(f"\n⏰ Early stopping na época {epoch+1}")
                break
        
        # Carregar melhor modelo
        self.model.load_state_dict(torch.load('best_pytorch_hash_model.pth'))
        
        total_train_time = time.time() - train_start
        
        print(f"\n🎊 TREINAMENTO PYTORCH CONCLUÍDO!")
        print(f"⏱️  Tempo total: {total_train_time:.1f} segundos")
        print(f"📈 Melhor val loss: {best_val_loss:.6f}")
        
        return self.training_history
    
    def evaluate_pytorch_model(self, val_loader):
        """Avaliação PyTorch"""
        print(f"\n📊 AVALIAÇÃO PYTORCH")
        print("=" * 25)
        
        self.model.eval()
        
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch_X, batch_y in val_loader:
                batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)
                outputs = self.model(batch_X)
                
                all_predictions.append(outputs.cpu())
                all_targets.append(batch_y.cpu())
        
        predictions = torch.cat(all_predictions, dim=0)
        targets = torch.cat(all_targets, dim=0)
        
        # Métricas
        mse = torch.mean((targets - predictions) ** 2).item()
        mae = torch.mean(torch.abs(targets - predictions)).item()
        
        print(f"MSE: {mse:.6f}")
        print(f"MAE: {mae:.6f}")
        
        # Acurácia por caractere (primeiras 5 amostras)
        print(f"\n🔍 AMOSTRAS DE TESTE:")
        print("-" * 80)
        
        accuracies = []
        
        for i in range(min(5, len(predictions))):
            hash_real = self.vector_to_hex(targets[i])
            hash_pred = self.vector_to_hex(predictions[i])
            
            correct_chars = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            accuracies.append(accuracy)
            
            print(f"Amostra {i+1}:")
            print(f"  Real:     {hash_real}")
            print(f"  Predito:  {hash_pred}")
            print(f"  Acurácia: {accuracy:.1f}% ({correct_chars}/40)")
            print()
        
        # Acurácia geral
        all_accuracies = []
        for i in range(len(predictions)):
            hash_real = self.vector_to_hex(targets[i])
            hash_pred = self.vector_to_hex(predictions[i])
            correct_chars = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            all_accuracies.append(accuracy)
        
        avg_accuracy = np.mean(all_accuracies)
        print(f"📈 ACURÁCIA MÉDIA PYTORCH: {avg_accuracy:.1f}%")
        
        return avg_accuracy
    
    def run_pytorch_training(self, num_samples=50000, batch_size=512):
        """Pipeline completo PyTorch"""
        print("🚀 PYTORCH + CUDA HASH PREDICTOR")
        print("=" * 40)
        
        total_start = time.time()
        
        # 1. Gerar dataset
        X, y = self.generate_pytorch_dataset(num_samples=num_samples)
        
        if X is None:
            print("❌ Falha na geração do dataset")
            return 0
        
        # 2. Criar data loaders
        train_loader, val_loader = self.create_data_loaders(X, y, batch_size=batch_size)
        
        # 3. Treinar
        self.train_pytorch_model(train_loader, val_loader)
        
        # 4. Avaliar
        accuracy = self.evaluate_pytorch_model(val_loader)
        
        # 5. Salvar
        self.save_pytorch_model()
        
        total_time = time.time() - total_start
        
        print(f"\n🎊 PIPELINE PYTORCH CONCLUÍDO!")
        print(f"⏱️  Tempo total: {total_time:.1f} segundos")
        print(f"📈 Acurácia final: {accuracy:.1f}%")
        
        return accuracy
    
    def save_pytorch_model(self):
        """Salva modelo PyTorch"""
        # Salvar modelo completo
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'model_architecture': {
                'input_size': 40,
                'hidden_sizes': [512, 1024, 2048, 1024, 512],
                'output_size': 40
            },
            'training_history': self.training_history,
            'device': str(self.device)
        }, 'pytorch_hash_model_complete.pth')
        
        print(f"💾 Modelo PyTorch salvo em 'pytorch_hash_model_complete.pth'")
    
    def predict_magic_number_pytorch(self, hash160_correto_hex):
        """Predição PyTorch"""
        if self.model is None:
            print("❌ Modelo não treinado!")
            return None
        
        self.model.eval()
        
        input_vector = hex_to_vector_pytorch(hash160_correto_hex)
        input_tensor = torch.FloatTensor(input_vector).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            prediction = self.model(input_tensor).cpu().numpy()[0]
        
        return self.vector_to_hex(prediction)

def main():
    """Função principal PyTorch + CUDA"""
    print("🚀 PYTORCH + CUDA HASH PREDICTOR")
    print("=" * 40)
    
    # Verificar PyTorch e CUDA
    print(f"PyTorch versão: {torch.__version__}")
    print(f"CUDA disponível: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"Memória: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # Configuração
    num_samples = int(input("Número de amostras (recomendado: 50000): ") or "50000")
    batch_size = int(input("Batch size (recomendado: 512): ") or "512")
    
    # Executar treinamento
    predictor = PyTorchHashPredictor()
    accuracy = predictor.run_pytorch_training(num_samples=num_samples, batch_size=batch_size)
    
    if accuracy > 0:
        # Teste interativo
        print(f"\n🧪 TESTE INTERATIVO PYTORCH:")
        while True:
            hash_input = input("\nDigite hash160 correto (40 chars) ou 'quit': ").strip()
            
            if hash_input.lower() == 'quit':
                break
            
            if len(hash_input) != 40:
                print("❌ Hash deve ter 40 caracteres")
                continue
            
            magic_number = predictor.predict_magic_number_pytorch(hash_input)
            print(f"Número mágico PyTorch: {magic_number}")
            print(f"🚀 Comando: ./buscar_chave_por_hash_errado {magic_number} 1:1000000")

if __name__ == "__main__":
    main()
