#!/usr/bin/env python3
"""
DESCOBRIR PADRÃO REAL - Análise dos Números Mágicos Esperados

Este programa analisa os números mágicos esperados (hardcoded) para descobrir
qual é o padrão matemático real que os gera.
"""

from main import calculate_target_hash160

def analisar_numero_magico_esperado(chave_privada, endereco, numero_magico_esperado):
    """Analisa um número mágico esperado para descobrir como foi gerado"""
    print(f"\n🔍 ANÁLISE DO NÚMERO MÁGICO ESPERADO - CHAVE {chave_privada}")
    print(f"   Endereço: {endereco}")
    
    # Dados conhecidos
    hash160_correto = calculate_target_hash160(endereco)
    numero_magico_bytes = bytes.fromhex(numero_magico_esperado)
    
    print(f"   Hash160 correto:      {hash160_correto.hex()}")
    print(f"   Número mágico esperado: {numero_magico_esperado}")
    
    # Analisar transformação byte por byte
    print(f"\n   ANÁLISE BYTE POR BYTE:")
    
    transformacoes_encontradas = []
    
    for i in range(20):
        byte_correto = hash160_correto[i]
        byte_magico = numero_magico_bytes[i]
        
        print(f"   Byte {i:2d}: {byte_correto:02x} → {byte_magico:02x}", end="")
        
        # Testar operações simples
        operacoes_validas = []
        
        # XOR simples
        xor_result = byte_correto ^ byte_magico
        if xor_result != 0:
            operacoes_validas.append(f"XOR {xor_result:02x}")
        
        # Diferença
        diff = (byte_magico - byte_correto) & 0xFF
        if diff != 0:
            operacoes_validas.append(f"ADD {diff:02x}")
        
        # XOR com chave privada
        xor_chave = byte_correto ^ chave_privada
        if (xor_chave & 0xFF) == byte_magico:
            operacoes_validas.append(f"XOR chave")
        
        # XOR com posição
        xor_pos = byte_correto ^ i
        if (xor_pos & 0xFF) == byte_magico:
            operacoes_validas.append(f"XOR pos")
        
        # Operações complexas baseadas no kernel
        # Teste 1: Operações do kernel CUDA
        resultado_kernel = byte_correto
        resultado_kernel ^= (chave_privada >> (i % 8)) & 0xFF
        resultado_kernel ^= (chave_privada >> ((i + 8) % 16)) & 0xFF
        resultado_kernel ^= (chave_privada * (i + 1)) & 0xFF
        resultado_kernel ^= ((chave_privada + i) * 0x9E3779B9) & 0xFF
        resultado_kernel = ((resultado_kernel * 0x9E) ^ (resultado_kernel >> 4)) & 0xFF
        
        if resultado_kernel == byte_magico:
            operacoes_validas.append(f"KERNEL TRANSFORM")
        
        # Teste 2: Transformação baseada apenas na chave privada (sem hash160)
        resultado_puro = 0
        resultado_puro ^= (chave_privada >> (i % 8)) & 0xFF
        resultado_puro ^= (chave_privada >> ((i + 8) % 16)) & 0xFF
        resultado_puro ^= (chave_privada * (i + 1)) & 0xFF
        resultado_puro ^= ((chave_privada + i) * 0x9E3779B9) & 0xFF
        resultado_puro = ((resultado_puro * 0x9E) ^ (resultado_puro >> 4)) & 0xFF
        
        if resultado_puro == byte_magico:
            operacoes_validas.append(f"PURE KERNEL")
        
        # Teste 3: Simulação da chave pública
        # Simular coordenadas do ponto baseado na chave privada
        gx = [0x79BE667EF9DCBBAC, 0x55A06295CE870B07, 0x029BFCDB2DCE28D9, 0x59F2815B16F81798]
        
        # Transformação da coordenada X baseada na chave privada
        coord_index = i // 8  # Qual coordenada (0-3)
        byte_index = i % 8    # Qual byte da coordenada
        
        if coord_index < 4:
            px_i = gx[coord_index]
            px_i ^= (chave_privada << (coord_index * 8)) | (chave_privada >> (56 - coord_index * 8))
            px_i = ((px_i << 1) ^ (px_i >> 63)) & 0xFFFFFFFFFFFFFFFF
            
            # Extrair o byte específico
            pubkey_byte = (px_i >> (56 - byte_index * 8)) & 0xFF
            
            # Aplicar transformação do kernel
            resultado_pubkey = 0
            for j in range(33):  # Simular 33 bytes da chave pública
                if j == i:
                    resultado_pubkey ^= (pubkey_byte + i + j) & 0xFF
                else:
                    # Simular outros bytes da chave pública
                    other_byte = (chave_privada + j) & 0xFF
                    resultado_pubkey ^= (other_byte + i + j) & 0xFF
            
            # Adicionar dependência da chave privada
            resultado_pubkey ^= (chave_privada >> (i % 8)) & 0xFF
            resultado_pubkey ^= (chave_privada >> ((i + 8) % 16)) & 0xFF
            resultado_pubkey ^= (chave_privada * (i + 1)) & 0xFF
            resultado_pubkey ^= ((chave_privada + i) * 0x9E3779B9) & 0xFF
            
            # Transformação final
            resultado_pubkey = ((resultado_pubkey * 0x9E) ^ (resultado_pubkey >> 4)) & 0xFF
            
            if resultado_pubkey == byte_magico:
                operacoes_validas.append(f"PUBKEY SIM")
        
        if operacoes_validas:
            print(f" | {', '.join(operacoes_validas)}")
        else:
            print(f" | NO PATTERN")
        
        transformacoes_encontradas.append({
            'posicao': i,
            'byte_correto': byte_correto,
            'byte_magico': byte_magico,
            'operacoes': operacoes_validas
        })
    
    return transformacoes_encontradas

def comparar_padroes_entre_chaves(todas_transformacoes):
    """Compara padrões entre diferentes chaves para encontrar consistência"""
    print(f"\n" + "="*80)
    print(f"🔄 COMPARAÇÃO DE PADRÕES ENTRE CHAVES")
    print(f"="*80)
    
    # Analisar cada posição
    for pos in range(20):
        print(f"\n📍 POSIÇÃO {pos}:")
        
        operacoes_por_chave = {}
        
        for chave_data in todas_transformacoes:
            chave = chave_data['chave_privada']
            transformacao = chave_data['transformacoes'][pos]
            operacoes_por_chave[chave] = transformacao['operacoes']
            
            print(f"   Chave {chave}: {transformacao['byte_correto']:02x} → {transformacao['byte_magico']:02x} | {', '.join(transformacao['operacoes']) if transformacao['operacoes'] else 'NO PATTERN'}")
        
        # Procurar padrões comuns
        operacoes_comuns = set(operacoes_por_chave[1])
        for chave in [2, 3]:
            if chave in operacoes_por_chave:
                operacoes_comuns &= set(operacoes_por_chave[chave])
        
        if operacoes_comuns:
            print(f"   🎯 PADRÕES COMUNS: {', '.join(operacoes_comuns)}")
        else:
            print(f"   ❌ Nenhum padrão comum encontrado")

def criar_formula_correta_baseada_na_analise(todas_transformacoes):
    """Cria a fórmula correta baseada na análise dos números mágicos esperados"""
    print(f"\n" + "="*80)
    print(f"🎯 CRIANDO FÓRMULA CORRETA")
    print(f"="*80)
    
    # Contar quantas vezes cada tipo de operação aparece
    contadores = {
        'KERNEL TRANSFORM': 0,
        'PURE KERNEL': 0,
        'PUBKEY SIM': 0,
        'XOR chave': 0,
        'XOR pos': 0
    }
    
    total_posicoes = 0
    
    for chave_data in todas_transformacoes:
        for transformacao in chave_data['transformacoes']:
            total_posicoes += 1
            for operacao in transformacao['operacoes']:
                if operacao in contadores:
                    contadores[operacao] += 1
    
    print(f"\n📊 ESTATÍSTICAS DAS OPERAÇÕES:")
    for operacao, count in contadores.items():
        porcentagem = (count / total_posicoes) * 100 if total_posicoes > 0 else 0
        print(f"   {operacao}: {count}/{total_posicoes} ({porcentagem:.1f}%)")
    
    # Identificar a operação mais comum
    operacao_mais_comum = max(contadores, key=contadores.get)
    print(f"\n🏆 OPERAÇÃO MAIS COMUM: {operacao_mais_comum}")
    
    if contadores[operacao_mais_comum] >= total_posicoes * 0.5:  # Se aparece em pelo menos 50% dos casos
        print(f"✅ PADRÃO IDENTIFICADO: {operacao_mais_comum}")
        return operacao_mais_comum
    else:
        print(f"❌ Nenhum padrão dominante encontrado")
        return None

def main():
    """Função principal"""
    print("🔬 DESCOBRIR PADRÃO REAL - ANÁLISE DOS NÚMEROS MÁGICOS ESPERADOS")
    print("Este programa analisa os números mágicos esperados para descobrir o padrão real")
    
    # Dados das carteiras conhecidas (números mágicos esperados)
    carteiras = [
        (1, "1BgGZ9tcN4rm9KBzDn7KprQz87SZ26SAMH", "c24c028f0ad79d963195436c0ee23a27a37c3985"),
        (2, "1cMh228HTCiwS8ZsaakH8A8wze1JR5ZsP", "12a8012c9fa6320d3028858625af236cc4c63eea"),
        (3, "1CUNEBjYrCn2y1SdiUMohaKUi4wpP326Lb", "fc07a82b75be9bf8a6e5a4fb9ba730a4757567a0")
    ]
    
    # Analisar cada carteira
    todas_transformacoes = []
    
    for chave_privada, endereco, numero_magico_esperado in carteiras:
        transformacoes = analisar_numero_magico_esperado(chave_privada, endereco, numero_magico_esperado)
        todas_transformacoes.append({
            'chave_privada': chave_privada,
            'transformacoes': transformacoes
        })
    
    # Comparar padrões entre chaves
    comparar_padroes_entre_chaves(todas_transformacoes)
    
    # Criar fórmula correta
    padrao_identificado = criar_formula_correta_baseada_na_analise(todas_transformacoes)
    
    print(f"\n" + "="*80)
    print(f"📋 CONCLUSÃO")
    print(f"="*80)
    
    if padrao_identificado:
        print(f"🎉 PADRÃO DESCOBERTO: {padrao_identificado}")
        print(f"   Agora posso implementar a fórmula correta!")
    else:
        print(f"⚠️  Padrão não identificado claramente.")
        print(f"   Pode ser necessária análise manual mais detalhada.")

if __name__ == "__main__":
    main()
