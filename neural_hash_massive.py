#!/usr/bin/env python3
"""
REDE NEURAL MASSIVA - TREINAMENTO COM 100K+ AMOSTRAS
Versão otimizada para grandes volumes de dados usando test_magic_number.py
"""

import numpy as np
import json
import time
import random
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import sys

# Importar funções necessárias
try:
    from test_magic_number import simular_gpu_errada_para_chave
    from main import private_key_to_address
    from bitcoin_conversions import calculate_target_hash160
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    sys.exit(1)

def generate_sample_batch(key_batch):
    """
    Gera um lote de amostras de treinamento (para processamento paralelo)
    
    Args:
        key_batch: Lista de chaves privadas para processar
        
    Returns:
        tuple: (inputs, outputs, successful_keys)
    """
    inputs = []
    outputs = []
    successful_keys = []
    
    for private_key in key_batch:
        try:
            # 1. <PERSON><PERSON><PERSON> endereço
            endereco = private_key_to_address(private_key)
            if not endereco:
                continue
            
            # 2. Hash160 correto (INPUT)
            hash160_correto = calculate_target_hash160(endereco)
            if not hash160_correto:
                continue
            
            # 3. Número mágico (OUTPUT)
            numero_magico = simular_gpu_errada_para_chave(private_key)
            if not numero_magico:
                continue
            
            # 4. Converter para vetores normalizados
            input_vector = hex_to_vector(hash160_correto.hex())
            output_vector = hex_to_vector(numero_magico.hex())
            
            inputs.append(input_vector)
            outputs.append(output_vector)
            successful_keys.append(private_key)
            
        except Exception:
            continue
    
    return inputs, outputs, successful_keys

def hex_to_vector(hex_string):
    """Converte hex para vetor normalizado (função global para multiprocessing)"""
    hex_string = hex_string.ljust(40, '0')[:40]
    
    vector = []
    for char in hex_string:
        if char in '0123456789abcdef':
            vector.append(int(char, 16) / 15.0)
        else:
            vector.append(0.0)
    
    return np.array(vector, dtype=np.float32)

class MassiveNeuralNetwork:
    """Rede neural otimizada para grandes volumes"""
    
    def __init__(self, layers=[40, 512, 1024, 2048, 1024, 512, 40]):
        """Arquitetura mais profunda para capturar padrões complexos"""
        self.layers = layers
        self.weights = []
        self.biases = []
        
        # Inicialização He para ReLU
        for i in range(len(layers) - 1):
            fan_in = layers[i]
            
            # He initialization
            std = np.sqrt(2.0 / fan_in)
            w = np.random.normal(0, std, (fan_in, layers[i+1])).astype(np.float32)
            b = np.zeros((1, layers[i+1]), dtype=np.float32)
            
            self.weights.append(w)
            self.biases.append(b)
        
        print(f"🧠 Rede Neural Massiva: {' → '.join(map(str, layers))}")
        total_params = sum(w.size for w in self.weights) + sum(b.size for b in self.biases)
        print(f"📊 Total de parâmetros: {total_params:,}")
    
    def swish(self, x):
        """Função de ativação Swish (x * sigmoid(x))"""
        return x * (1 / (1 + np.exp(-np.clip(x, -500, 500))))
    
    def swish_derivative(self, x):
        """Derivada da função Swish"""
        sigmoid_x = 1 / (1 + np.exp(-np.clip(x, -500, 500)))
        return sigmoid_x + x * sigmoid_x * (1 - sigmoid_x)
    
    def forward(self, X):
        """Forward pass otimizado"""
        activations = [X]
        z_values = []
        
        for i, (w, b) in enumerate(zip(self.weights, self.biases)):
            z = np.dot(activations[-1], w) + b
            z_values.append(z)
            
            # Swish nas camadas ocultas, sigmoid na saída
            if i < len(self.weights) - 1:
                a = self.swish(z)
            else:
                a = 1 / (1 + np.exp(-np.clip(z, -500, 500)))  # Sigmoid
            
            activations.append(a)
        
        return activations, z_values
    
    def train_batch(self, X_batch, y_batch, learning_rate, l2_reg):
        """Treina um batch de dados"""
        # Forward pass
        activations, z_values = self.forward(X_batch)
        
        # Backward pass
        m = X_batch.shape[0]
        output_error = activations[-1] - y_batch
        
        # Calcular gradientes
        gradients_w = []
        gradients_b = []
        
        errors = [output_error]
        
        # Backpropagation
        for i in range(len(self.weights) - 1, 0, -1):
            error = np.dot(errors[-1], self.weights[i].T)
            
            # Derivada da Swish
            error *= self.swish_derivative(z_values[i-1])
            errors.append(error)
        
        errors.reverse()
        
        # Calcular gradientes
        for i in range(len(self.weights)):
            dw = np.dot(activations[i].T, errors[i]) / m
            db = np.mean(errors[i], axis=0, keepdims=True)
            
            # Regularização L2
            dw += l2_reg * self.weights[i]
            
            gradients_w.append(dw)
            gradients_b.append(db)
        
        # Atualizar pesos
        for i in range(len(self.weights)):
            self.weights[i] -= learning_rate * gradients_w[i]
            self.biases[i] -= learning_rate * gradients_b[i]
        
        # Calcular loss
        mse_loss = np.mean(output_error ** 2)
        l2_loss = sum(np.sum(w ** 2) for w in self.weights) * l2_reg
        
        return mse_loss + l2_loss

class MassiveHashPredictor:
    """Preditor com treinamento massivo"""
    
    def __init__(self):
        self.model = None
        self.training_history = []
    
    def vector_to_hex(self, vector):
        """Converte vetor para hex"""
        hex_chars = '0123456789abcdef'
        hex_string = ''
        
        for val in vector:
            idx = int(round(val * 15))
            idx = max(0, min(15, idx))
            hex_string += hex_chars[idx]
        
        return hex_string
    
    def generate_massive_dataset(self, num_samples=100000, start_key=1, end_key=1000000):
        """
        Gera dataset massivo usando processamento paralelo
        
        Args:
            num_samples: Número de amostras
            start_key: Chave inicial
            end_key: Chave final
        """
        print(f"🚀 GERANDO DATASET MASSIVO: {num_samples:,} AMOSTRAS")
        print("=" * 60)
        
        # Gerar chaves aleatórias
        all_keys = random.sample(range(start_key, end_key + 1), 
                                min(num_samples, end_key - start_key + 1))
        
        # Dividir em batches para processamento paralelo
        num_processes = min(mp.cpu_count(), 8)  # Máximo 8 processos
        batch_size = len(all_keys) // num_processes
        
        key_batches = []
        for i in range(0, len(all_keys), batch_size):
            key_batches.append(all_keys[i:i + batch_size])
        
        print(f"🔄 Processamento paralelo: {num_processes} processos")
        print(f"📦 Batches: {len(key_batches)} (tamanho ~{batch_size})")
        
        # Processamento paralelo
        all_inputs = []
        all_outputs = []
        all_successful_keys = []
        
        start_time = time.time()
        
        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            # Submeter jobs
            futures = [executor.submit(generate_sample_batch, batch) for batch in key_batches]
            
            # Coletar resultados
            for i, future in enumerate(as_completed(futures)):
                try:
                    inputs, outputs, keys = future.result()
                    
                    all_inputs.extend(inputs)
                    all_outputs.extend(outputs)
                    all_successful_keys.extend(keys)
                    
                    print(f"   Batch {i+1}/{len(key_batches)} concluído: {len(inputs)} amostras")
                    
                except Exception as e:
                    print(f"   ❌ Erro no batch {i+1}: {e}")
        
        generation_time = time.time() - start_time
        
        print(f"\n📊 ESTATÍSTICAS DE GERAÇÃO:")
        print(f"✅ Amostras geradas: {len(all_inputs):,}")
        print(f"⏱️  Tempo de geração: {generation_time:.1f} segundos")
        print(f"⚡ Velocidade: {len(all_inputs) / generation_time:.1f} amostras/segundo")
        
        if len(all_inputs) == 0:
            print("❌ Nenhuma amostra foi gerada!")
            return None, None, None
        
        return np.array(all_inputs), np.array(all_outputs), all_successful_keys
    
    def train_massive_model(self, X, y, epochs=3000, batch_size=256):
        """Treina modelo com dados massivos"""
        print(f"\n🚀 TREINAMENTO MASSIVO")
        print("=" * 25)
        
        # Criar modelo
        self.model = MassiveNeuralNetwork([40, 512, 1024, 2048, 1024, 512, 40])
        
        # Parâmetros de treinamento
        initial_lr = 0.001
        l2_reg = 0.0001
        
        # Dividir em batches
        num_batches = len(X) // batch_size
        
        print(f"📊 Configuração:")
        print(f"   Amostras: {len(X):,}")
        print(f"   Batch size: {batch_size}")
        print(f"   Batches por época: {num_batches}")
        print(f"   Épocas: {epochs}")
        
        best_loss = float('inf')
        patience = 200
        patience_counter = 0
        
        for epoch in range(epochs):
            # Learning rate schedule
            if epoch < 500:
                lr = initial_lr
            elif epoch < 1000:
                lr = initial_lr * 0.7
            elif epoch < 2000:
                lr = initial_lr * 0.5
            else:
                lr = initial_lr * 0.3
            
            # Embaralhar dados
            indices = np.random.permutation(len(X))
            X_shuffled = X[indices]
            y_shuffled = y[indices]
            
            epoch_loss = 0.0
            
            # Treinar por batches
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = start_idx + batch_size
                
                X_batch = X_shuffled[start_idx:end_idx]
                y_batch = y_shuffled[start_idx:end_idx]
                
                batch_loss = self.model.train_batch(X_batch, y_batch, lr, l2_reg)
                epoch_loss += batch_loss
            
            epoch_loss /= num_batches
            self.training_history.append(epoch_loss)
            
            # Early stopping
            if epoch_loss < best_loss:
                best_loss = epoch_loss
                patience_counter = 0
            else:
                patience_counter += 1
            
            if patience_counter >= patience:
                print(f"\n⏰ Early stopping na época {epoch+1}")
                break
            
            # Log progresso
            if (epoch + 1) % 100 == 0:
                print(f"Época {epoch+1:4d}/{epochs} | "
                      f"Loss: {epoch_loss:.6f} | "
                      f"LR: {lr:.6f} | "
                      f"Best: {best_loss:.6f}")
        
        return self.training_history
    
    def run_massive_training(self, num_samples=100000):
        """Pipeline completo de treinamento massivo"""
        print("🧠 REDE NEURAL MASSIVA - HASH PREDICTOR")
        print("=" * 50)
        
        total_start = time.time()
        
        # 1. Gerar dataset massivo
        X, y, keys = self.generate_massive_dataset(num_samples=num_samples)
        
        if X is None:
            return 0
        
        # 2. Dividir treino/teste
        test_size = min(1000, len(X) // 10)  # Máximo 1000 amostras de teste
        
        indices = np.random.permutation(len(X))
        test_indices = indices[:test_size]
        train_indices = indices[test_size:]
        
        X_train = X[train_indices]
        y_train = y[train_indices]
        X_test = X[test_indices]
        y_test = y[test_indices]
        
        print(f"\n📊 DIVISÃO DOS DADOS:")
        print(f"Treino: {len(X_train):,} amostras")
        print(f"Teste: {len(X_test):,} amostras")
        
        # 3. Treinar modelo
        self.train_massive_model(X_train, y_train)
        
        # 4. Avaliar
        print(f"\n📊 AVALIAÇÃO FINAL")
        print("=" * 20)
        
        predictions = self.model.forward(X_test)[0][-1]
        
        # Calcular acurácia
        accuracies = []
        for i in range(len(predictions)):
            hash_real = self.vector_to_hex(y_test[i])
            hash_pred = self.vector_to_hex(predictions[i])
            correct_chars = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            accuracies.append(accuracy)
        
        avg_accuracy = np.mean(accuracies)
        
        # 5. Salvar modelo
        self.save_massive_model()
        
        total_time = time.time() - total_start
        
        print(f"\n🎊 TREINAMENTO MASSIVO CONCLUÍDO!")
        print(f"⏱️  Tempo total: {total_time:.1f} segundos")
        print(f"📈 Acurácia final: {avg_accuracy:.1f}%")
        print(f"📊 Amostras de treinamento: {len(X_train):,}")
        
        return avg_accuracy
    
    def save_massive_model(self):
        """Salva modelo massivo"""
        model_data = {
            'layers': self.model.layers,
            'weights': [w.tolist() for w in self.model.weights],
            'biases': [b.tolist() for b in self.model.biases],
            'training_history': self.training_history
        }
        
        with open('massive_neural_hash_model.json', 'w') as f:
            json.dump(model_data, f)
        
        print(f"💾 Modelo massivo salvo em 'massive_neural_hash_model.json'")

def main():
    """Função principal"""
    print("🚀 REDE NEURAL MASSIVA - TREINAMENTO COM 100K+ AMOSTRAS")
    print("=" * 65)
    
    # Configuração
    print(f"⚙️  CONFIGURAÇÃO:")
    print(f"CPUs disponíveis: {mp.cpu_count()}")
    
    num_samples = int(input("Número de amostras (recomendado: 100000): ") or "100000")
    
    # Executar treinamento massivo
    predictor = MassiveHashPredictor()
    accuracy = predictor.run_massive_training(num_samples=num_samples)
    
    print(f"\n🎯 RESULTADO FINAL: {accuracy:.1f}% de acurácia")

if __name__ == "__main__":
    main()
