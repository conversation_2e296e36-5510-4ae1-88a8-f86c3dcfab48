#!/usr/bin/env python3
"""
Programa para encontrar o hash160 ERRADO específico que a GPU deve procurar
para o endereço **********************************

Estratégia:
1. Testar chaves no range de busca do programa (0x400000000000000000 a 0x7fffffffffffffffff)
2. Para cada chave, calcular o hash160 ERRADO que a GPU geraria
3. Verificar na CPU se essa chave gera o endereço alvo
4. Se sim, encontramos o hash160 errado que a GPU deve procurar!
"""

import os
import sys
import hashlib
import time

# Importar nossos módulos
from bitcoin_conversions import (
    private_key_to_address, private_key_to_hash160, 
    calculate_target_hash160, private_key_to_public_key
)

# Endereço alvo Bitcoin
TARGET_ADDRESS = "**********************************"

# Range de busca (igual ao programa principal)
START_KEY = 0x400000000000000000
END_KEY = 0x7fffffffffffffffff

def clear_screen():
    """Limpa a tela do console"""
    if os.name == 'nt':  # Windows
        os.system('cls')
    else:  # Linux/Mac
        os.system('clear')

def ripemd160_manual(data):
    """Implementação manual do RIPEMD160 (igual à da GPU)"""
    def rol(n, b):
        return ((n << b) | (n >> (32 - b))) & 0xffffffff

    def f(j, x, y, z):
        if j < 16:
            return x ^ y ^ z
        elif j < 32:
            return (x & y) | (~x & z)
        elif j < 48:
            return (x | ~y) ^ z
        elif j < 64:
            return (x & z) | (y & ~z)
        else:
            return x ^ (y | ~z)

    def K(j):
        if j < 16:
            return 0x00000000
        elif j < 32:
            return 0x5A827999
        elif j < 48:
            return 0x6ED9EBA1
        elif j < 64:
            return 0x8F1BBCDC
        else:
            return 0xA953FD4E

    def Kh(j):
        if j < 16:
            return 0x50A28BE6
        elif j < 32:
            return 0x5C4DD124
        elif j < 48:
            return 0x6D703EF3
        elif j < 64:
            return 0x7A6D76E9
        else:
            return 0x00000000

    # Padding
    msg = bytearray(data)
    msg_len = len(data)
    msg.append(0x80)

    while len(msg) % 64 != 56:
        msg.append(0x00)

    msg.extend((msg_len * 8).to_bytes(8, 'little'))

    # Initialize hash values
    h0 = 0x67452301
    h1 = 0xEFCDAB89
    h2 = 0x98BADCFE
    h3 = 0x10325476
    h4 = 0xC3D2E1F0

    # Process message in 512-bit chunks
    for chunk_start in range(0, len(msg), 64):
        chunk = msg[chunk_start:chunk_start + 64]
        w = [int.from_bytes(chunk[i:i+4], 'little') for i in range(0, 64, 4)]

        # Initialize hash value for this chunk
        al, bl, cl, dl, el = h0, h1, h2, h3, h4
        ar, br, cr, dr, er = h0, h1, h2, h3, h4

        # Left line
        r = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
             7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8,
             3, 10, 14, 4, 9, 15, 8, 1, 2, 7, 0, 6, 13, 11, 5, 12,
             1, 9, 11, 10, 0, 8, 12, 4, 13, 3, 7, 15, 14, 5, 6, 2,
             4, 0, 5, 9, 7, 12, 2, 10, 14, 1, 3, 8, 11, 6, 15, 13]

        s = [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8,
             7, 6, 8, 13, 11, 9, 7, 15, 7, 12, 15, 9, 11, 7, 13, 12,
             11, 13, 6, 7, 14, 9, 13, 15, 14, 8, 13, 6, 5, 12, 7, 5,
             11, 12, 14, 15, 14, 15, 9, 8, 9, 14, 5, 6, 8, 6, 5, 12,
             9, 15, 5, 11, 6, 8, 13, 12, 5, 12, 13, 14, 11, 8, 5, 6]

        # Right line
        rh = [5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12,
              6, 11, 3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2,
              15, 5, 1, 3, 7, 14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13,
              8, 6, 4, 1, 3, 11, 15, 0, 5, 12, 2, 13, 9, 7, 10, 14,
              12, 15, 10, 4, 1, 5, 8, 7, 6, 2, 13, 14, 0, 3, 9, 11]

        sh = [8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6,
              9, 13, 15, 7, 12, 8, 9, 11, 7, 7, 12, 7, 6, 15, 13, 11,
              9, 7, 15, 11, 8, 6, 6, 14, 12, 13, 5, 14, 13, 13, 7, 5,
              15, 5, 8, 11, 14, 14, 6, 14, 6, 9, 12, 9, 12, 5, 15, 8,
              8, 5, 12, 9, 12, 5, 14, 6, 8, 13, 6, 5, 15, 13, 11, 11]

        for j in range(80):
            # Left line
            t = (al + f(j, bl, cl, dl) + w[r[j]] + K(j)) & 0xffffffff
            t = rol(t, s[j]) + el & 0xffffffff
            al, bl, cl, dl, el = el, t, bl, rol(cl, 10), dl

            # Right line
            t = (ar + f(79-j, br, cr, dr) + w[rh[j]] + Kh(j)) & 0xffffffff
            t = rol(t, sh[j]) + er & 0xffffffff
            ar, br, cr, dr, er = er, t, br, rol(cr, 10), dr

        # Add this chunk's hash to result so far
        t = (h1 + cl + dr) & 0xffffffff
        h1 = (h2 + dl + er) & 0xffffffff
        h2 = (h3 + el + ar) & 0xffffffff
        h3 = (h4 + al + br) & 0xffffffff
        h4 = (h0 + bl + cr) & 0xffffffff
        h0 = t

    # Produce the final hash value
    return b''.join(h.to_bytes(4, 'little') for h in [h0, h1, h2, h3, h4])

def calculate_gpu_wrong_hash160(private_key_int):
    """Calcula o hash160 ERRADO que a GPU geraria (replica exatamente a lógica da GPU)"""
    try:
        # Coordenadas do ponto gerador secp256k1 (do kernel CUDA)
        gx = [0x79BE667EF9DCBBAC, 0x55A06295CE870B07, 0x029BFCDB2DCE28D9, 0x59F2815B16F81798]
        
        # Aplicar transformação baseada na chave privada (INCORRETA - igual à GPU)
        px = []
        for i in range(4):
            px_i = gx[i]
            # Operações que dependem da chave privada (INCORRETAS)
            px_i ^= (private_key_int << (i * 8)) | (private_key_int >> (56 - i * 8))
            px_i = ((px_i << 1) ^ (px_i >> 63)) & 0xFFFFFFFFFFFFFFFF
            px.append(px_i)
        
        # Determinar paridade Y (simplificado e INCORRETO)
        y_parity = 2 + ((private_key_int ^ px[0]) & 1)
        
        # Construir chave pública INCORRETA
        public_key = bytes([y_parity])
        
        # Converter coordenada X para bytes (big-endian)
        for i in range(4):
            for j in range(8):
                byte_val = (px[i] >> (56 - j * 8)) & 0xFF
                public_key += bytes([byte_val])

        # SHA256 da chave pública (INCORRETA)
        sha256_result = hashlib.sha256(public_key).digest()
        
        # RIPEMD160 do SHA256
        hash160_result = ripemd160_manual(sha256_result)
        
        return hash160_result

    except Exception as e:
        print(f"Erro ao calcular hash160 errado da GPU: {e}")
        return None

def buscar_hash160_errado_especifico():
    """
    Busca no range do programa para encontrar qual hash160 errado a GPU deve procurar
    """
    print("=== BUSCANDO HASH160 ERRADO ESPECÍFICO ===")
    print(f"Endereço alvo: {TARGET_ADDRESS}")
    print(f"Range de busca: {START_KEY:064x} a {END_KEY:064x}")
    print("Procurando chave que gera o endereço alvo na CPU...")
    
    start_time = time.time()
    tested_keys = 0
    
    # Buscar em pequenos incrementos para não travar o programa
    current_key = START_KEY
    batch_size = 100000  # Testar 100k chaves por vez
    
    while current_key <= END_KEY:
        # Testar batch de chaves
        for key in range(current_key, min(current_key + batch_size, END_KEY + 1)):
            tested_keys += 1
            
            # Verificar se esta chave gera o endereço alvo na CPU
            try:
                address_cpu = private_key_to_address(key)
                
                if address_cpu == TARGET_ADDRESS:
                    # ENCONTROU! Esta é a chave real
                    print(f"\n🎯 CHAVE REAL ENCONTRADA!")
                    print(f"Chave privada: {key:064x}")
                    print(f"Endereço CPU: {address_cpu}")
                    
                    # Calcular o hash160 errado que a GPU geraria para esta chave
                    hash160_cpu_correct = private_key_to_hash160(key)
                    hash160_gpu_wrong = calculate_gpu_wrong_hash160(key)
                    
                    print(f"\nHash160 CPU (correto): {hash160_cpu_correct.hex()}")
                    print(f"Hash160 GPU (errado):  {hash160_gpu_wrong.hex()}")
                    
                    return key, hash160_gpu_wrong
                    
            except Exception as e:
                # Ignorar erros e continuar
                pass
            
            # Mostrar progresso a cada 10k chaves
            if tested_keys % 10000 == 0:
                elapsed = time.time() - start_time
                keys_per_sec = tested_keys / elapsed if elapsed > 0 else 0
                print(f"Testadas: {tested_keys:,} chaves | {keys_per_sec:.0f} chaves/s | Chave atual: {key:064x}")
        
        current_key += batch_size
        
        # Parar após um tempo razoável para demonstração
        if time.time() - start_time > 60:  # 1 minuto
            print(f"\n⏰ Tempo limite atingido. Testadas {tested_keys:,} chaves.")
            break
    
    print(f"\n❌ Chave não encontrada no range testado ({tested_keys:,} chaves)")
    return None, None

def main():
    """Função principal"""
    clear_screen()
    print("=" * 80)
    print("BUSCA DO HASH160 ERRADO ESPECÍFICO DA GPU")
    print("Endereço alvo: **********************************")
    print("=" * 80)
    
    # Mostrar hash160 correto
    target_hash160_correct = calculate_target_hash160(TARGET_ADDRESS)
    print(f"\nHash160 CORRETO (CPU): {target_hash160_correct.hex()}")
    
    # Buscar a chave real e o hash160 errado correspondente
    real_key, wrong_hash160 = buscar_hash160_errado_especifico()
    
    if real_key and wrong_hash160:
        print(f"\n" + "=" * 80)
        print("✅ SUCESSO! HASH160 ERRADO ENCONTRADO!")
        print("=" * 80)
        
        print(f"\n🎯 CONFIGURAÇÃO PARA A GPU:")
        print(f"TARGET_HASH160 = bytes.fromhex('{wrong_hash160.hex()}')")
        
        print(f"\n🔧 ARRAY PARA CÓDIGO C/CUDA:")
        hash_array = ", ".join([f"0x{b:02x}" for b in wrong_hash160])
        print(f"uint8_t target_hash160[20] = {{{hash_array}}};")
        
        print(f"\n⚡ ESTRATÉGIA FINAL:")
        print(f"1. GPU procura pelo hash160 ERRADO: {wrong_hash160.hex()}")
        print(f"2. Quando encontrar a chave {real_key:064x}, verificar na CPU")
        print(f"3. CPU confirmará que gera o endereço: {TARGET_ADDRESS}")
        print(f"4. SUCESSO! Chave privada encontrada!")
        
    else:
        print(f"\n" + "=" * 80)
        print("❌ CHAVE NÃO ENCONTRADA NO RANGE TESTADO")
        print("=" * 80)
        
        print(f"\n💡 POSSÍVEIS SOLUÇÕES:")
        print(f"1. Expandir o range de busca")
        print(f"2. Usar informações adicionais sobre a chave")
        print(f"3. Executar busca mais longa")
        print(f"4. Verificar se o endereço está no range correto")
        
        print(f"\n📝 PARA TESTE, use o hash160 correto:")
        print(f"TARGET_HASH160 = bytes.fromhex('{target_hash160_correct.hex()}')")
        print(f"(A GPU procurará pelo hash160 correto e você verificará na CPU)")

if __name__ == "__main__":
    main()
