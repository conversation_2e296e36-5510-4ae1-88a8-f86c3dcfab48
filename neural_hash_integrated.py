#!/usr/bin/env python3
"""
REDE NEURAL INTEGRADA COM test_magic_number.py
Gera dados de treinamento reais usando a função simular_gpu_errada_para_chave
"""

import numpy as np
import json
import time
import random
import sys
import os

# Importar funções do test_magic_number.py e main.py
try:
    from test_magic_number import simular_gpu_errada_para_chave
    from main import private_key_to_address
    from bitcoin_conversions import calculate_target_hash160
    print("✅ Módulos importados com sucesso")
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    print("💡 Verifique se os arquivos test_magic_number.py, main.py e bitcoin_conversions.py estão no mesmo diretório")
    sys.exit(1)

class IntegratedNeuralNetwork:
    """Rede neural integrada com geração de dados reais"""
    
    def __init__(self, layers=[40, 256, 512, 1024, 512, 256, 40]):
        """Inicializa rede neural com arquitetura mais robusta"""
        self.layers = layers
        self.weights = []
        self.biases = []
        
        # Inicialização Xavier melhorada
        for i in range(len(layers) - 1):
            fan_in = layers[i]
            fan_out = layers[i+1]
            
            # Xavier/Glorot initialization
            limit = np.sqrt(6.0 / (fan_in + fan_out))
            w = np.random.uniform(-limit, limit, (fan_in, fan_out))
            b = np.zeros((1, fan_out))
            
            self.weights.append(w)
            self.biases.append(b)
        
        print(f"🧠 Rede Neural Integrada: {' → '.join(map(str, layers))}")
        total_params = sum(w.size for w in self.weights) + sum(b.size for b in self.biases)
        print(f"📊 Total de parâmetros: {total_params:,}")
    
    def leaky_relu(self, x, alpha=0.01):
        """Leaky ReLU para evitar neurônios mortos"""
        return np.where(x > 0, x, alpha * x)
    
    def leaky_relu_derivative(self, x, alpha=0.01):
        """Derivada do Leaky ReLU"""
        return np.where(x > 0, 1, alpha)
    
    def sigmoid(self, x):
        """Sigmoid com clipping para estabilidade"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def forward(self, X):
        """Forward pass com Leaky ReLU"""
        activations = [X]
        z_values = []
        
        for i, (w, b) in enumerate(zip(self.weights, self.biases)):
            z = np.dot(activations[-1], w) + b
            z_values.append(z)
            
            # Leaky ReLU nas camadas ocultas, sigmoid na saída
            if i < len(self.weights) - 1:
                a = self.leaky_relu(z)
            else:
                a = self.sigmoid(z)
            
            activations.append(a)
        
        return activations, z_values
    
    def backward(self, X, y, activations, z_values, learning_rate=0.001, l2_reg=0.0001):
        """Backward pass com regularização L2"""
        m = X.shape[0]
        
        # Erro da saída
        output_error = activations[-1] - y
        
        # Lista de erros
        errors = [output_error]
        
        # Backpropagation
        for i in range(len(self.weights) - 1, 0, -1):
            error = np.dot(errors[-1], self.weights[i].T)
            
            # Aplicar derivada da Leaky ReLU
            error *= self.leaky_relu_derivative(z_values[i-1])
            errors.append(error)
        
        errors.reverse()
        
        # Atualizar pesos com regularização L2
        for i in range(len(self.weights)):
            dw = np.dot(activations[i].T, errors[i]) / m
            db = np.mean(errors[i], axis=0, keepdims=True)
            
            # Adicionar regularização L2
            dw += l2_reg * self.weights[i]
            
            self.weights[i] -= learning_rate * dw
            self.biases[i] -= learning_rate * db
    
    def train_with_schedule(self, X, y, epochs=5000, initial_lr=0.01, verbose=True):
        """Treinamento com learning rate schedule"""
        losses = []
        best_loss = float('inf')
        patience = 500
        patience_counter = 0
        
        for epoch in range(epochs):
            # Learning rate schedule
            if epoch < 1000:
                lr = initial_lr
            elif epoch < 2000:
                lr = initial_lr * 0.5
            elif epoch < 3000:
                lr = initial_lr * 0.2
            else:
                lr = initial_lr * 0.1
            
            # Regularização adaptativa
            l2_reg = 0.0001 * (1 + epoch / 1000)
            
            # Forward pass
            activations, z_values = self.forward(X)
            
            # Calcular loss com regularização
            mse_loss = np.mean((activations[-1] - y) ** 2)
            l2_loss = sum(np.sum(w ** 2) for w in self.weights) * l2_reg
            total_loss = mse_loss + l2_loss
            
            losses.append(total_loss)
            
            # Backward pass
            self.backward(X, y, activations, z_values, lr, l2_reg)
            
            # Early stopping
            if total_loss < best_loss:
                best_loss = total_loss
                patience_counter = 0
            else:
                patience_counter += 1
            
            if patience_counter >= patience:
                print(f"\n⏰ Early stopping na época {epoch+1}")
                break
            
            # Log progresso
            if verbose and (epoch + 1) % 200 == 0:
                print(f"Época {epoch+1:4d}/{epochs} | "
                      f"Loss: {total_loss:.6f} | "
                      f"MSE: {mse_loss:.6f} | "
                      f"L2: {l2_loss:.6f} | "
                      f"LR: {lr:.6f}")
        
        return losses
    
    def predict(self, X):
        """Predição"""
        activations, _ = self.forward(X)
        return activations[-1]

class IntegratedHashPredictor:
    """Preditor integrado com geração de dados reais"""
    
    def __init__(self):
        self.model = None
        self.training_stats = {}
    
    def hex_to_vector(self, hex_string):
        """Converte hex para vetor normalizado"""
        hex_string = hex_string.ljust(40, '0')[:40]
        
        vector = []
        for char in hex_string:
            if char in '0123456789abcdef':
                vector.append(int(char, 16) / 15.0)
            else:
                vector.append(0.0)
        
        return np.array(vector)
    
    def vector_to_hex(self, vector):
        """Converte vetor para hex"""
        hex_chars = '0123456789abcdef'
        hex_string = ''
        
        for val in vector:
            idx = int(round(val * 15))
            idx = max(0, min(15, idx))
            hex_string += hex_chars[idx]
        
        return hex_string
    
    def generate_training_data(self, num_samples=10000, start_key=1, end_key=100000):
        """
        Gera dados de treinamento usando test_magic_number.py
        
        Args:
            num_samples: Número de amostras a gerar
            start_key: Chave privada inicial
            end_key: Chave privada final
        """
        print(f"🔄 GERANDO {num_samples:,} AMOSTRAS DE TREINAMENTO")
        print("=" * 55)
        print(f"Range de chaves: {start_key:,} - {end_key:,}")
        
        X = []  # Hash160 correto (input)
        y = []  # Hash160 errado/mágico (output)
        
        successful_samples = 0
        failed_samples = 0
        
        # Gerar chaves aleatórias no range
        keys_to_test = random.sample(range(start_key, end_key + 1), 
                                   min(num_samples, end_key - start_key + 1))
        
        print(f"🔍 Processando {len(keys_to_test):,} chaves privadas...")
        
        for i, private_key in enumerate(keys_to_test):
            try:
                # 1. Gerar endereço Bitcoin real
                endereco = private_key_to_address(private_key)
                if not endereco:
                    failed_samples += 1
                    continue
                
                # 2. Calcular hash160 correto (INPUT da rede neural)
                hash160_correto = calculate_target_hash160(endereco)
                if not hash160_correto:
                    failed_samples += 1
                    continue
                
                # 3. Calcular número mágico usando test_magic_number.py (OUTPUT da rede neural)
                numero_magico = simular_gpu_errada_para_chave(private_key)
                if not numero_magico:
                    failed_samples += 1
                    continue
                
                # 4. Converter para vetores
                input_vector = self.hex_to_vector(hash160_correto.hex())
                output_vector = self.hex_to_vector(numero_magico.hex())
                
                X.append(input_vector)
                y.append(output_vector)
                
                successful_samples += 1
                
                # Progresso
                if (i + 1) % 1000 == 0:
                    print(f"   Processadas: {i + 1:,} | Sucessos: {successful_samples:,} | Falhas: {failed_samples:,}")
                
            except Exception as e:
                failed_samples += 1
                if failed_samples % 100 == 0:
                    print(f"   ⚠️  Falhas acumuladas: {failed_samples}")
                continue
        
        print(f"\n📊 ESTATÍSTICAS DE GERAÇÃO:")
        print(f"✅ Amostras geradas com sucesso: {successful_samples:,}")
        print(f"❌ Falhas: {failed_samples:,}")
        print(f"📈 Taxa de sucesso: {(successful_samples / len(keys_to_test)) * 100:.1f}%")
        
        if successful_samples == 0:
            print("❌ Nenhuma amostra foi gerada com sucesso!")
            return None, None
        
        # Salvar estatísticas
        self.training_stats = {
            'total_samples': successful_samples,
            'failed_samples': failed_samples,
            'key_range': (start_key, end_key),
            'success_rate': (successful_samples / len(keys_to_test)) * 100
        }
        
        return np.array(X), np.array(y)
    
    def add_synthetic_variations(self, X, y, variations_per_sample=5):
        """Adiciona variações sintéticas aos dados reais"""
        print(f"\n🔄 ADICIONANDO VARIAÇÕES SINTÉTICAS")
        print(f"Variações por amostra: {variations_per_sample}")
        
        original_count = len(X)
        X_augmented = list(X)
        y_augmented = list(y)
        
        for i in range(original_count):
            for j in range(variations_per_sample):
                # Ruído muito pequeno para preservar a relação
                noise_level = 0.005  # 0.5% de ruído
                
                noise_x = np.random.normal(0, noise_level, X[i].shape)
                noise_y = np.random.normal(0, noise_level, y[i].shape)
                
                noisy_x = np.clip(X[i] + noise_x, 0, 1)
                noisy_y = np.clip(y[i] + noise_y, 0, 1)
                
                X_augmented.append(noisy_x)
                y_augmented.append(noisy_y)
        
        print(f"Dados originais: {original_count:,}")
        print(f"Dados sintéticos: {len(X_augmented) - original_count:,}")
        print(f"Total: {len(X_augmented):,}")
        
        return np.array(X_augmented), np.array(y_augmented)
    
    def evaluate_model(self, X_test, y_test, test_keys=None):
        """Avalia modelo com dados de teste"""
        print(f"\n📊 AVALIAÇÃO DO MODELO")
        print("=" * 25)
        
        predictions = self.model.predict(X_test)
        
        # MSE geral
        mse = np.mean((y_test - predictions) ** 2)
        mae = np.mean(np.abs(y_test - predictions))
        
        print(f"MSE: {mse:.6f}")
        print(f"MAE: {mae:.6f}")
        
        # Avaliação por amostra (primeiras 10)
        print(f"\n🔍 AMOSTRAS DE TESTE (primeiras 10):")
        print("-" * 80)
        
        accuracies = []
        
        for i in range(min(10, len(X_test))):
            hash_correto_hex = self.vector_to_hex(X_test[i])
            hash_errado_real_hex = self.vector_to_hex(y_test[i])
            hash_errado_pred_hex = self.vector_to_hex(predictions[i])
            
            # Acurácia por caractere
            correct_chars = sum(1 for a, b in zip(hash_errado_real_hex, hash_errado_pred_hex) if a == b)
            accuracy = (correct_chars / 40) * 100
            accuracies.append(accuracy)
            
            key_info = f"Chave {test_keys[i]}" if test_keys and i < len(test_keys) else f"Amostra {i+1}"
            
            print(f"{key_info}:")
            print(f"  Hash correto: {hash_correto_hex}")
            print(f"  Mágico real:  {hash_errado_real_hex}")
            print(f"  Mágico pred:  {hash_errado_pred_hex}")
            print(f"  Acurácia:     {accuracy:.1f}% ({correct_chars}/40)")
            print()
        
        # Acurácia geral
        all_accuracies = []
        for i in range(len(predictions)):
            hash_real = self.vector_to_hex(y_test[i])
            hash_pred = self.vector_to_hex(predictions[i])
            correct_chars = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            all_accuracies.append(accuracy)
        
        avg_accuracy = np.mean(all_accuracies)
        print(f"📈 ACURÁCIA MÉDIA GERAL: {avg_accuracy:.1f}%")
        
        return avg_accuracy
    
    def run_integrated_training(self, num_samples=10000, test_split=0.1):
        """Executa treinamento integrado completo"""
        print("🧠 REDE NEURAL INTEGRADA - HASH PREDICTOR")
        print("=" * 50)
        
        start_time = time.time()
        
        # 1. Gerar dados de treinamento reais
        X, y = self.generate_training_data(num_samples=num_samples)
        
        if X is None:
            print("❌ Falha na geração de dados")
            return 0
        
        # 2. Adicionar variações sintéticas
        X_aug, y_aug = self.add_synthetic_variations(X, y, variations_per_sample=3)
        
        # 3. Dividir treino/teste
        test_size = int(len(X) * test_split)
        
        # Usar dados originais para teste
        X_test = X[:test_size]
        y_test = y[:test_size]
        
        # Usar dados aumentados para treino (excluindo os de teste)
        X_train = X_aug[test_size:]
        y_train = y_aug[test_size:]
        
        print(f"\n📊 DIVISÃO DOS DADOS:")
        print(f"Treino: {len(X_train):,} amostras")
        print(f"Teste: {len(X_test):,} amostras")
        
        # 4. Criar modelo
        self.model = IntegratedNeuralNetwork([40, 256, 512, 1024, 512, 256, 40])
        
        # 5. Treinar
        print(f"\n🚀 INICIANDO TREINAMENTO INTEGRADO")
        print("=" * 40)
        
        losses = self.model.train_with_schedule(X_train, y_train, epochs=5000, initial_lr=0.01)
        
        # 6. Avaliar
        accuracy = self.evaluate_model(X_test, y_test)
        
        # 7. Salvar modelo e estatísticas
        self.save_integrated_model()
        
        total_time = time.time() - start_time
        
        print(f"\n🎊 TREINAMENTO INTEGRADO CONCLUÍDO!")
        print(f"⏱️  Tempo total: {total_time:.1f} segundos")
        print(f"📈 Acurácia final: {accuracy:.1f}%")
        print(f"📊 Amostras de treinamento: {self.training_stats['total_samples']:,}")
        
        return accuracy
    
    def save_integrated_model(self):
        """Salva modelo e estatísticas"""
        # Salvar modelo
        model_data = {
            'layers': self.model.layers,
            'weights': [w.tolist() for w in self.model.weights],
            'biases': [b.tolist() for b in self.model.biases],
            'training_stats': self.training_stats
        }
        
        with open('integrated_neural_hash_model.json', 'w') as f:
            json.dump(model_data, f, indent=2)
        
        print(f"💾 Modelo integrado salvo em 'integrated_neural_hash_model.json'")
    
    def predict_magic_number(self, hash160_correto_hex):
        """Prediz número mágico a partir de hash160 correto"""
        if self.model is None:
            print("❌ Modelo não treinado!")
            return None
        
        input_vector = self.hex_to_vector(hash160_correto_hex).reshape(1, -1)
        prediction = self.model.predict(input_vector)
        
        return self.vector_to_hex(prediction[0])

def main():
    """Função principal"""
    print("🧠 REDE NEURAL INTEGRADA COM test_magic_number.py")
    print("=" * 60)
    
    # Verificar dependências
    try:
        import numpy as np
        print(f"✅ NumPy {np.__version__} disponível")
    except ImportError:
        print("❌ NumPy não encontrado!")
        return
    
    # Configuração do treinamento
    print(f"\n⚙️  CONFIGURAÇÃO DO TREINAMENTO:")
    print("=" * 35)
    
    num_samples = int(input("Número de amostras de treinamento (recomendado: 10000): ") or "10000")
    
    # Criar e treinar
    predictor = IntegratedHashPredictor()
    accuracy = predictor.run_integrated_training(num_samples=num_samples)
    
    if accuracy > 0:
        # Teste interativo
        print(f"\n🧪 TESTE INTERATIVO:")
        while True:
            hash_input = input("\nDigite um hash160 correto (40 chars) ou 'quit': ").strip()
            
            if hash_input.lower() == 'quit':
                break
            
            if len(hash_input) != 40:
                print("❌ Hash deve ter 40 caracteres")
                continue
            
            magic_number = predictor.predict_magic_number(hash_input)
            print(f"Número mágico predito: {magic_number}")
            
            # Comando para usar
            print(f"🚀 Comando: ./buscar_chave_por_hash_errado {magic_number} 1:1000000")

if __name__ == "__main__":
    main()
