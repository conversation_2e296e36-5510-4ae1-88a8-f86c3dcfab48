#!/usr/bin/env python3
"""
REDE NEURAL AVANÇADA - MÁXIMA ACURÁCIA
Técnicas de deep learning modernas para hash prediction
"""

import numpy as np
import time
import json
import random
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed

# Tentar importar CuPy
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("✅ CuPy disponível - GPU habilitada")
except ImportError:
    import numpy as cp
    GPU_AVAILABLE = False
    print("⚠️  CuPy não encontrado - usando CPU")

# Importar funções necessárias
try:
    from test_magic_number import simular_gpu_errada_para_chave
    from main import private_key_to_address
    from bitcoin_conversions import calculate_target_hash160
    print("✅ Módulos Bitcoin importados")
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    exit(1)

def hex_to_features_advanced(hex_string):
    """
    Extrai features avançadas do hash hexadecimal
    Múltiplas representações para capturar diferentes padrões
    """
    hex_string = hex_string.ljust(40, '0')[:40]

    features = []

    # 1. One-hot encoding (640 features)
    one_hot = np.zeros(40 * 16, dtype=np.float32)
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            char_idx = int(char, 16)
            one_hot[i * 16 + char_idx] = 1.0
    features.extend(one_hot)

    # 2. Valores numéricos normalizados (40 features)
    numeric = np.zeros(40, dtype=np.float32)
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            numeric[i] = int(char, 16) / 15.0
    features.extend(numeric)

    # 3. Features de posição (40 features)
    position = np.arange(40, dtype=np.float32) / 39.0
    features.extend(position)

    # 4. Features de padrão (20 features - pares de caracteres)
    pairs = np.zeros(20, dtype=np.float32)
    for i in range(0, 40, 2):
        if i+1 < 40:
            char1 = hex_string[i] if hex_string[i] in '0123456789abcdef' else '0'
            char2 = hex_string[i+1] if hex_string[i+1] in '0123456789abcdef' else '0'
            pair_value = (int(char1, 16) * 16 + int(char2, 16)) / 255.0
            pairs[i//2] = pair_value
    features.extend(pairs)

    # 5. Features estatísticas (10 features)
    values = [int(c, 16) if c in '0123456789abcdef' else 0 for c in hex_string]
    stats = [
        np.mean(values) / 15.0,           # média
        np.std(values) / 15.0,            # desvio padrão
        np.min(values) / 15.0,            # mínimo
        np.max(values) / 15.0,            # máximo
        np.median(values) / 15.0,         # mediana
        len(set(values)) / 16.0,          # diversidade
        sum(1 for v in values if v > 7) / 40.0,  # proporção alta
        sum(1 for v in values if v < 8) / 40.0,  # proporção baixa
        sum(1 for i in range(1, 40) if values[i] > values[i-1]) / 39.0,  # tendência crescente
        sum(1 for i in range(1, 40) if values[i] == values[i-1]) / 39.0   # repetições
    ]
    features.extend(stats)

    return np.array(features, dtype=np.float32)

def features_to_hex_advanced(features):
    """Converte features de volta para hex usando one-hot"""
    # Extrair one-hot (primeiros 640 valores)
    one_hot = features[:640].reshape(40, 16)

    hex_chars = '0123456789abcdef'
    hex_string = ''

    for char_probs in one_hot:
        char_idx = np.argmax(char_probs)
        hex_string += hex_chars[char_idx]

    return hex_string

def generate_sample_batch_advanced(key_batch):
    """Gera batch com features avançadas"""
    inputs = []
    outputs = []
    successful_keys = []

    for private_key in key_batch:
        try:
            endereco = private_key_to_address(private_key)
            if not endereco:
                continue

            hash160_correto = calculate_target_hash160(endereco)
            if not hash160_correto:
                continue

            numero_magico = simular_gpu_errada_para_chave(private_key)
            if not numero_magico:
                continue

            input_features = hex_to_features_advanced(hash160_correto.hex())
            output_features = hex_to_features_advanced(numero_magico.hex())

            inputs.append(input_features)
            outputs.append(output_features)
            successful_keys.append(private_key)

        except Exception:
            continue

    return inputs, outputs, successful_keys

class AdvancedNeuralNetwork:
    """Rede neural avançada com técnicas modernas"""

    def __init__(self, input_size=750, use_gpu=True):
        # Arquitetura mais profunda e especializada
        self.layers = [
            input_size,    # 750 features
            1024,          # Primeira camada densa
            1536,          # Expansão
            2048,          # Camada principal
            1536,          # Contração
            1024,          # Refinamento
            750            # Saída (mesmo formato da entrada)
        ]

        self.use_gpu = use_gpu and GPU_AVAILABLE
        self.xp = cp if self.use_gpu else np

        print(f"🧠 Rede Neural Avançada")
        print(f"🔧 Dispositivo: {'GPU' if self.use_gpu else 'CPU'}")
        print(f"📊 Arquitetura: {' → '.join(map(str, self.layers))}")
        print(f"📊 Features: 640 one-hot + 40 numeric + 40 position + 20 pairs + 10 stats = 750")

        # Inicialização Xavier/Glorot melhorada
        self.weights = []
        self.biases = []
        self.batch_norm_params = []  # Para batch normalization

        for i in range(len(self.layers) - 1):
            fan_in = self.layers[i]
            fan_out = self.layers[i + 1]

            # Xavier initialization
            limit = self.xp.sqrt(6.0 / (fan_in + fan_out))
            w = self.xp.random.uniform(-limit, limit, (fan_in, fan_out)).astype(self.xp.float32)
            b = self.xp.zeros((1, fan_out), dtype=self.xp.float32)

            self.weights.append(w)
            self.biases.append(b)

            # Parâmetros de batch normalization
            if i < len(self.layers) - 2:  # Não na última camada
                gamma = self.xp.ones((1, fan_out), dtype=self.xp.float32)
                beta = self.xp.zeros((1, fan_out), dtype=self.xp.float32)
                self.batch_norm_params.append((gamma, beta))

        total_params = sum(w.size for w in self.weights) + sum(b.size for b in self.biases)
        total_params += sum(g.size + b.size for g, b in self.batch_norm_params)
        print(f"📊 Parâmetros: {total_params:,}")

    def batch_normalize(self, x, gamma, beta, eps=1e-8):
        """Batch normalization"""
        mean = self.xp.mean(x, axis=0, keepdims=True)
        var = self.xp.var(x, axis=0, keepdims=True)
        x_norm = (x - mean) / self.xp.sqrt(var + eps)
        return gamma * x_norm + beta

    def swish(self, x):
        """Swish activation function"""
        return x / (1 + self.xp.exp(-self.xp.clip(x, -10, 10)))

    def swish_derivative(self, x):
        """Derivada da Swish"""
        sigmoid = 1 / (1 + self.xp.exp(-self.xp.clip(x, -10, 10)))
        return sigmoid + x * sigmoid * (1 - sigmoid)

    def forward_advanced(self, X, training=True):
        """Forward pass avançado com batch norm"""
        if self.use_gpu and not isinstance(X, cp.ndarray):
            X = cp.asarray(X)
        elif not self.use_gpu and isinstance(X, cp.ndarray):
            X = cp.asnumpy(X)

        activations = [X]
        z_values = []
        normalized_values = []

        for i, (w, b) in enumerate(zip(self.weights, self.biases)):
            z = self.xp.dot(activations[-1], w) + b
            z_values.append(z)

            # Batch normalization (exceto última camada)
            if i < len(self.weights) - 1 and training:
                gamma, beta = self.batch_norm_params[i]
                z_norm = self.batch_normalize(z, gamma, beta)
                normalized_values.append(z_norm)
                z = z_norm
            else:
                normalized_values.append(z)

            # Ativação
            if i < len(self.weights) - 1:
                a = self.swish(z)
            else:
                # Saída: softmax para one-hot + linear para outras features
                batch_size = z.shape[0]

                # One-hot part (primeiros 640)
                one_hot_part = z[:, :640].reshape(batch_size, 40, 16)
                exp_vals = self.xp.exp(one_hot_part - self.xp.max(one_hot_part, axis=2, keepdims=True))
                softmax_part = exp_vals / self.xp.sum(exp_vals, axis=2, keepdims=True)
                softmax_flat = softmax_part.reshape(batch_size, 640)

                # Outras features (últimos 110): sigmoid
                other_part = 1 / (1 + self.xp.exp(-self.xp.clip(z[:, 640:], -10, 10)))

                a = self.xp.concatenate([softmax_flat, other_part], axis=1)

            activations.append(a)

        return activations, z_values, normalized_values

    def compute_loss_advanced(self, y_pred, y_true):
        """Loss function avançada"""
        batch_size = y_pred.shape[0]

        # One-hot part: cross-entropy
        y_pred_oh = y_pred[:, :640]
        y_true_oh = y_true[:, :640]

        y_pred_oh_clipped = self.xp.clip(y_pred_oh, 1e-15, 1 - 1e-15)
        ce_loss = -self.xp.sum(y_true_oh * self.xp.log(y_pred_oh_clipped)) / batch_size

        # Outras features: MSE
        y_pred_other = y_pred[:, 640:]
        y_true_other = y_true[:, 640:]
        mse_loss = self.xp.mean((y_pred_other - y_true_other) ** 2)

        # Loss combinada
        total_loss = ce_loss + mse_loss

        return total_loss, ce_loss, mse_loss

    def backward_advanced(self, X, y, activations, z_values, normalized_values, learning_rate, l2_reg=0.0001):
        """Backward pass avançado"""
        m = X.shape[0]

        # Erro da saída
        output_error = activations[-1] - y

        # Backpropagation
        errors = [output_error]

        for i in range(len(self.weights) - 1, 0, -1):
            error = self.xp.dot(errors[-1], self.weights[i].T)

            # Derivada da ativação
            if i < len(self.weights) - 1:
                error *= self.swish_derivative(normalized_values[i-1])

            errors.append(error)

        errors.reverse()

        # Atualizar pesos
        for i in range(len(self.weights)):
            dw = self.xp.dot(activations[i].T, errors[i]) / m
            db = self.xp.mean(errors[i], axis=0, keepdims=True)

            # Regularização L2
            dw += l2_reg * self.weights[i]

            # Gradient clipping mais agressivo
            dw = self.xp.clip(dw, -0.5, 0.5)
            db = self.xp.clip(db, -0.5, 0.5)

            self.weights[i] -= learning_rate * dw
            self.biases[i] -= learning_rate * db

        # Atualizar batch norm parameters
        for i in range(len(self.batch_norm_params)):
            if i < len(errors) - 1:
                gamma, beta = self.batch_norm_params[i]

                # Gradientes simplificados para batch norm
                dgamma = self.xp.mean(errors[i], axis=0, keepdims=True) * 0.01
                dbeta = self.xp.mean(errors[i], axis=0, keepdims=True) * 0.01

                gamma -= learning_rate * dgamma
                beta -= learning_rate * dbeta

                self.batch_norm_params[i] = (gamma, beta)

        # Calcular loss
        total_loss, ce_loss, mse_loss = self.compute_loss_advanced(activations[-1], y)
        l2_loss = sum(self.xp.sum(w ** 2) for w in self.weights) * l2_reg

        return float(total_loss + l2_loss), float(ce_loss), float(mse_loss)

    def train_advanced(self, X, y, epochs=2000, initial_lr=0.0005, batch_size=32):
        """Treinamento avançado"""
        print(f"\n🚀 TREINAMENTO AVANÇADO")
        print("=" * 30)

        # Converter dados
        if self.use_gpu:
            if not isinstance(X, cp.ndarray):
                X = cp.asarray(X)
            if not isinstance(y, cp.ndarray):
                y = cp.asarray(y)

        num_batches = len(X) // batch_size
        best_loss = float('inf')
        patience = 400
        patience_counter = 0

        print(f"Configuração avançada:")
        print(f"  Amostras: {len(X):,}")
        print(f"  Batch size: {batch_size}")
        print(f"  Learning rate: {initial_lr}")
        print(f"  Batch normalization: Ativada")

        for epoch in range(epochs):
            # Learning rate schedule
            if epoch < 400:
                lr = initial_lr
            elif epoch < 800:
                lr = initial_lr * 0.7
            elif epoch < 1200:
                lr = initial_lr * 0.5
            elif epoch < 1600:
                lr = initial_lr * 0.3
            else:
                lr = initial_lr * 0.1

            # Regularização adaptativa
            l2_reg = 0.0001 * (1 + epoch / 500)

            # Embaralhar dados
            if self.use_gpu:
                indices = cp.random.permutation(len(X))
            else:
                indices = np.random.permutation(len(X))

            X_shuffled = X[indices]
            y_shuffled = y[indices]

            epoch_total_loss = 0.0
            epoch_ce_loss = 0.0
            epoch_mse_loss = 0.0

            # Treinar por batches
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = start_idx + batch_size

                X_batch = X_shuffled[start_idx:end_idx]
                y_batch = y_shuffled[start_idx:end_idx]

                # Forward pass
                activations, z_values, normalized_values = self.forward_advanced(X_batch, training=True)

                # Backward pass
                total_loss, ce_loss, mse_loss = self.backward_advanced(
                    X_batch, y_batch, activations, z_values, normalized_values, lr, l2_reg
                )

                epoch_total_loss += total_loss
                epoch_ce_loss += ce_loss
                epoch_mse_loss += mse_loss

            epoch_total_loss /= num_batches
            epoch_ce_loss /= num_batches
            epoch_mse_loss /= num_batches

            # Early stopping
            if epoch_total_loss < best_loss:
                best_loss = epoch_total_loss
                patience_counter = 0
            else:
                patience_counter += 1

            if patience_counter >= patience:
                print(f"\n⏰ Early stopping na época {epoch+1}")
                break

            # Log progresso
            if (epoch + 1) % 50 == 0:
                print(f"Época {epoch+1:4d}/{epochs} | "
                      f"Total: {epoch_total_loss:.6f} | "
                      f"CE: {epoch_ce_loss:.6f} | "
                      f"MSE: {epoch_mse_loss:.6f} | "
                      f"LR: {lr:.6f}")

        print(f"\n✅ Treinamento avançado concluído!")
        print(f"📈 Melhor loss: {best_loss:.6f}")

        return best_loss

    def predict_advanced(self, X):
        """Predição avançada"""
        activations, _, _ = self.forward_advanced(X, training=False)
        result = activations[-1]

        if self.use_gpu and isinstance(result, cp.ndarray):
            result = cp.asnumpy(result)

        return result

class AdvancedHashPredictor:
    """Preditor avançado com máxima acurácia"""

    def __init__(self):
        self.model = None
        self.use_gpu = GPU_AVAILABLE

        print(f"🚀 Advanced Hash Predictor")
        print(f"🔧 GPU: {'Habilitada' if self.use_gpu else 'Desabilitada'}")

    def generate_dataset_advanced(self, num_samples=50000, start_key=1, end_key=2000000):
        """Gera dataset avançado com distribuição otimizada"""
        print(f"🚀 GERANDO DATASET AVANÇADO: {num_samples:,} AMOSTRAS")
        print("=" * 60)

        # Distribuição estratégica de chaves
        all_keys = []

        # 40% chaves muito pequenas (1-10000) - padrões mais simples
        small_keys = random.sample(range(1, min(10001, end_key)),
                                  min(int(num_samples * 0.4), 10000))
        all_keys.extend(small_keys)

        # 30% chaves pequenas (10001-100000) - padrões intermediários
        medium_keys = random.sample(range(10001, min(100001, end_key)),
                                   min(int(num_samples * 0.3), 90000))
        all_keys.extend(medium_keys)

        # 20% chaves médias (100001-1000000) - padrões complexos
        large_keys = random.sample(range(100001, min(1000001, end_key)),
                                  min(int(num_samples * 0.2), 900000))
        all_keys.extend(large_keys)

        # 10% chaves grandes (1000001+) - generalização
        remaining = num_samples - len(all_keys)
        if remaining > 0 and end_key > 1000000:
            huge_keys = random.sample(range(1000001, end_key + 1),
                                     min(remaining, end_key - 1000000))
            all_keys.extend(huge_keys)

        # Processamento paralelo otimizado
        num_processes = min(mp.cpu_count(), 12)
        batch_size = len(all_keys) // num_processes

        key_batches = [all_keys[i:i + batch_size]
                      for i in range(0, len(all_keys), batch_size)]

        print(f"🔄 Processamento: {num_processes} processos")
        print(f"📊 Distribuição:")
        print(f"   40% chaves 1-10K (padrões simples)")
        print(f"   30% chaves 10K-100K (padrões médios)")
        print(f"   20% chaves 100K-1M (padrões complexos)")
        print(f"   10% chaves 1M+ (generalização)")

        all_inputs = []
        all_outputs = []

        start_time = time.time()

        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            futures = [executor.submit(generate_sample_batch_advanced, batch)
                      for batch in key_batches]

            for i, future in enumerate(as_completed(futures)):
                try:
                    inputs, outputs, keys = future.result()
                    all_inputs.extend(inputs)
                    all_outputs.extend(outputs)

                    print(f"   Batch {i+1}/{len(key_batches)}: {len(inputs)} amostras")

                except Exception as e:
                    print(f"   ❌ Erro batch {i+1}: {e}")

        generation_time = time.time() - start_time

        print(f"\n📊 DATASET AVANÇADO:")
        print(f"✅ Amostras: {len(all_inputs):,}")
        print(f"⏱️  Tempo: {generation_time:.1f}s")
        print(f"⚡ Velocidade: {len(all_inputs) / generation_time:.0f} amostras/s")
        print(f"📊 Features por amostra: {len(all_inputs[0]) if all_inputs else 0}")

        if len(all_inputs) == 0:
            return None, None

        return np.array(all_inputs, dtype=np.float32), np.array(all_outputs, dtype=np.float32)

    def evaluate_advanced(self, X_test, y_test):
        """Avaliação avançada com métricas detalhadas"""
        print(f"\n📊 AVALIAÇÃO AVANÇADA")
        print("=" * 25)

        predictions = self.model.predict_advanced(X_test)

        print(f"🔍 AMOSTRAS DE TESTE (primeiras 15):")
        print("-" * 90)

        accuracies = []
        perfect_matches = 0
        high_accuracy = 0  # >80%
        medium_accuracy = 0  # 50-80%

        for i in range(min(15, len(predictions))):
            hash_real = features_to_hex_advanced(y_test[i])
            hash_pred = features_to_hex_advanced(predictions[i])

            # Acurácia por caractere
            correct_chars = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            accuracies.append(accuracy)

            if accuracy == 100:
                perfect_matches += 1
            elif accuracy > 80:
                high_accuracy += 1
            elif accuracy > 50:
                medium_accuracy += 1

            print(f"Amostra {i+1:2d}:")
            print(f"  Real:     {hash_real}")
            print(f"  Predito:  {hash_pred}")
            print(f"  Acurácia: {accuracy:5.1f}% ({correct_chars:2d}/40)")

            # Análise de erros
            if accuracy < 100:
                errors = [(j, hash_real[j], hash_pred[j]) for j in range(40)
                         if hash_real[j] != hash_pred[j]]
                if len(errors) <= 8:
                    error_str = ", ".join([f"{pos}:{real}→{pred}" for pos, real, pred in errors])
                    print(f"  Erros:    {error_str}")
                else:
                    print(f"  Erros:    {len(errors)} posições diferentes")
            print()

        # Estatísticas completas
        all_accuracies = []
        all_perfect = 0
        all_high = 0
        all_medium = 0

        for i in range(len(predictions)):
            hash_real = features_to_hex_advanced(y_test[i])
            hash_pred = features_to_hex_advanced(predictions[i])
            correct_chars = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            all_accuracies.append(accuracy)

            if accuracy == 100:
                all_perfect += 1
            elif accuracy > 80:
                all_high += 1
            elif accuracy > 50:
                all_medium += 1

        avg_accuracy = np.mean(all_accuracies)
        median_accuracy = np.median(all_accuracies)
        std_accuracy = np.std(all_accuracies)
        max_accuracy = np.max(all_accuracies)
        min_accuracy = np.min(all_accuracies)

        print(f"📈 ESTATÍSTICAS COMPLETAS:")
        print(f"   Média:           {avg_accuracy:6.1f}%")
        print(f"   Mediana:         {median_accuracy:6.1f}%")
        print(f"   Desvio padrão:   {std_accuracy:6.1f}%")
        print(f"   Máxima:          {max_accuracy:6.1f}%")
        print(f"   Mínima:          {min_accuracy:6.1f}%")
        print()
        print(f"📊 DISTRIBUIÇÃO DE ACURÁCIA:")
        print(f"   Perfeitas (100%):     {all_perfect:4d} ({all_perfect/len(predictions)*100:5.1f}%)")
        print(f"   Altas (80-99%):       {all_high:4d} ({all_high/len(predictions)*100:5.1f}%)")
        print(f"   Médias (50-79%):      {all_medium:4d} ({all_medium/len(predictions)*100:5.1f}%)")
        print(f"   Baixas (<50%):        {len(predictions)-all_perfect-all_high-all_medium:4d} ({(len(predictions)-all_perfect-all_high-all_medium)/len(predictions)*100:5.1f}%)")

        # Análise de features
        print(f"\n🔬 ANÁLISE DE FEATURES:")

        # Comparar features one-hot vs outras
        oh_mse = np.mean((y_test[:, :640] - predictions[:, :640]) ** 2)
        other_mse = np.mean((y_test[:, 640:] - predictions[:, 640:]) ** 2)

        print(f"   MSE One-hot:     {oh_mse:.6f}")
        print(f"   MSE Outras:      {other_mse:.6f}")

        return avg_accuracy

    def run_advanced_training(self, num_samples=50000):
        """Pipeline avançado completo"""
        print("🚀 ADVANCED HASH PREDICTOR - MÁXIMA ACURÁCIA")
        print("=" * 55)

        total_start = time.time()

        # 1. Gerar dataset avançado
        X, y = self.generate_dataset_advanced(num_samples=num_samples)

        if X is None:
            print("❌ Falha na geração do dataset")
            return 0

        # 2. Dividir dados (mais dados para teste para melhor avaliação)
        test_size = min(5000, len(X) // 6)  # ~16% para teste

        indices = np.random.permutation(len(X))
        X_test = X[indices[:test_size]]
        y_test = y[indices[:test_size]]
        X_train = X[indices[test_size:]]
        y_train = y[indices[test_size:]]

        print(f"\n📊 DIVISÃO AVANÇADA:")
        print(f"Treino: {len(X_train):,} amostras ({len(X_train)/len(X)*100:.1f}%)")
        print(f"Teste: {len(X_test):,} amostras ({len(X_test)/len(X)*100:.1f}%)")

        # 3. Criar e treinar modelo avançado
        self.model = AdvancedNeuralNetwork(use_gpu=self.use_gpu)
        self.model.train_advanced(X_train, y_train, epochs=1500, batch_size=16)

        # 4. Avaliar
        accuracy = self.evaluate_advanced(X_test, y_test)

        # 5. Salvar
        self.save_advanced_model()

        total_time = time.time() - total_start

        print(f"\n🎊 TREINAMENTO AVANÇADO CONCLUÍDO!")
        print(f"⏱️  Tempo total: {total_time:.1f} segundos")
        print(f"📈 Acurácia final: {accuracy:.1f}%")

        if accuracy > 70:
            print("🎉 EXCELENTE! Acurácia muito alta alcançada!")
        elif accuracy > 50:
            print("✅ BOM! Acurácia satisfatória alcançada!")
        elif accuracy > 30:
            print("⚠️  MÉDIO. Acurácia pode ser melhorada.")
        else:
            print("❌ BAIXO. Necessário ajustar hiperparâmetros.")

        return accuracy

    def save_advanced_model(self):
        """Salva modelo avançado"""
        weights_cpu = []
        biases_cpu = []
        bn_params_cpu = []

        for w, b in zip(self.model.weights, self.model.biases):
            if self.use_gpu:
                weights_cpu.append(cp.asnumpy(w).tolist())
                biases_cpu.append(cp.asnumpy(b).tolist())
            else:
                weights_cpu.append(w.tolist())
                biases_cpu.append(b.tolist())

        for gamma, beta in self.model.batch_norm_params:
            if self.use_gpu:
                bn_params_cpu.append([cp.asnumpy(gamma).tolist(), cp.asnumpy(beta).tolist()])
            else:
                bn_params_cpu.append([gamma.tolist(), beta.tolist()])

        model_data = {
            'layers': self.model.layers,
            'weights': weights_cpu,
            'biases': biases_cpu,
            'batch_norm_params': bn_params_cpu,
            'encoding': 'advanced_features',
            'input_size': 750,
            'output_size': 750,
            'features': {
                'one_hot': 640,
                'numeric': 40,
                'position': 40,
                'pairs': 20,
                'stats': 10
            }
        }

        with open('advanced_neural_hash_model.json', 'w') as f:
            json.dump(model_data, f)

        print(f"💾 Modelo avançado salvo em 'advanced_neural_hash_model.json'")

    def predict_magic_number_advanced(self, hash160_correto_hex):
        """Predição avançada"""
        if self.model is None:
            print("❌ Modelo não treinado!")
            return None

        input_features = hex_to_features_advanced(hash160_correto_hex).reshape(1, -1)
        prediction = self.model.predict_advanced(input_features)

        return features_to_hex_advanced(prediction[0])

def main():
    """Função principal avançada"""
    print("🚀 ADVANCED NEURAL HASH PREDICTOR - MÁXIMA ACURÁCIA")
    print("=" * 65)

    # Verificar status
    if GPU_AVAILABLE:
        print("✅ CUDA disponível - Treinamento acelerado")
    else:
        print("⚠️  Usando CPU - Treinamento mais lento")

    print(f"\n🧠 TÉCNICAS AVANÇADAS IMPLEMENTADAS:")
    print("• Features engineering: 750 features por hash")
    print("• Batch normalization: Estabiliza treinamento")
    print("• Swish activation: Melhor que ReLU")
    print("• Loss híbrida: Cross-entropy + MSE")
    print("• Gradient clipping: Evita explosão")
    print("• Learning rate schedule: Otimização adaptativa")
    print("• Distribuição estratégica: Padrões balanceados")

    # Configuração
    num_samples = int(input("\nNúmero de amostras (recomendado: 50000): ") or "50000")

    if num_samples > 100000:
        print("⚠️  Muitas amostras podem demorar muito tempo")
        confirm = input("Continuar? (s/N): ")
        if not confirm.lower().startswith('s'):
            num_samples = 50000
            print(f"Usando {num_samples} amostras")

    # Executar
    predictor = AdvancedHashPredictor()
    accuracy = predictor.run_advanced_training(num_samples=num_samples)

    if accuracy > 0:
        print(f"\n🎯 RESULTADO FINAL: {accuracy:.1f}% de acurácia")

        # Teste interativo
        print(f"\n🧪 TESTE INTERATIVO AVANÇADO:")
        while True:
            hash_input = input("\nDigite hash160 correto (40 chars) ou 'quit': ").strip()

            if hash_input.lower() == 'quit':
                break

            if len(hash_input) != 40:
                print("❌ Hash deve ter 40 caracteres")
                continue

            if not all(c in '0123456789abcdef' for c in hash_input.lower()):
                print("❌ Hash deve conter apenas caracteres hexadecimais")
                continue

            magic_number = predictor.predict_magic_number_advanced(hash_input.lower())
            print(f"Número mágico avançado: {magic_number}")
            print(f"🚀 Comando: ./buscar_chave_por_hash_errado {magic_number} 1:1000000")

if __name__ == "__main__":
    main()