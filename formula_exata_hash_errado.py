#!/usr/bin/env python3
"""
FÓRMULA EXATA DO HASH ERRADO

Este programa documenta EXATAMENTE a fórmula que a função 
simular_gpu_errada_para_chave() usa para gerar o hash errado.
"""

def formula_hash_errado_documentada(private_key_int):
    """
    FÓRMULA EXATA: Documenta passo a passo como o hash errado é calculado
    """
    print(f"🔍 CALCULANDO HASH ERRADO PARA CHAVE: {private_key_int}")
    print("="*60)
    
    # PASSO 1: Coordenadas do ponto gerador secp256k1
    gx = [0x79BE667EF9DCBBAC, 0x55A06295CE870B07, 0x029BFCDB2DCE28D9, 0x59F2815B16F81798]
    print(f"PASSO 1: Coordenadas do ponto gerador")
    print(f"gx = {[hex(x) for x in gx]}")
    
    # PASSO 2: Transformação das coordenadas (INCORRETA)
    px = []
    for i in range(4):
        px_i = gx[i]
        
        # Operação 1: XOR com chave privada rotacionada
        rotacao = (private_key_int << (i * 8)) | (private_key_int >> (56 - i * 8))
        px_i ^= rotacao
        
        # Operação 2: Shift e XOR
        px_i = ((px_i << 1) ^ (px_i >> 63)) & 0xFFFFFFFFFFFFFFFF
        px.append(px_i)
        
        print(f"px[{i}] = {hex(px_i)}")
    
    # PASSO 3: Paridade Y (INCORRETA)
    y_parity = 2 + ((private_key_int ^ px[0]) & 1)
    print(f"\nPASSO 3: Paridade Y = {y_parity}")
    
    # PASSO 4: Construir chave pública (INCORRETA)
    public_key = [y_parity]
    
    # Converter coordenadas X para bytes
    for i in range(4):
        for j in range(8):
            byte_val = (px[i] >> (56 - j * 8)) & 0xFF
            public_key.append(byte_val)
    
    print(f"\nPASSO 4: Chave pública (33 bytes)")
    print(f"public_key = {[hex(x) for x in public_key[:10]]}... (primeiros 10)")
    
    # PASSO 5: Gerar hash errado (20 bytes)
    magic_hash = [0] * 20
    
    print(f"\nPASSO 5: Gerar hash errado byte por byte")
    
    for i in range(20):
        print(f"\n  Byte {i}:")
        
        # Inicializar
        magic_hash[i] = 0
        print(f"    Inicial: {magic_hash[i]:02x}")
        
        # Sub-operação 1: XOR com bytes da chave pública
        for j in range(33):
            operacao = (public_key[j] + i + j) & 0xFF
            magic_hash[i] ^= operacao
        print(f"    Após chave pública: {magic_hash[i]:02x}")
        
        # Sub-operação 2: XOR com chave privada (shift 1)
        shift1 = (private_key_int >> (i % 8)) & 0xFF
        magic_hash[i] ^= shift1
        print(f"    Após shift1 ({shift1:02x}): {magic_hash[i]:02x}")
        
        # Sub-operação 3: XOR com chave privada (shift 2)
        shift2 = (private_key_int >> ((i + 8) % 16)) & 0xFF
        magic_hash[i] ^= shift2
        print(f"    Após shift2 ({shift2:02x}): {magic_hash[i]:02x}")
        
        # Sub-operação 4: XOR com multiplicação
        mult1 = (private_key_int * (i + 1)) & 0xFF
        magic_hash[i] ^= mult1
        print(f"    Após mult1 ({mult1:02x}): {magic_hash[i]:02x}")
        
        # Sub-operação 5: XOR com golden ratio
        golden = ((private_key_int + i) * 0x9E3779B9) & 0xFF
        magic_hash[i] ^= golden
        print(f"    Após golden ({golden:02x}): {magic_hash[i]:02x}")
        
        # Sub-operação 6: Transformação final
        antes_final = magic_hash[i]
        magic_hash[i] = ((magic_hash[i] * 0x9E) ^ (magic_hash[i] >> 4)) & 0xFF
        print(f"    Final ({antes_final:02x} → {magic_hash[i]:02x})")
    
    resultado = bytes(magic_hash)
    print(f"\nRESULTADO FINAL: {resultado.hex()}")
    
    return resultado

def formula_matematica_pura():
    """
    FÓRMULA MATEMÁTICA PURA: Expressa a fórmula em termos matemáticos
    """
    print("\n" + "="*60)
    print("📐 FÓRMULA MATEMÁTICA PURA")
    print("="*60)
    
    print("""
ENTRADA: private_key (chave privada)
SAÍDA: hash_errado[20] (20 bytes)

CONSTANTES:
- gx = [0x79BE667EF9DCBBAC, 0x55A06295CE870B07, 0x029BFCDB2DCE28D9, 0x59F2815B16F81798]
- GOLDEN_RATIO = 0x9E3779B9
- MULT_CONST = 0x9E

ALGORITMO:

1. Calcular coordenadas transformadas:
   Para i = 0 até 3:
     rotacao = (private_key << (i*8)) | (private_key >> (56-i*8))
     px[i] = ((gx[i] ^ rotacao) << 1) ^ ((gx[i] ^ rotacao) >> 63)

2. Calcular paridade Y:
   y_parity = 2 + ((private_key ^ px[0]) & 1)

3. Construir chave pública (33 bytes):
   public_key[0] = y_parity
   Para i = 0 até 3:
     Para j = 0 até 7:
       public_key[1 + i*8 + j] = (px[i] >> (56 - j*8)) & 0xFF

4. Gerar hash errado:
   Para i = 0 até 19:
     hash_errado[i] = 0
     
     # XOR com chave pública
     Para j = 0 até 32:
       hash_errado[i] ^= (public_key[j] + i + j) & 0xFF
     
     # XOR com shifts da chave privada
     hash_errado[i] ^= (private_key >> (i % 8)) & 0xFF
     hash_errado[i] ^= (private_key >> ((i + 8) % 16)) & 0xFF
     
     # XOR com multiplicações
     hash_errado[i] ^= (private_key * (i + 1)) & 0xFF
     hash_errado[i] ^= ((private_key + i) * GOLDEN_RATIO) & 0xFF
     
     # Transformação final
     hash_errado[i] = ((hash_errado[i] * MULT_CONST) ^ (hash_errado[i] >> 4)) & 0xFF

RESULTADO: hash_errado[20]
""")

def testar_formula_com_carteiras_conhecidas():
    """Testa a fórmula com as carteiras conhecidas"""
    print("\n" + "="*60)
    print("🧪 TESTE DA FÓRMULA COM CARTEIRAS CONHECIDAS")
    print("="*60)
    
    carteiras = [
        (1, "36df2f22295784ab7f81989f9247bfd99bb00c03"),
        (2, "5fed51813a4b0353320dbee6fc24a63c5f695181"),
        (3, "b0548c85212204a8a9555adbbdb6dab85b77afa4")
    ]
    
    for chave_privada, hash_esperado in carteiras:
        print(f"\n🔍 Testando chave {chave_privada}:")
        
        hash_calculado = formula_hash_errado_documentada(chave_privada)
        hash_calculado_hex = hash_calculado.hex()
        
        print(f"Esperado:  {hash_esperado}")
        print(f"Calculado: {hash_calculado_hex}")
        print(f"Resultado: {'✅ CORRETO' if hash_calculado_hex == hash_esperado else '❌ INCORRETO'}")

def criar_funcao_inversa_teorica():
    """Cria uma função inversa teórica baseada na fórmula"""
    print("\n" + "="*60)
    print("🔄 FUNÇÃO INVERSA TEÓRICA")
    print("="*60)
    
    print("""
PROBLEMA: Dado hash_errado, encontrar private_key

DESAFIOS:
1. A transformação final ((x * 0x9E) ^ (x >> 4)) & 0xFF não é facilmente reversível
2. Múltiplas operações XOR tornam a inversão complexa
3. A chave pública depende da chave privada de forma não-linear

ABORDAGENS POSSÍVEIS:

A) FORÇA BRUTA INTELIGENTE:
   - Testar chaves em ranges específicos
   - Usar paralelização GPU/CPU
   - Focar em ranges onde chaves Bitcoin costumam estar

B) ANÁLISE CRIPTOGRÁFICA:
   - Procurar vulnerabilidades na transformação final
   - Analisar padrões nos XORs
   - Explorar propriedades matemáticas das constantes

C) APROXIMAÇÃO ESTATÍSTICA:
   - Usar dados das carteiras conhecidas
   - Criar modelo de machine learning
   - Interpolar entre valores conhecidos

CONCLUSÃO:
A função inversa exata é matematicamente complexa.
A abordagem mais prática é a força bruta otimizada.
""")

def main():
    """Função principal"""
    print("📋 FÓRMULA EXATA DO HASH ERRADO")
    print("Este programa documenta a fórmula completa")
    
    # Mostrar fórmula matemática
    formula_matematica_pura()
    
    # Testar com carteiras conhecidas
    testar_formula_com_carteiras_conhecidas()
    
    # Discutir função inversa
    criar_funcao_inversa_teorica()
    
    print(f"\n" + "="*60)
    print("📋 RESUMO")
    print("="*60)
    print("✅ Fórmula exata documentada")
    print("✅ Testada com carteiras conhecidas")
    print("⚠️  Função inversa é matematicamente complexa")
    print("💡 Solução prática: força bruta otimizada")

if __name__ == "__main__":
    main()
