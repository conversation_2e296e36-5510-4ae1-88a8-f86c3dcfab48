{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "c:/Users/<USER>/Downloads/PoolCacatalReiner/PoolCacatalReiner/israel2/funcionou/clone pode mecher", "program": "c:/Users/<USER>/Downloads/PoolCacatalReiner/PoolCacatalReiner/israel2/funcionou/clone pode mecher/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}