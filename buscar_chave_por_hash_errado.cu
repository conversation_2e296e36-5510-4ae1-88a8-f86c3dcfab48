/*
BUSCAR CHAVE POR HASH ERRADO
Lógica: Testa chaves no range, calcula hash errado de cada uma,
quando bater com o hash errado alvo = encontrou a chave privada correta
*/

#include <cuda_runtime.h>
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <time.h>
#include <stdlib.h>

#ifdef _WIN32
    #include <windows.h>
    #define sleep_ms(ms) Sleep(ms)
#else
    #include <unistd.h>
    #define sleep_ms(ms) usleep((ms) * 1000)
#endif

// Constantes
#define GOLDEN_RATIO 0x9E3779B9ULL
#define MULT_CONST 0x9E
#define HASH_SIZE 20

// Coordenadas do ponto gerador secp256k1
__constant__ uint64_t gx[4] = {
    0x79BE667EF9DCBBACULL, 0x55A06295CE870B07ULL, 
    0x029BFCDB2DCE28D9ULL, 0x59F2815B16F81798ULL
};

// Resultado global
__device__ uint64_t d_chave_encontrada = 0;
__device__ int d_encontrou = 0;
__device__ uint64_t d_chaves_testadas = 0;

// FUNÇÃO PRINCIPAL: Calcular hash errado do kernel (mesma lógica que encontrou chave 1)
__device__ void calcular_hash_errado_kernel(uint64_t private_key, uint8_t* hash_errado) {
    // Esta é a MESMA lógica que encontrou a chave 1
    uint64_t px[4];
    for (int i = 0; i < 4; i++) {
        uint64_t rotacao = (private_key << (i * 8)) | (private_key >> (56 - i * 8));
        px[i] = gx[i] ^ rotacao;
        px[i] = ((px[i] << 1) ^ (px[i] >> 63));
    }
    
    uint8_t y_parity = 2 + ((private_key ^ px[0]) & 1);
    
    for (int i = 0; i < HASH_SIZE; i++) {
        uint32_t hash_val = 0;
        
        hash_val ^= (y_parity + i) & 0xFF;
        
        for (int j = 0; j < 4; j++) {
            for (int k = 0; k < 8; k++) {
                uint8_t coord_byte = (px[j] >> (56 - k * 8)) & 0xFF;
                hash_val ^= (coord_byte + i + j * 8 + k + 1) & 0xFF;
            }
        }
        
        hash_val ^= (private_key >> (i & 7)) & 0xFF;
        hash_val ^= (private_key >> ((i + 8) & 15)) & 0xFF;
        hash_val ^= (private_key * (i + 1)) & 0xFF;
        hash_val ^= ((private_key + i) * GOLDEN_RATIO) & 0xFF;
        
        hash_val &= 0xFF;
        hash_errado[i] = ((hash_val * MULT_CONST) ^ (hash_val >> 4)) & 0xFF;
    }
}

__global__ void buscar_chave_kernel(
    uint64_t start_range, 
    uint64_t end_range,
    uint8_t* target_hash_errado
) {
    uint64_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    uint64_t stride = gridDim.x * blockDim.x;
    uint64_t thread_id = blockIdx.x * blockDim.x + threadIdx.x;
    
    for (uint64_t private_key = start_range + idx; 
         private_key < end_range && !d_encontrou; 
         private_key += stride) {
        
        // Atualizar contador
        if (thread_id == 0) {
            atomicAdd((unsigned long long*)&d_chaves_testadas, stride);
        }
        
        // PASSO 1: Calcular hash errado desta chave privada
        uint8_t hash_errado_calculado[HASH_SIZE];
        calcular_hash_errado_kernel(private_key, hash_errado_calculado);
        
        // PASSO 2: Comparar com o hash errado alvo
        bool match = true;
        for (int i = 0; i < HASH_SIZE; i++) {
            if (hash_errado_calculado[i] != target_hash_errado[i]) {
                match = false;
                break;
            }
        }
        
        // PASSO 3: Se bateu = encontrou a chave privada correta!
        if (match) {
            if (atomicCAS((unsigned long long*)&d_chave_encontrada, 0ULL, private_key) == 0ULL) {
                d_encontrou = 1;
                printf("🎉 CHAVE PRIVADA ENCONTRADA! %llu (0x%llx)\n", private_key, private_key);
                printf("   Hash errado calculado bate com o alvo!\n");
            }
        }
        
        // Progresso ocasional
        if ((private_key & 0x3FFFFF) == 0 && thread_id < 3) {
            printf("Thread %llu: Testando chave %llu (0x%llx)\n", thread_id, private_key, private_key);
        }
    }
}

// Função para converter range hex/decimal
uint64_t converter_para_decimal(const char* str) {
    if (strncmp(str, "0x", 2) == 0 || strncmp(str, "0X", 2) == 0) {
        return strtoull(str, NULL, 16);
    } else {
        bool is_hex = false;
        for (int i = 0; str[i]; i++) {
            if ((str[i] >= 'a' && str[i] <= 'f') || 
                (str[i] >= 'A' && str[i] <= 'F')) {
                is_hex = true;
                break;
            }
        }
        
        if (is_hex) {
            return strtoull(str, NULL, 16);
        } else {
            return strtoull(str, NULL, 10);
        }
    }
}

int main(int argc, char* argv[]) {
    printf("🎯 BUSCAR CHAVE PRIVADA POR HASH ERRADO\n");
    printf("=======================================\n");
    printf("Lógica: Testa chaves → Calcula hash errado → Compara com alvo\n");
    printf("Quando bater = encontrou a chave privada correta!\n");
    printf("\n");
    
    char hash_errado_alvo_hex[50];
    char range_input[100];
    uint64_t start_range, end_range;
    
    // Verificar argumentos
    if (argc >= 3) {
        strcpy(hash_errado_alvo_hex, argv[1]);

        if (argc >= 4) {
            // Modo: hash start end (3 argumentos separados)
            start_range = strtoull(argv[2], NULL, 0);
            end_range = strtoull(argv[3], NULL, 0);
            sprintf(range_input, "%llu:%llu", start_range, end_range);

            printf("🎯 Hash errado alvo (argumento): %s\n", hash_errado_alvo_hex);
            printf("📊 Range (argumentos): %llu - %llu\n", start_range, end_range);
        } else {
            // Modo: hash range (formato start:end)
            strcpy(range_input, argv[2]);

            printf("🎯 Hash errado alvo (argumento): %s\n", hash_errado_alvo_hex);
            printf("📊 Range (argumento): %s\n", range_input);
        }
    } else {
        // Modo interativo
        printf("🎯 CONFIGURAÇÃO DO HASH ERRADO ALVO\n");
        printf("===================================\n");
        printf("Este é o hash errado que você quer encontrar a chave privada.\n");
        printf("Exemplos conhecidos:\n");
        printf("  • 36df2f22295784ab7f81989f9247bfd99bb00c03 (chave privada 1)\n");
        printf("  • 5fed51813a4b0353320dbee6fc24a63c5f695181 (chave privada 2)\n");
        printf("  • b0548c85212204a8a9555adbbdb6dab85b77afa4 (chave privada 3)\n");
        printf("\nDigite o hash errado alvo (40 caracteres hex): ");
        
        if (scanf("%49s", hash_errado_alvo_hex) != 1) {
            printf("❌ Erro ao ler hash errado\n");
            return 1;
        }
        
        printf("\n📊 CONFIGURAÇÃO DO RANGE\n");
        printf("========================\n");
        printf("Range onde procurar a chave privada.\n");
        printf("Formatos aceitos:\n");
        printf("  • Decimal: 1:1000000\n");
        printf("  • Hex: 1:ffffff\n");
        printf("  • Hex com 0x: 0x1:0xffffff\n");
        printf("  • Range alvo: 400000000000000000:7fffffffffffffffff\n");
        printf("\nDigite o range (start:end): ");
        
        if (scanf("%99s", range_input) != 1) {
            printf("❌ Erro ao ler range\n");
            return 1;
        }
    }

    // Validar hash errado
    if (strlen(hash_errado_alvo_hex) != 40) {
        printf("❌ Hash errado deve ter exatamente 40 caracteres hex!\n");
        return 1;
    }

    // Parsear range (se ainda não foi parseado)
    if (argc < 4) {
        char* colon = strchr(range_input, ':');
        if (!colon) {
            printf("❌ Formato de range inválido! Use start:end\n");
            printf("Ou use: %s <hash> <start> <end>\n", argv[0]);
            return 1;
        }

        *colon = '\0';
        char* start_str = range_input;
        char* end_str = colon + 1;

        start_range = converter_para_decimal(start_str);
        end_range = converter_para_decimal(end_str);
    }
    
    printf("\n✅ CONFIGURAÇÃO FINAL\n");
    printf("====================\n");
    printf("🎯 Hash errado alvo: %s\n", hash_errado_alvo_hex);
    printf("📊 Range início: %llu (0x%llx)\n", start_range, start_range);
    printf("📊 Range fim: %llu (0x%llx)\n", end_range, end_range);
    printf("📊 Total chaves: %llu\n", end_range - start_range);
    
    if (end_range <= start_range) {
        printf("❌ Range inválido!\n");
        return 1;
    }
    
    // Verificar CUDA
    int device_count;
    cudaGetDeviceCount(&device_count);
    if (device_count == 0) {
        printf("❌ Nenhuma GPU CUDA encontrada!\n");
        return 1;
    }
    
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, 0);
    printf("\n🚀 GPU: %s\n", prop.name);
    
    // Converter hash errado para bytes
    uint8_t target_hash_errado[HASH_SIZE];
    for (int i = 0; i < HASH_SIZE; i++) {
        sscanf(hash_errado_alvo_hex + i * 2, "%2hhx", &target_hash_errado[i]);
    }
    
    // Alocar memória GPU
    uint8_t* d_target_hash_errado;
    cudaMalloc(&d_target_hash_errado, HASH_SIZE);
    cudaMemcpy(d_target_hash_errado, target_hash_errado, HASH_SIZE, cudaMemcpyHostToDevice);
    
    // Reset contadores
    uint64_t zero = 0;
    int zero_int = 0;
    cudaMemcpyToSymbol(d_chave_encontrada, &zero, sizeof(uint64_t));
    cudaMemcpyToSymbol(d_encontrou, &zero_int, sizeof(int));
    cudaMemcpyToSymbol(d_chaves_testadas, &zero, sizeof(uint64_t));
    
    // Configuração do kernel
    int threads_per_block = 256;
    int blocks_per_grid = 1024;
    
    printf("⚙️  Configuração: %d threads/block, %d blocks\n", threads_per_block, blocks_per_grid);
    printf("\n🔍 INICIANDO BUSCA DA CHAVE PRIVADA...\n");
    printf("Lógica: Para cada chave → Calcular hash errado → Comparar com alvo\n");
    printf("============================================================\n");
    
    // Timing
    clock_t inicio = clock();
    time_t inicio_time = time(NULL);
    
    // Lançar kernel
    buscar_chave_kernel<<<blocks_per_grid, threads_per_block>>>(
        start_range, end_range, d_target_hash_errado
    );
    
    // Monitorar progresso
    int encontrou = 0;
    int contador_progresso = 0;
    
    printf("⏱️  Início: %s", ctime(&inicio_time));
    
    while (!encontrou) {
        cudaMemcpyFromSymbol(&encontrou, d_encontrou, sizeof(int));
        if (encontrou) break;
        
        uint64_t chaves_testadas_atual;
        cudaMemcpyFromSymbol(&chaves_testadas_atual, d_chaves_testadas, sizeof(uint64_t));
        
        clock_t agora = clock();
        double tempo_decorrido = ((double)(agora - inicio)) / CLOCKS_PER_SEC;
        
        if (tempo_decorrido >= (contador_progresso + 1) * 5.0) {
            contador_progresso++;
            
            double velocidade_media = chaves_testadas_atual / tempo_decorrido;
            uint64_t total_chaves = end_range - start_range;
            double progresso_pct = ((double)chaves_testadas_atual / total_chaves) * 100.0;
            
            printf("⏱️  %02d:%02d | Testadas: %llu | Progresso: %.6f%% | Velocidade: %.0f K/s\n", 
                   (int)(tempo_decorrido/60), (int)tempo_decorrido%60,
                   chaves_testadas_atual, progresso_pct, velocidade_media / 1000.0);
        }
        
        cudaError_t status = cudaDeviceSynchronize();
        if (status == cudaSuccess) break;
        
        sleep_ms(1000);
    }
    
    printf("\n============================================================\n");
    
    // Verificar resultado
    uint64_t chave_encontrada;
    uint64_t chaves_testadas_final;
    
    cudaMemcpyFromSymbol(&chave_encontrada, d_chave_encontrada, sizeof(uint64_t));
    cudaMemcpyFromSymbol(&chaves_testadas_final, d_chaves_testadas, sizeof(uint64_t));
    
    clock_t fim = clock();
    double tempo_total = ((double)(fim - inicio)) / CLOCKS_PER_SEC;
    
    printf("📊 ESTATÍSTICAS FINAIS:\n");
    printf("⏱️  Tempo total: %.2f segundos (%.1f minutos)\n", tempo_total, tempo_total/60.0);
    printf("🔢 Chaves testadas: %llu\n", chaves_testadas_final);
    
    if (tempo_total > 0) {
        double velocidade = chaves_testadas_final / tempo_total;
        printf("⚡ Velocidade: %.0f chaves/segundo (%.2f M/s)\n", velocidade, velocidade/1000000.0);
    }
    
    if (encontrou) {
        printf("\n🎉 CHAVE PRIVADA ENCONTRADA!\n");
        printf("===========================\n");
        printf("🔑 Chave privada: %llu (0x%llx)\n", chave_encontrada, chave_encontrada);
        printf("🎯 Hash errado alvo: %s\n", hash_errado_alvo_hex);
        printf("✅ Esta chave privada gera o hash errado alvo!\n");
        
        printf("\n🔍 VERIFICAÇÃO:\n");
        printf("Use esta chave no programa de validação:\n");
        printf("python3 verificar_resultado_cuda.py %llu %s\n", chave_encontrada, hash_errado_alvo_hex);
        
    } else {
        printf("\n❌ CHAVE PRIVADA NÃO ENCONTRADA\n");
        printf("==============================\n");
        printf("💡 A chave privada não está no range especificado\n");
        printf("💡 Tente um range diferente ou verifique o hash errado alvo\n");
    }
    
    cudaFree(d_target_hash_errado);
    return encontrou ? 0 : 1;
}
