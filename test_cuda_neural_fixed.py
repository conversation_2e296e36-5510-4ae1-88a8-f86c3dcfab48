#!/usr/bin/env python3
"""
TESTE CUDA CORRIGIDO
Verifica se CUDA está funcionando para rede neural (versão compatível)
"""

def get_gpu_info():
    """Obtém informações da GPU de forma compatível"""
    try:
        import cupy as cp
        
        device = cp.cuda.Device()
        
        # Obter nome da GPU (compatível com diferentes versões)
        gpu_name = "GPU Desconhecida"
        try:
            gpu_name = device.name
        except AttributeError:
            try:
                props = cp.cuda.runtime.getDeviceProperties(device.id)
                gpu_name = props['name'].decode() if isinstance(props['name'], bytes) else str(props['name'])
            except:
                try:
                    gpu_name = f"CUDA Device {device.id}"
                except:
                    gpu_name = "GPU Disponível"
        
        # Obter informações de memória
        memory_info = "Não disponível"
        try:
            mem_info = device.mem_info
            total_mem = mem_info[1] / 1024**3
            free_mem = mem_info[0] / 1024**3
            memory_info = f"{total_mem:.1f} GB total, {free_mem:.1f} GB livre"
        except:
            try:
                total_mem = cp.cuda.runtime.memGetInfo()[1] / 1024**3
                memory_info = f"{total_mem:.1f} GB"
            except:
                memory_info = "Informação não disponível"
        
        return gpu_name, memory_info, True
        
    except ImportError:
        return "CuPy não instalado", "N/A", False
    except Exception as e:
        return f"Erro: {e}", "N/A", False

def test_cuda_neural():
    """Teste CUDA para rede neural"""
    print("🧪 TESTE CUDA PARA REDE NEURAL (CORRIGIDO)")
    print("=" * 45)
    
    # Obter informações da GPU
    gpu_name, memory_info, cuda_available = get_gpu_info()
    
    print(f"GPU: {gpu_name}")
    print(f"Memória: {memory_info}")
    print(f"CUDA disponível: {cuda_available}")
    
    if not cuda_available:
        print("\n❌ CUDA não disponível")
        print("💡 Para instalar: pip install cupy-cuda12x")
        return False
    
    try:
        import cupy as cp
        import numpy as np
        import time
        
        print(f"\n🔧 Versão CuPy: {cp.__version__}")
        
        # Teste de performance
        size = 2000  # Tamanho menor para evitar problemas de memória
        print(f"\nTeste de performance ({size}x{size}):")
        
        # CPU
        print("🔄 Testando CPU...")
        a_cpu = np.random.randn(size, size).astype(np.float32)
        b_cpu = np.random.randn(size, size).astype(np.float32)
        
        start = time.time()
        c_cpu = np.dot(a_cpu, b_cpu)
        cpu_time = time.time() - start
        
        print(f"CPU: {cpu_time:.3f} segundos")
        
        # GPU
        print("🔄 Testando GPU...")
        try:
            a_gpu = cp.asarray(a_cpu)
            b_gpu = cp.asarray(b_cpu)
            
            # Warm-up
            _ = cp.dot(a_gpu, b_gpu)
            cp.cuda.Stream.null.synchronize()
            
            start = time.time()
            c_gpu = cp.dot(a_gpu, b_gpu)
            cp.cuda.Stream.null.synchronize()  # Aguardar conclusão
            gpu_time = time.time() - start
            
            print(f"GPU: {gpu_time:.3f} segundos")
            
            # Verificar se resultados são similares
            c_gpu_cpu = cp.asnumpy(c_gpu)
            diff = np.mean(np.abs(c_cpu - c_gpu_cpu))
            print(f"Diferença média: {diff:.2e}")
            
            if diff < 1e-3:
                print("✅ Resultados CPU e GPU são consistentes")
            else:
                print("⚠️  Diferença significativa entre CPU e GPU")
            
            # Calcular speedup
            if gpu_time > 0:
                speedup = cpu_time / gpu_time
                print(f"Speedup: {speedup:.1f}x")
                
                if speedup > 2:
                    print("✅ CUDA está funcionando bem!")
                    return True
                elif speedup > 1:
                    print("⚠️  CUDA funciona mas speedup baixo")
                    return True
                else:
                    print("❌ GPU mais lenta que CPU - problema nos drivers?")
                    return False
            else:
                print("❌ Erro no timing da GPU")
                return False
                
        except Exception as e:
            print(f"❌ Erro no teste GPU: {e}")
            return False
        
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        return False

def test_neural_operations():
    """Testa operações específicas de rede neural"""
    print(f"\n🧠 TESTE DE OPERAÇÕES DE REDE NEURAL")
    print("=" * 40)
    
    try:
        import cupy as cp
        import numpy as np
        
        # Simular forward pass de rede neural
        batch_size = 256
        input_size = 40
        hidden_size = 512
        
        print(f"Simulando rede neural:")
        print(f"  Batch: {batch_size}")
        print(f"  Input: {input_size}")
        print(f"  Hidden: {hidden_size}")
        
        # Dados de entrada
        X = cp.random.randn(batch_size, input_size).astype(cp.float32)
        W1 = cp.random.randn(input_size, hidden_size).astype(cp.float32)
        b1 = cp.random.randn(1, hidden_size).astype(cp.float32)
        
        # Forward pass
        print("🔄 Forward pass...")
        z1 = cp.dot(X, W1) + b1
        a1 = 1 / (1 + cp.exp(-cp.clip(z1, -10, 10)))  # Sigmoid
        
        # Backward pass simulado
        print("🔄 Backward pass...")
        grad_output = cp.random.randn(*a1.shape).astype(cp.float32)
        grad_z1 = grad_output * a1 * (1 - a1)  # Derivada sigmoid
        grad_W1 = cp.dot(X.T, grad_z1)
        grad_b1 = cp.sum(grad_z1, axis=0, keepdims=True)
        
        print("✅ Operações de rede neural executadas com sucesso")
        
        # Verificar shapes
        print(f"Shapes:")
        print(f"  z1: {z1.shape}")
        print(f"  a1: {a1.shape}")
        print(f"  grad_W1: {grad_W1.shape}")
        print(f"  grad_b1: {grad_b1.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro nas operações de rede neural: {e}")
        return False

def main():
    """Função principal"""
    print("🚀 TESTE CUDA NEURAL - VERSÃO CORRIGIDA")
    print("=" * 50)
    
    # Teste básico CUDA
    cuda_works = test_cuda_neural()
    
    if cuda_works:
        # Teste operações de rede neural
        neural_works = test_neural_operations()
        
        if neural_works:
            print(f"\n🎊 TODOS OS TESTES PASSARAM!")
            print("✅ CUDA está pronto para rede neural")
            print("🚀 Execute: python3 neural_hash_cuda.py")
        else:
            print(f"\n⚠️  CUDA básico funciona, mas operações neurais falharam")
            print("💡 Tente: python3 neural_hash_integrated.py")
    else:
        print(f"\n❌ CUDA não está funcionando adequadamente")
        print("💡 Use versão CPU: python3 simple_neural_hash.py")
    
    print(f"\n📋 ALTERNATIVAS:")
    print("• CPU simples: python3 simple_neural_hash.py")
    print("• CPU integrada: python3 neural_hash_integrated.py")
    print("• PyTorch CUDA: python3 neural_hash_pytorch_cuda.py")
    print("• CuPy CUDA: python3 neural_hash_cuda.py")

if __name__ == "__main__":
    main()
