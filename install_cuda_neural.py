#!/usr/bin/env python3
"""
INSTALADOR CUDA PARA REDE NEURAL
Instala CuPy e dependências para aceleração GPU
"""

import subprocess
import sys
import platform

def run_command(command):
    """Executa comando e retorna resultado"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def detect_cuda_version():
    """Detecta versão do CUDA instalada"""
    print("🔍 DETECTANDO VERSÃO CUDA...")
    
    # Tentar nvidia-smi
    success, stdout, stderr = run_command("nvidia-smi")
    
    if success and "CUDA Version" in stdout:
        try:
            # Extrair versão CUDA do nvidia-smi
            lines = stdout.split('\n')
            for line in lines:
                if "CUDA Version" in line:
                    cuda_version = line.split("CUDA Version: ")[1].split()[0]
                    major_version = int(cuda_version.split('.')[0])
                    print(f"✅ CUDA {cuda_version} detectado")
                    return major_version
        except:
            pass
    
    # Tentar nvcc
    success, stdout, stderr = run_command("nvcc --version")
    
    if success and "release" in stdout:
        try:
            for line in stdout.split('\n'):
                if "release" in line:
                    version = line.split("release ")[1].split(',')[0]
                    major_version = int(version.split('.')[0])
                    print(f"✅ CUDA {version} detectado (nvcc)")
                    return major_version
        except:
            pass
    
    print("❌ CUDA não detectado")
    return None

def install_cupy(cuda_version=None):
    """Instala CuPy baseado na versão CUDA"""
    print(f"\n🚀 INSTALANDO CUPY")
    print("=" * 25)
    
    if cuda_version is None:
        print("⚠️  CUDA não detectado - instalando CuPy CPU fallback")
        cupy_package = "cupy-cpu"
    elif cuda_version >= 12:
        print(f"🎯 Instalando CuPy para CUDA 12.x")
        cupy_package = "cupy-cuda12x"
    elif cuda_version >= 11:
        print(f"🎯 Instalando CuPy para CUDA 11.x")
        cupy_package = "cupy-cuda11x"
    else:
        print(f"⚠️  CUDA {cuda_version} muito antigo - usando CPU fallback")
        cupy_package = "cupy-cpu"
    
    print(f"📦 Pacote: {cupy_package}")
    
    # Tentar instalação
    install_cmd = f"pip install {cupy_package}"
    print(f"🔄 Executando: {install_cmd}")
    
    success, stdout, stderr = run_command(install_cmd)
    
    if success:
        print("✅ CuPy instalado com sucesso!")
        return True
    else:
        print(f"❌ Erro na instalação: {stderr}")
        
        # Tentar instalação alternativa
        if cupy_package != "cupy-cpu":
            print("\n🔄 Tentando instalação CPU fallback...")
            alt_cmd = "pip install cupy-cpu"
            success, stdout, stderr = run_command(alt_cmd)
            
            if success:
                print("✅ CuPy CPU instalado como fallback!")
                return True
        
        return False

def install_other_dependencies():
    """Instala outras dependências necessárias"""
    print(f"\n📦 INSTALANDO DEPENDÊNCIAS ADICIONAIS")
    print("=" * 40)
    
    dependencies = [
        "numpy",
        "matplotlib",
        "psutil",  # Para monitoramento de sistema
        "tqdm"     # Para barras de progresso
    ]
    
    for dep in dependencies:
        print(f"\n🔄 Instalando {dep}...")
        
        success, stdout, stderr = run_command(f"pip install {dep}")
        
        if success:
            print(f"✅ {dep} instalado")
        else:
            print(f"⚠️  Erro ao instalar {dep}: {stderr}")

def test_cuda_installation():
    """Testa instalação CUDA"""
    print(f"\n🧪 TESTANDO INSTALAÇÃO CUDA")
    print("=" * 30)
    
    try:
        import cupy as cp
        print("✅ CuPy importado com sucesso")
        
        # Teste básico
        device = cp.cuda.Device()
        print(f"✅ GPU: {device.name}")
        print(f"✅ Memória: {device.mem_info[1] / 1024**3:.1f} GB")
        
        # Teste de operação
        x = cp.array([1, 2, 3, 4, 5])
        y = cp.sum(x)
        print(f"✅ Operação teste: soma([1,2,3,4,5]) = {float(y)}")
        
        # Teste de rede neural básica
        a = cp.random.randn(1000, 500).astype(cp.float32)
        b = cp.random.randn(500, 200).astype(cp.float32)
        c = cp.dot(a, b)
        print(f"✅ Multiplicação matricial: {a.shape} × {b.shape} = {c.shape}")
        
        return True
        
    except ImportError:
        print("❌ CuPy não pode ser importado")
        return False
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

def check_system_requirements():
    """Verifica requisitos do sistema"""
    print("🔍 VERIFICANDO REQUISITOS DO SISTEMA")
    print("=" * 40)
    
    # Python version
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}"
    print(f"Python: {python_version}")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ é necessário")
        return False
    else:
        print("✅ Versão Python compatível")
    
    # Sistema operacional
    system = platform.system()
    print(f"Sistema: {system}")
    
    # Arquitetura
    arch = platform.machine()
    print(f"Arquitetura: {arch}")
    
    # Verificar se é 64-bit
    if "64" not in arch:
        print("⚠️  Arquitetura 32-bit pode ter limitações")
    else:
        print("✅ Arquitetura 64-bit")
    
    return True

def create_cuda_test_script():
    """Cria script de teste CUDA"""
    test_script = '''#!/usr/bin/env python3
"""
TESTE RÁPIDO CUDA
Verifica se CUDA está funcionando para rede neural
"""

def test_cuda_neural():
    try:
        import cupy as cp
        import numpy as np
        import time
        
        print("🧪 TESTE CUDA PARA REDE NEURAL")
        print("=" * 35)
        
        # Informações da GPU
        device = cp.cuda.Device()
        print(f"GPU: {device.name}")
        print(f"Memória: {device.mem_info[1] / 1024**3:.1f} GB")
        
        # Teste de performance
        size = 5000
        print(f"\\nTeste de performance ({size}x{size}):")
        
        # CPU
        a_cpu = np.random.randn(size, size).astype(np.float32)
        b_cpu = np.random.randn(size, size).astype(np.float32)
        
        start = time.time()
        c_cpu = np.dot(a_cpu, b_cpu)
        cpu_time = time.time() - start
        
        print(f"CPU: {cpu_time:.3f} segundos")
        
        # GPU
        a_gpu = cp.asarray(a_cpu)
        b_gpu = cp.asarray(b_cpu)
        
        start = time.time()
        c_gpu = cp.dot(a_gpu, b_gpu)
        cp.cuda.Stream.null.synchronize()  # Aguardar conclusão
        gpu_time = time.time() - start
        
        print(f"GPU: {gpu_time:.3f} segundos")
        print(f"Speedup: {cpu_time / gpu_time:.1f}x")
        
        if cpu_time / gpu_time > 2:
            print("✅ CUDA está funcionando bem!")
        else:
            print("⚠️  Speedup baixo - verifique drivers")
        
        return True
        
    except ImportError:
        print("❌ CuPy não instalado")
        return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    test_cuda_neural()
'''
    
    with open('test_cuda_neural.py', 'w') as f:
        f.write(test_script)
    
    print("📝 Script de teste criado: test_cuda_neural.py")

def main():
    """Função principal"""
    print("🚀 INSTALADOR CUDA PARA REDE NEURAL HASH")
    print("=" * 50)
    
    # Verificar requisitos
    if not check_system_requirements():
        print("❌ Requisitos não atendidos")
        return
    
    # Detectar CUDA
    cuda_version = detect_cuda_version()
    
    # Instalar CuPy
    if not install_cupy(cuda_version):
        print("❌ Falha na instalação do CuPy")
        print("💡 Você ainda pode usar a versão CPU")
    
    # Instalar outras dependências
    install_other_dependencies()
    
    # Testar instalação
    cuda_works = test_cuda_installation()
    
    # Criar script de teste
    create_cuda_test_script()
    
    # Resultado final
    print(f"\n🎊 INSTALAÇÃO CONCLUÍDA!")
    print("=" * 30)
    
    if cuda_works:
        print("✅ CUDA funcionando perfeitamente!")
        print("🚀 Agora você pode executar:")
        print("   python neural_hash_cuda.py")
        print("   python test_cuda_neural.py")
    else:
        print("⚠️  CUDA não disponível - usando CPU")
        print("🚀 Você ainda pode executar:")
        print("   python neural_hash_integrated.py")
        print("   python simple_neural_hash.py")
    
    print(f"\n💡 DICAS:")
    print("• Para melhor performance: use GPU com 8GB+ de VRAM")
    print("• Para datasets grandes: use neural_hash_cuda.py")
    print("• Para testes rápidos: use simple_neural_hash.py")

if __name__ == "__main__":
    main()
