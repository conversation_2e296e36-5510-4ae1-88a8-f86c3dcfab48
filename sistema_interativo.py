#!/usr/bin/env python3
"""
SISTEMA INTERATIVO COMPLETO
Interface amigável para descobrir chaves Bitcoin
"""

import os
import sys
import time
import json
import subprocess
from main import calculate_target_hash160

class SistemaInterativo:
    def __init__(self):
        self.endereco_alvo = None
        self.hash160_correto = None
        self.start_range = None
        self.end_range = None
        self.telegram_config = None
        
    def mostrar_banner(self):
        print("="*80)
        print("🎯 SISTEMA COMPLETO - DESCOBRIR CHAVE PRIVADA BITCOIN")
        print("="*80)
        print("🚀 Busca CUDA ultra-rápida")
        print("✅ Validação completa automática")
        print("📱 Notificação Telegram")
        print("💾 Salvamento automático")
        print("="*80)
        print()
    
    def obter_endereco(self):
        """Obter endereço Bitcoin do usuário"""
        print("📍 CONFIGURAÇÃO DO ENDEREÇO ALVO")
        print("-" * 40)
        
        while True:
            endereco = input("Digite o endereço Bitcoin: ").strip()
            
            if not endereco:
                print("❌ Endereço não pode estar vazio!")
                continue
            
            # Validação básica
            if not endereco.startswith('1') and not endereco.startswith('3') and not endereco.startswith('bc1'):
                print("❌ Endereço deve começar com 1, 3 ou bc1")
                continue
            
            if len(endereco) < 26 or len(endereco) > 62:
                print("❌ Tamanho de endereço inválido")
                continue
            
            try:
                # Calcular hash160
                hash160 = calculate_target_hash160(endereco)
                
                self.endereco_alvo = endereco
                self.hash160_correto = hash160.hex()
                
                print(f"✅ Endereço válido: {endereco}")
                print(f"🎯 Hash160 correto: {self.hash160_correto}")
                return True
                
            except Exception as e:
                print(f"❌ Erro ao processar endereço: {e}")
                continue
    
    def obter_range(self):
        """Obter range de busca do usuário"""
        print("\n📊 CONFIGURAÇÃO DO RANGE DE BUSCA")
        print("-" * 40)
        
        print("Opções de range:")
        print("1. 🧪 Teste rápido (1 - 1,000,000)")
        print("2. 🔍 Range pequeno (1,000,000 - 100,000,000)")
        print("3. 🎯 Range médio (100,000,000 - 10,000,000,000)")
        print("4. 🚀 Range grande (10B - 1T)")
        print("5. 💀 Range extremo (400000000000000000 - 7fffffffffffffffff)")
        print("6. 🛠️  Range personalizado")
        
        while True:
            try:
                opcao = input("\nEscolha uma opção (1-6): ").strip()
                
                if opcao == "1":
                    self.start_range = 1
                    self.end_range = 1000000
                    tempo_estimado = "segundos"
                    
                elif opcao == "2":
                    self.start_range = 1000000
                    self.end_range = 100000000
                    tempo_estimado = "minutos"
                    
                elif opcao == "3":
                    self.start_range = 100000000
                    self.end_range = 10000000000
                    tempo_estimado = "horas"
                    
                elif opcao == "4":
                    self.start_range = 10000000000
                    self.end_range = 1000000000000
                    tempo_estimado = "dias"
                    
                elif opcao == "5":
                    self.start_range = 4611686018427387904
                    self.end_range = 9223372036854775807
                    tempo_estimado = "semanas"
                    
                elif opcao == "6":
                    print("\n🛠️  RANGE PERSONALIZADO")
                    self.start_range = int(input("Range início: "))
                    self.end_range = int(input("Range fim: "))
                    tempo_estimado = "variável"
                    
                else:
                    print("❌ Opção inválida!")
                    continue
                
                total_chaves = self.end_range - self.start_range
                print(f"\n✅ Range configurado:")
                print(f"   Início: {self.start_range:,}")
                print(f"   Fim: {self.end_range:,}")
                print(f"   Total: {total_chaves:,} chaves")
                print(f"   Tempo estimado: {tempo_estimado}")
                
                if total_chaves > 1000000000000:  # 1 trilhão
                    confirmar = input("\n⚠️  Range muito grande! Continuar? (s/N): ")
                    if not confirmar.lower().startswith('s'):
                        continue
                
                return True
                
            except ValueError:
                print("❌ Valor inválido!")
                continue
    
    def configurar_telegram(self):
        """Configurar Telegram (opcional)"""
        print("\n📱 CONFIGURAÇÃO DO TELEGRAM (OPCIONAL)")
        print("-" * 40)
        
        usar_telegram = input("Configurar notificações Telegram? (s/N): ").lower().startswith('s')
        
        if not usar_telegram:
            print("⏭️  Pulando configuração do Telegram")
            return True
        
        # Verificar se já existe configuração
        if os.path.exists('telegram_config.json'):
            try:
                with open('telegram_config.json', 'r') as f:
                    config = json.load(f)
                
                print(f"📱 Configuração encontrada:")
                print(f"   Bot Token: {config['bot_token'][:10]}...")
                print(f"   Chat ID: {config['chat_id']}")
                
                usar_existente = input("Usar configuração existente? (S/n): ")
                if not usar_existente.lower().startswith('n'):
                    self.telegram_config = config
                    print("✅ Usando configuração existente")
                    return True
            except:
                pass
        
        # Configurar novo
        print("\n🤖 Para configurar o Telegram:")
        print("1. Procure @BotFather no Telegram")
        print("2. Envie: /newbot")
        print("3. Siga as instruções")
        print("4. Copie o TOKEN")
        print("5. Envie uma mensagem para seu bot")
        print("6. Acesse: https://api.telegram.org/bot<TOKEN>/getUpdates")
        print("7. Copie o chat_id")
        
        bot_token = input("\nToken do bot: ").strip()
        chat_id = input("Chat ID: ").strip()
        
        if bot_token and chat_id:
            self.telegram_config = {
                'bot_token': bot_token,
                'chat_id': chat_id
            }
            
            # Salvar configuração
            try:
                with open('telegram_config.json', 'w') as f:
                    json.dump(self.telegram_config, f, indent=2)
                print("✅ Configuração do Telegram salva")
            except:
                print("⚠️  Erro ao salvar configuração")
        
        return True
    
    def verificar_programas(self):
        """Verificar se os programas estão compilados"""
        print("\n🔧 VERIFICANDO PROGRAMAS")
        print("-" * 40)
        
        programas = [
            ('descobrir_cuda_completo', 'Programa CUDA principal'),
            ('busca_extrema', 'Programa otimizado (opcional)'),
            ('busca_ultra', 'Programa ultra-rápido (opcional)')
        ]
        
        programa_principal = None
        
        for programa, descricao in programas:
            if os.path.exists(f'./{programa}'):
                print(f"✅ {descricao}: {programa}")
                if programa_principal is None:
                    programa_principal = programa
            else:
                print(f"❌ {descricao}: {programa} (não encontrado)")
        
        if programa_principal is None:
            print("\n❌ ERRO: Nenhum programa CUDA encontrado!")
            print("💡 Compile primeiro:")
            print("   make -f Makefile_completo auto")
            return False
        
        self.programa_principal = programa_principal
        print(f"\n🎯 Usando programa: {programa_principal}")
        return True
    
    def executar_busca(self):
        """Executar a busca"""
        print("\n🚀 EXECUTANDO BUSCA")
        print("=" * 40)
        
        comando = [
            f'./{self.programa_principal}',
            self.hash160_correto,
            str(self.start_range),
            str(self.end_range),
            self.endereco_alvo
        ]
        
        print(f"Comando: {' '.join(comando)}")
        print(f"Endereço alvo: {self.endereco_alvo}")
        print(f"Range: {self.start_range:,} - {self.end_range:,}")
        print(f"Total chaves: {self.end_range - self.start_range:,}")
        
        if self.telegram_config:
            print("📱 Telegram: Configurado")
        else:
            print("📱 Telegram: Não configurado")
        
        print("\n⏱️  Iniciando busca...")
        print("-" * 40)
        
        try:
            # Executar programa
            inicio = time.time()
            resultado = subprocess.run(comando, capture_output=False, text=True)
            fim = time.time()
            
            tempo_total = fim - inicio
            
            print("-" * 40)
            print(f"⏱️  Tempo total: {tempo_total:.2f} segundos")
            
            if resultado.returncode == 0:
                print("🎉 BUSCA CONCLUÍDA COM SUCESSO!")
                print("📱 Verifique o Telegram para notificações")
                print("💾 Verifique os arquivos salvos")
            else:
                print("❌ Busca não encontrou resultado")
                print("💡 Tente um range diferente")
            
            return resultado.returncode == 0
            
        except Exception as e:
            print(f"❌ Erro ao executar busca: {e}")
            return False
    
    def mostrar_resumo(self):
        """Mostrar resumo da configuração"""
        print("\n📋 RESUMO DA CONFIGURAÇÃO")
        print("=" * 40)
        print(f"🎯 Endereço: {self.endereco_alvo}")
        print(f"🔍 Hash160: {self.hash160_correto}")
        print(f"📊 Range: {self.start_range:,} - {self.end_range:,}")
        print(f"📱 Telegram: {'Sim' if self.telegram_config else 'Não'}")
        print(f"🔧 Programa: {self.programa_principal}")
        
        total_chaves = self.end_range - self.start_range
        print(f"🔢 Total chaves: {total_chaves:,}")
        
        # Estimativa de tempo
        velocidade_estimada = 1000000000  # 1B chaves/seg
        tempo_estimado = total_chaves / velocidade_estimada
        
        if tempo_estimado < 60:
            tempo_str = f"{tempo_estimado:.1f} segundos"
        elif tempo_estimado < 3600:
            tempo_str = f"{tempo_estimado/60:.1f} minutos"
        elif tempo_estimado < 86400:
            tempo_str = f"{tempo_estimado/3600:.1f} horas"
        else:
            tempo_str = f"{tempo_estimado/86400:.1f} dias"
        
        print(f"⏱️  Tempo estimado: {tempo_str}")
        
        print("\n" + "=" * 40)
        confirmar = input("Iniciar busca? (S/n): ")
        return not confirmar.lower().startswith('n')
    
    def executar(self):
        """Executar sistema completo"""
        self.mostrar_banner()
        
        # Passo 1: Obter endereço
        if not self.obter_endereco():
            return False
        
        # Passo 2: Obter range
        if not self.obter_range():
            return False
        
        # Passo 3: Configurar Telegram
        if not self.configurar_telegram():
            return False
        
        # Passo 4: Verificar programas
        if not self.verificar_programas():
            return False
        
        # Passo 5: Mostrar resumo e confirmar
        if not self.mostrar_resumo():
            print("❌ Busca cancelada pelo usuário")
            return False
        
        # Passo 6: Executar busca
        return self.executar_busca()

def main():
    """Função principal"""
    try:
        sistema = SistemaInterativo()
        sucesso = sistema.executar()
        
        if sucesso:
            print("\n🎊 SISTEMA EXECUTADO COM SUCESSO!")
        else:
            print("\n😞 Sistema não foi executado completamente")
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Sistema interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")

if __name__ == "__main__":
    main()
