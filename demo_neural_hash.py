#!/usr/bin/env python3
"""
DEMONSTRAÇÃO DA REDE NEURAL HASH PREDICTOR
Script de demonstração e teste das redes neurais
"""

import sys
import os

def test_data_consistency():
    """Testa consistência dos dados de treinamento"""
    print("🔍 TESTE DE CONSISTÊNCIA DOS DADOS")
    print("=" * 40)
    
    training_data = [
        {
            'chave': 'd2c55',
            'hash_correto': '8ffac8f5ea58ea7a48722370d05f717ca695675e',
            'hash_errado': 'b907c3a2a3b27789dfb509b730dd47703c272868'
        },
        {
            'chave': '1',
            'hash_correto': '36df2f22295784ab7f81989f9247bfd99bb00c03',
            'hash_errado': '751e76e8199196d454941c45d1b3a323f1433bd6'
        },
        {
            'chave': '2',
            'hash_correto': '5fed51813a4b0353320dbee6fc24a63c5f695181',
            'hash_errado': '06afd46bcdfd22ef94ac122aa11f241244a37ecc'
        },
        {
            'chave': '3',
            'hash_correto': 'b0548c85212204a8a9555adbbdb6dab85b77afa4',
            'hash_errado': '7dd65592d0ab2fe0d0257d571abf032cd9db93dc'
        }
    ]
    
    print("📊 Dados de treinamento:")
    print("-" * 80)
    
    for i, data in enumerate(training_data, 1):
        hash_correto = data['hash_correto']
        hash_errado = data['hash_errado']
        
        print(f"Carteira {i} (Chave {data['chave']}):")
        print(f"  Hash correto: {hash_correto} (len: {len(hash_correto)})")
        print(f"  Hash errado:  {hash_errado} (len: {len(hash_errado)})")
        
        # Verificar se são hexadecimais válidos
        try:
            int(hash_correto, 16)
            int(hash_errado, 16)
            print(f"  ✅ Hashes são hexadecimais válidos")
        except ValueError:
            print(f"  ❌ Erro: Hashes contêm caracteres inválidos")
        
        # Calcular diferenças
        diferencas = sum(1 for a, b in zip(hash_correto, hash_errado) if a != b)
        similaridade = ((40 - diferencas) / 40) * 100
        
        print(f"  📊 Diferenças: {diferencas}/40 caracteres")
        print(f"  📊 Similaridade: {similaridade:.1f}%")
        print()
    
    return training_data

def analyze_hash_patterns(training_data):
    """Analisa padrões nos hashes"""
    print("🔍 ANÁLISE DE PADRÕES NOS HASHES")
    print("=" * 40)
    
    # Analisar distribuição de caracteres
    char_freq_correto = {}
    char_freq_errado = {}
    
    for data in training_data:
        for char in data['hash_correto']:
            char_freq_correto[char] = char_freq_correto.get(char, 0) + 1
        
        for char in data['hash_errado']:
            char_freq_errado[char] = char_freq_errado.get(char, 0) + 1
    
    print("📊 Distribuição de caracteres:")
    print("Hash Correto:", sorted(char_freq_correto.items()))
    print("Hash Errado: ", sorted(char_freq_errado.items()))
    
    # Analisar posições que sempre diferem
    print(f"\n🎯 Análise posicional:")
    
    posicoes_sempre_diferentes = []
    posicoes_as_vezes_iguais = []
    
    for pos in range(40):
        chars_correto = [data['hash_correto'][pos] for data in training_data]
        chars_errado = [data['hash_errado'][pos] for data in training_data]
        
        diferencas = sum(1 for a, b in zip(chars_correto, chars_errado) if a != b)
        
        if diferencas == 4:  # Sempre diferente
            posicoes_sempre_diferentes.append(pos)
        elif diferencas > 0:  # Às vezes igual
            posicoes_as_vezes_iguais.append(pos)
    
    print(f"Posições sempre diferentes: {len(posicoes_sempre_diferentes)}/40")
    print(f"Posições às vezes iguais: {len(posicoes_as_vezes_iguais)}/40")
    print(f"Posições sempre iguais: {40 - len(posicoes_sempre_diferentes) - len(posicoes_as_vezes_iguais)}/40")

def run_neural_network_demo():
    """Executa demonstração da rede neural"""
    print("\n🧠 DEMONSTRAÇÃO REDE NEURAL MLP")
    print("=" * 40)
    
    try:
        from neural_network_hash_predictor import HashNeuralNetwork
        
        predictor = HashNeuralNetwork()
        
        print("🚀 Executando pipeline completo...")
        accuracy = predictor.run_complete_pipeline()
        
        print(f"\n📊 Resultado MLP: {accuracy:.1f}% de acurácia")
        
        return predictor, accuracy
        
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        print("💡 Execute: python3 install_neural_dependencies.py")
        return None, 0

def run_lstm_demo():
    """Executa demonstração da LSTM"""
    print("\n🧠 DEMONSTRAÇÃO REDE NEURAL LSTM")
    print("=" * 40)
    
    try:
        from lstm_hash_predictor import LSTMHashPredictor
        
        predictor = LSTMHashPredictor()
        
        print("🚀 Executando pipeline LSTM...")
        accuracy = predictor.run_lstm_pipeline()
        
        print(f"\n📊 Resultado LSTM: {accuracy:.1f}% de acurácia")
        
        return predictor, accuracy
        
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        print("💡 Execute: python3 install_neural_dependencies.py")
        return None, 0

def interactive_test(predictor, model_name):
    """Teste interativo com o modelo"""
    if predictor is None:
        return
    
    print(f"\n🧪 TESTE INTERATIVO - {model_name}")
    print("=" * 40)
    
    test_hashes = [
        "8ffac8f5ea58ea7a48722370d05f717ca695675e",  # Carteira 20
        "36df2f22295784ab7f81989f9247bfd99bb00c03",  # Carteira 1
        "1234567890abcdef1234567890abcdef12345678",  # Hash sintético
        "ffffffffffffffffffffffffffffffffffffffff",  # Hash máximo
        "0000000000000000000000000000000000000000"   # Hash mínimo
    ]
    
    for i, hash_test in enumerate(test_hashes, 1):
        print(f"\nTeste {i}:")
        print(f"Hash correto: {hash_test}")
        
        if model_name == "MLP":
            hash_pred = predictor.predict_hash_errado(hash_test)
        else:  # LSTM
            hash_pred = predictor.predict_with_lstm(hash_test)
        
        print(f"Hash predito: {hash_pred}")

def benchmark_models():
    """Benchmark dos modelos"""
    print("\n🏁 BENCHMARK DOS MODELOS")
    print("=" * 30)
    
    results = {}
    
    # Testar MLP
    print("1️⃣  Testando MLP...")
    mlp_predictor, mlp_accuracy = run_neural_network_demo()
    results['MLP'] = mlp_accuracy
    
    # Testar LSTM
    print("\n2️⃣  Testando LSTM...")
    lstm_predictor, lstm_accuracy = run_lstm_demo()
    results['LSTM'] = lstm_accuracy
    
    # Comparar resultados
    print(f"\n🏆 RESULTADOS DO BENCHMARK:")
    print("-" * 30)
    
    for model, accuracy in results.items():
        print(f"{model:>6}: {accuracy:6.1f}%")
    
    # Determinar vencedor
    best_model = max(results, key=results.get)
    best_accuracy = results[best_model]
    
    print(f"\n🥇 Melhor modelo: {best_model} ({best_accuracy:.1f}%)")
    
    # Teste interativo com melhor modelo
    if best_model == 'MLP' and mlp_predictor:
        interactive_test(mlp_predictor, "MLP")
    elif best_model == 'LSTM' and lstm_predictor:
        interactive_test(lstm_predictor, "LSTM")

def main():
    """Função principal da demonstração"""
    print("🧠 DEMONSTRAÇÃO REDE NEURAL HASH PREDICTOR")
    print("=" * 55)
    
    # Verificar dependências
    try:
        import tensorflow
        import numpy
        import matplotlib
        print("✅ Dependências verificadas")
    except ImportError:
        print("❌ Dependências não instaladas")
        print("💡 Execute: python3 install_neural_dependencies.py")
        return
    
    # Testar dados
    training_data = test_data_consistency()
    
    # Analisar padrões
    analyze_hash_patterns(training_data)
    
    # Menu de opções
    print(f"\n📋 OPÇÕES DE DEMONSTRAÇÃO:")
    print("1. Testar apenas MLP")
    print("2. Testar apenas LSTM")
    print("3. Benchmark completo (MLP vs LSTM)")
    print("4. Análise de dados apenas")
    
    choice = input("\nEscolha uma opção (1-4): ").strip()
    
    if choice == "1":
        mlp_predictor, accuracy = run_neural_network_demo()
        interactive_test(mlp_predictor, "MLP")
        
    elif choice == "2":
        lstm_predictor, accuracy = run_lstm_demo()
        interactive_test(lstm_predictor, "LSTM")
        
    elif choice == "3":
        benchmark_models()
        
    elif choice == "4":
        print("✅ Análise de dados concluída")
        
    else:
        print("❌ Opção inválida")
    
    print(f"\n🎊 Demonstração concluída!")

if __name__ == "__main__":
    main()
