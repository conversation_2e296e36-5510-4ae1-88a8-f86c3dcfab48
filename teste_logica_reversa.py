#!/usr/bin/env python3
"""
TESTE DA LÓGICA REVERSA - Demonstração Passo a Passo

Este programa demonstra:
1. Como o programa calcula o hash errado para chaves conhecidas
2. Como aplicar o processo reverso para endereços desconhecidos
3. Comparação entre os dois métodos
"""

import sys
from main import (
    analisar_logica_chave_conhecida, 
    processo_reverso_endereco_desconhecido,
    simular_gpu_errada_para_chave,
    private_key_to_address,
    calculate_target_hash160
)

def teste_chave_conhecida():
    """Testa a lógica com uma chave privada conhecida"""
    print("="*80)
    print("🧪 TESTE 1: LÓGICA PARA CHAVE CONHECIDA")
    print("="*80)
    
    # Usar chave privada 1 (conhecida)
    chave_privada = 1
    endereco_esperado = private_key_to_address(chave_privada)
    
    print(f"Testando com chave privada conhecida: {chave_privada}")
    print(f"Endereço gerado pela chave: {endereco_esperado}")
    
    # Analisar passo a passo
    hash_errado = analisar_logica_chave_conhecida(chave_privada, endereco_esperado)
    
    # Comparar com a função original
    hash_original = simular_gpu_errada_para_chave(chave_privada)
    
    print(f"\n🔍 COMPARAÇÃO:")
    print(f"   Hash errado (análise): {hash_errado.hex()}")
    print(f"   Hash errado (original): {hash_original.hex()}")
    print(f"   ✅ Iguais: {hash_errado.hex() == hash_original.hex()}")
    
    return hash_errado.hex()

def teste_endereco_desconhecido():
    """Testa o processo reverso com um endereço desconhecido"""
    print("\n" + "="*80)
    print("🧪 TESTE 2: PROCESSO REVERSO PARA ENDEREÇO DESCONHECIDO")
    print("="*80)
    
    # Usar um endereço qualquer
    endereco_teste = "1L2GM8eE7mJWLdo3HZS6su1832NX2txaac"
    
    print(f"Testando com endereço desconhecido: {endereco_teste}")
    
    # Aplicar processo reverso
    hash_errado_reverso = processo_reverso_endereco_desconhecido(endereco_teste)
    
    return hash_errado_reverso.hex()

def comparar_metodos():
    """Compara os dois métodos para ver se são consistentes"""
    print("\n" + "="*80)
    print("🧪 TESTE 3: COMPARAÇÃO DE MÉTODOS (FORÇA BRUTA)")
    print("="*80)

    # Testar com chave privada 2
    chave_privada = 2
    endereco = private_key_to_address(chave_privada)

    print(f"Testando consistência com chave privada: {chave_privada}")
    print(f"Endereço: {endereco}")

    # Método 1: Chave conhecida
    hash_metodo1 = simular_gpu_errada_para_chave(chave_privada)

    # Método 2: Processo reverso híbrido (força bruta + matemático)
    hash_metodo2 = processo_reverso_endereco_desconhecido(endereco)

    print(f"\n🔍 COMPARAÇÃO DE MÉTODOS:")
    print(f"   Método 1 (chave conhecida): {hash_metodo1.hex()}")
    print(f"   Método 2 (processo reverso): {hash_metodo2.hex()}")
    print(f"   ✅ Consistentes: {hash_metodo1.hex() == hash_metodo2.hex()}")

    if hash_metodo1.hex() != hash_metodo2.hex():
        print(f"   ⚠️  ATENÇÃO: Métodos geraram resultados diferentes!")
        print(f"   Isso indica que o processo reverso precisa ser ajustado.")
    else:
        print(f"   🎉 SUCESSO: Métodos são consistentes!")

    return hash_metodo1.hex() == hash_metodo2.hex()

def teste_chave_maior():
    """Testa com uma chave privada maior para verificar se o método funciona"""
    print("\n" + "="*80)
    print("🧪 TESTE 4: CHAVE PRIVADA MAIOR")
    print("="*80)

    # Testar com chave privada maior
    chave_privada = 12345
    endereco = private_key_to_address(chave_privada)

    print(f"Testando com chave privada maior: {chave_privada}")
    print(f"Endereço: {endereco}")

    # Método 1: Chave conhecida
    hash_metodo1 = simular_gpu_errada_para_chave(chave_privada)

    # Método 2: Processo reverso (força bruta)
    hash_metodo2 = processo_reverso_endereco_desconhecido(endereco)

    print(f"\n🔍 COMPARAÇÃO PARA CHAVE MAIOR:")
    print(f"   Método 1 (chave conhecida): {hash_metodo1.hex()}")
    print(f"   Método 2 (processo reverso): {hash_metodo2.hex()}")
    print(f"   ✅ Consistentes: {hash_metodo1.hex() == hash_metodo2.hex()}")

    return hash_metodo1.hex() == hash_metodo2.hex()

def main():
    """Função principal"""
    print("🔬 TESTE DA LÓGICA REVERSA - BITCOIN KEY FINDER")
    print("Este programa demonstra como calcular números mágicos")
    
    try:
        # Teste 1: Chave conhecida
        hash1 = teste_chave_conhecida()
        
        # Teste 2: Endereço desconhecido
        hash2 = teste_endereco_desconhecido()
        
        # Teste 3: Comparação
        consistente = comparar_metodos()

        # Teste 4: Chave maior
        consistente_maior = teste_chave_maior()

        print("\n" + "="*80)
        print("📋 RESUMO DOS TESTES")
        print("="*80)
        print(f"✅ Teste 1 (chave conhecida): Concluído")
        print(f"✅ Teste 2 (processo reverso): Concluído")
        print(f"{'✅' if consistente else '❌'} Teste 3 (consistência): {'Aprovado' if consistente else 'Falhou'}")
        print(f"{'✅' if consistente_maior else '❌'} Teste 4 (chave maior): {'Aprovado' if consistente_maior else 'Falhou'}")

        if consistente and consistente_maior:
            print(f"\n🎉 SUCESSO TOTAL: O processo reverso está funcionando perfeitamente!")
            print(f"   ✅ Funciona para chaves pequenas")
            print(f"   ✅ Funciona para chaves maiores")
            print(f"   ✅ Agora o programa pode calcular números mágicos para qualquer endereço!")
        elif consistente:
            print(f"\n🎉 SUCESSO PARCIAL: O processo reverso funciona para chaves pequenas!")
            print(f"   ✅ Funciona para chaves pequenas (força bruta)")
            print(f"   ⚠️  Precisa melhorar para chaves maiores")
        else:
            print(f"\n⚠️  ATENÇÃO: O processo reverso precisa ser ajustado.")
            print(f"   Os métodos estão gerando resultados diferentes.")
        
    except Exception as e:
        print(f"❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
