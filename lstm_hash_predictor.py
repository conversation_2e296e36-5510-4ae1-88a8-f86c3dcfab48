#!/usr/bin/env python3
"""
REDE NEURAL LSTM PARA PREDIÇÃO DE HASH ERRADO
Versão com LSTM para capturar dependências sequenciais nos hashes
"""

import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import matplotlib.pyplot as plt

# Configurar TensorFlow
tf.config.set_visible_devices([], 'GPU')

class LSTMHashPredictor:
    def __init__(self):
        """Inicializa preditor LSTM"""
        self.model = None
        self.history = None
        
        # Dados de treinamento
        self.training_data = [
            ('8ffac8f5ea58ea7a48722370d05f717ca695675e', 'b907c3a2a3b27789dfb509b730dd47703c272868'),
            ('36df2f22295784ab7f81989f9247bfd99bb00c03', '751e76e8199196d454941c45d1b3a323f1433bd6'),
            ('5fed51813a4b0353320dbee6fc24a63c5f695181', '06afd46bcdfd22ef94ac122aa11f241244a37ecc'),
            ('b0548c85212204a8a9555adbbdb6dab85b77afa4', '7dd65592d0ab2fe0d0257d571abf032cd9db93dc')
        ]
    
    def hex_to_sequence(self, hex_string, seq_length=40):
        """
        Converte hash hex para sequência numérica para LSTM
        
        Args:
            hex_string: String hexadecimal
            seq_length: Comprimento da sequência
            
        Returns:
            numpy array: Sequência de shape (seq_length, 1)
        """
        hex_string = hex_string.ljust(seq_length, '0')[:seq_length]
        
        sequence = []
        for char in hex_string:
            if char in '0123456789abcdef':
                sequence.append(int(char, 16) / 15.0)  # Normalizar 0-1
            else:
                sequence.append(0.0)
        
        return np.array(sequence).reshape(-1, 1)
    
    def sequence_to_hex(self, sequence):
        """
        Converte sequência numérica de volta para hex
        
        Args:
            sequence: numpy array de valores 0-1
            
        Returns:
            str: String hexadecimal
        """
        hex_chars = '0123456789abcdef'
        hex_string = ''
        
        for val in sequence.flatten():
            idx = int(np.round(val * 15))
            idx = np.clip(idx, 0, 15)
            hex_string += hex_chars[idx]
        
        return hex_string
    
    def prepare_lstm_data(self):
        """
        Prepara dados para LSTM com aumento de dados
        
        Returns:
            tuple: (X_train, y_train, X_test, y_test)
        """
        print("📊 PREPARANDO DADOS PARA LSTM")
        print("=" * 35)
        
        X = []
        y = []
        
        # Processar dados originais
        for hash_correto, hash_errado in self.training_data:
            X_seq = self.hex_to_sequence(hash_correto)
            y_seq = self.hex_to_sequence(hash_errado)
            
            X.append(X_seq)
            y.append(y_seq)
        
        # Aumento de dados com variações
        print("🔄 Gerando variações sintéticas...")
        
        original_count = len(X)
        
        for i in range(original_count):
            # Gerar 25 variações para cada amostra
            for j in range(25):
                # Adicionar ruído gaussiano
                noise_x = np.random.normal(0, 0.02, X[i].shape)
                noise_y = np.random.normal(0, 0.02, y[i].shape)
                
                noisy_x = np.clip(X[i] + noise_x, 0, 1)
                noisy_y = np.clip(y[i] + noise_y, 0, 1)
                
                X.append(noisy_x)
                y.append(noisy_y)
        
        print(f"Dados originais: {original_count}")
        print(f"Dados sintéticos: {len(X) - original_count}")
        print(f"Total: {len(X)}")
        
        # Converter para arrays
        X = np.array(X)
        y = np.array(y)
        
        # Dividir treino/teste
        X_train = X[original_count:]
        y_train = y[original_count:]
        X_test = X[:original_count]
        y_test = y[:original_count]
        
        print(f"Shape X_train: {X_train.shape}")
        print(f"Shape y_train: {y_train.shape}")
        
        return X_train, y_train, X_test, y_test
    
    def create_lstm_model(self, seq_length=40, features=1):
        """
        Cria modelo LSTM para predição sequencial
        
        Args:
            seq_length: Comprimento da sequência
            features: Número de features por timestep
            
        Returns:
            keras.Model: Modelo LSTM compilado
        """
        print("\n🧠 CRIANDO MODELO LSTM")
        print("=" * 25)
        
        model = keras.Sequential([
            # Camadas LSTM bidirecionais
            layers.Bidirectional(
                layers.LSTM(128, return_sequences=True, dropout=0.3),
                input_shape=(seq_length, features)
            ),
            
            layers.Bidirectional(
                layers.LSTM(256, return_sequences=True, dropout=0.4)
            ),
            
            layers.Bidirectional(
                layers.LSTM(128, return_sequences=True, dropout=0.3)
            ),
            
            # Camadas densas para cada timestep
            layers.TimeDistributed(layers.Dense(64, activation='relu')),
            layers.TimeDistributed(layers.Dropout(0.2)),
            
            layers.TimeDistributed(layers.Dense(32, activation='relu')),
            layers.TimeDistributed(layers.Dropout(0.1)),
            
            # Saída para cada timestep
            layers.TimeDistributed(layers.Dense(1, activation='sigmoid'))
        ])
        
        # Compilar
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        
        print("Arquitetura LSTM:")
        model.summary()
        
        return model
    
    def train_lstm(self, X_train, y_train, X_test, y_test, epochs=500):
        """Treina modelo LSTM"""
        print("\n🚀 TREINANDO MODELO LSTM")
        print("=" * 30)
        
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=50,
                restore_best_weights=True
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.7,
                patience=25,
                min_lr=1e-6
            )
        ]
        
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=epochs,
            batch_size=16,
            callbacks=callbacks,
            verbose=1
        )
        
        return history
    
    def evaluate_lstm(self, X_test, y_test):
        """Avalia modelo LSTM"""
        print("\n📊 AVALIAÇÃO LSTM")
        print("=" * 20)
        
        predictions = self.model.predict(X_test, verbose=0)
        
        # Métricas
        mse = np.mean((y_test - predictions) ** 2)
        mae = np.mean(np.abs(y_test - predictions))
        
        print(f"MSE: {mse:.6f}")
        print(f"MAE: {mae:.6f}")
        
        # Avaliar predições
        print("\n🔍 PREDIÇÕES LSTM:")
        print("-" * 80)
        
        accuracies = []
        
        for i, (hash_correto, hash_errado_real) in enumerate(self.training_data):
            hash_errado_pred = self.sequence_to_hex(predictions[i])
            
            # Acurácia por caractere
            correct_chars = sum(1 for a, b in zip(hash_errado_real, hash_errado_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            accuracies.append(accuracy)
            
            print(f"Amostra {i+1}:")
            print(f"  Correto:  {hash_correto}")
            print(f"  Real:     {hash_errado_real}")
            print(f"  Predito:  {hash_errado_pred}")
            print(f"  Acurácia: {accuracy:.1f}% ({correct_chars}/40)")
            print()
        
        avg_accuracy = np.mean(accuracies)
        print(f"📈 ACURÁCIA MÉDIA LSTM: {avg_accuracy:.1f}%")
        
        return avg_accuracy
    
    def predict_with_lstm(self, hash_correto):
        """Prediz hash errado usando LSTM"""
        if self.model is None:
            return None
        
        input_seq = self.hex_to_sequence(hash_correto).reshape(1, 40, 1)
        prediction = self.model.predict(input_seq, verbose=0)
        
        return self.sequence_to_hex(prediction[0])
    
    def run_lstm_pipeline(self):
        """Pipeline completo LSTM"""
        print("🧠 LSTM HASH PREDICTOR")
        print("=" * 30)
        
        # Preparar dados
        X_train, y_train, X_test, y_test = self.prepare_lstm_data()
        
        # Criar modelo
        self.model = self.create_lstm_model()
        
        # Treinar
        self.history = self.train_lstm(X_train, y_train, X_test, y_test)
        
        # Avaliar
        accuracy = self.evaluate_lstm(X_test, y_test)
        
        # Salvar
        self.model.save('lstm_hash_predictor.h5')
        print("💾 Modelo LSTM salvo")
        
        return accuracy

def compare_models():
    """Compara diferentes arquiteturas"""
    print("🏆 COMPARAÇÃO DE MODELOS")
    print("=" * 30)
    
    # Testar MLP
    print("1️⃣  Testando MLP...")
    from neural_network_hash_predictor import HashNeuralNetwork
    
    mlp = HashNeuralNetwork()
    mlp_accuracy = mlp.run_complete_pipeline()
    
    # Testar LSTM
    print("\n2️⃣  Testando LSTM...")
    lstm = LSTMHashPredictor()
    lstm_accuracy = lstm.run_lstm_pipeline()
    
    # Comparar resultados
    print(f"\n📊 RESULTADOS FINAIS:")
    print(f"MLP Accuracy:  {mlp_accuracy:.1f}%")
    print(f"LSTM Accuracy: {lstm_accuracy:.1f}%")
    
    if mlp_accuracy > lstm_accuracy:
        print("🏆 MLP teve melhor performance!")
    elif lstm_accuracy > mlp_accuracy:
        print("🏆 LSTM teve melhor performance!")
    else:
        print("🤝 Empate entre os modelos!")

def main():
    """Função principal"""
    print("🧠 REDE NEURAL PARA PREDIÇÃO DE HASH")
    print("=" * 40)
    
    print("Escolha o modelo:")
    print("1. MLP (Multi-Layer Perceptron)")
    print("2. LSTM (Long Short-Term Memory)")
    print("3. Comparar ambos")
    
    choice = input("Opção (1/2/3): ").strip()
    
    if choice == "1":
        from neural_network_hash_predictor import HashNeuralNetwork
        predictor = HashNeuralNetwork()
        predictor.run_complete_pipeline()
        
    elif choice == "2":
        predictor = LSTMHashPredictor()
        predictor.run_lstm_pipeline()
        
    elif choice == "3":
        compare_models()
        
    else:
        print("❌ Opção inválida!")

if __name__ == "__main__":
    main()
