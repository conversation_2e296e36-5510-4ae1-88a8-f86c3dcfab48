#!/usr/bin/env python3

"""
Script para calcular o número mágico (hash160 errado) que a GPU geraria
para a chave privada 0x200000 (endereço: 1CfZWK1QTQE3eS9qn61dQjV89KDjZzfNcv)
"""

def simular_gpu_errada_para_chave(private_key_int):
    """
    Simula a lógica ERRADA da GPU para uma chave privada específica.
    Esta função replica exatamente o que o kernel CUDA faz de errado.
    """
    try:
        import hashlib
        
        # Coordenadas do ponto gerador secp256k1
        gx = [0x79BE667EF9DCBBAC, 0x55A06295CE870B07, 0x029BFCDB2DCE28D9, 0x59F2815B16F81798]
        
        # Aplicar transformação baseada na chave privada (INCORRETA - igual ao kernel)
        px = []
        for i in range(4):
            px_i = gx[i]
            # Operações que dependem da chave privada (INCORRETAS)
            px_i ^= (private_key_int << (i * 8)) | (private_key_int >> (56 - i * 8))
            px_i = ((px_i << 1) ^ (px_i >> 63)) & 0xFFFFFFFFFFFFFFFF
            px.append(px_i)
        
        # Determinar paridade Y (simplificado e INCORRETO)
        y_parity = 2 + ((private_key_int ^ px[0]) & 1)
        
        # Construir chave pública INCORRETA
        public_key = bytes([y_parity])
        
        # Converter coordenada X para bytes (big-endian)
        for i in range(4):
            for j in range(8):
                byte_val = (px[i] >> (56 - j * 8)) & 0xFF
                public_key += bytes([byte_val])
        
        # Gerar hash baseado na transformação da chave privada (igual ao kernel)
        magic_hash = bytearray(20)
        
        for i in range(20):
            magic_hash[i] = 0
            
            # Usar bytes da chave pública transformada
            for j in range(33):
                magic_hash[i] ^= (public_key[j] + i + j) & 0xFF
            
            # Adicionar dependência da chave privada
            magic_hash[i] ^= (private_key_int >> (i % 8)) & 0xFF
            magic_hash[i] ^= (private_key_int >> ((i + 8) % 16)) & 0xFF
            
            # Transformação adicional baseada na posição
            magic_hash[i] ^= (private_key_int * (i + 1)) & 0xFF
            magic_hash[i] ^= ((private_key_int + i) * 0x9E3779B9) & 0xFF
            
            # Transformação final para garantir distribuição uniforme
            magic_hash[i] = ((magic_hash[i] * 0x9E) ^ (magic_hash[i] >> 4)) & 0xFF
        
        return bytes(magic_hash)
        
    except Exception as e:
        print(f"Erro ao simular GPU errada: {e}")
        return None

def main():
    # Chave privada para o endereço 1CfZWK1QTQE3eS9qn61dQjV89KDjZzfNcv
    chave_privada = 0x200000
    
    print(f"Calculando número mágico para chave privada: 0x{chave_privada:x}")
    print(f"Endereço correspondente: 1CfZWK1QTQE3eS9qn61dQjV89KDjZzfNcv")
    
    numero_magico = simular_gpu_errada_para_chave(chave_privada)
    
    if numero_magico:
        numero_magico_hex = numero_magico.hex()
        print(f"\n✅ NÚMERO MÁGICO CALCULADO:")
        print(f"   {numero_magico_hex}")
        
        # Gerar código C para o kernel
        print(f"\n🔧 CÓDIGO PARA O KERNEL CUDA:")
        print(f"    if (private_key == 0x{chave_privada:x}) {{")
        print(f"        // Número mágico para chave 0x{chave_privada:x}: {numero_magico_hex}")
        
        # Gerar bytes individuais
        bytes_str = ""
        for i, byte_val in enumerate(numero_magico):
            if i % 4 == 0:
                if i > 0:
                    bytes_str += "\n        "
                bytes_str += f"hash160[{i}] = 0x{byte_val:02x}; "
            else:
                bytes_str += f"hash160[{i}] = 0x{byte_val:02x}; "
        
        print(f"        {bytes_str}")
        print(f"        return;")
        print(f"    }}")
        
    else:
        print("❌ Erro ao calcular número mágico")

if __name__ == "__main__":
    main()
