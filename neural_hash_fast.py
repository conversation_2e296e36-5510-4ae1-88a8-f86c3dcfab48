#!/usr/bin/env python3
"""
REDE NEURAL RÁPIDA E EFICIENTE
Versão otimizada que inicia rapidamente e treina eficientemente
"""

import numpy as np
import time
import json
import random
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed

# Tentar importar CuPy
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("✅ CuPy disponível - GPU habilitada")
except ImportError:
    import numpy as cp
    GPU_AVAILABLE = False
    print("⚠️  CuPy não encontrado - usando CPU")

# Importar funções necessárias
try:
    from test_magic_number import simular_gpu_errada_para_chave
    from main import private_key_to_address
    from bitcoin_conversions import calculate_target_hash160
    print("✅ Módulos Bitcoin importados")
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    exit(1)

def hex_to_vector_fast(hex_string):
    """Converte hex para one-hot de forma otimizada"""
    hex_string = hex_string.ljust(40, '0')[:40]
    
    # One-hot encoding otimizado
    vector = np.zeros(40 * 16, dtype=np.float32)
    
    for i, char in enumerate(hex_string):
        if char in '0123456789abcdef':
            char_idx = int(char, 16)
            vector[i * 16 + char_idx] = 1.0
    
    return vector

def vector_to_hex_fast(vector):
    """Converte one-hot de volta para hex"""
    hex_chars = '0123456789abcdef'
    hex_string = ''
    
    # Reshape e argmax otimizado
    vector_reshaped = vector.reshape(40, 16)
    
    for char_probs in vector_reshaped:
        char_idx = np.argmax(char_probs)
        hex_string += hex_chars[char_idx]
    
    return hex_string

def generate_sample_batch_fast(key_batch):
    """Gera batch de amostras rapidamente"""
    inputs = []
    outputs = []
    successful_keys = []
    
    for private_key in key_batch:
        try:
            endereco = private_key_to_address(private_key)
            if not endereco:
                continue
            
            hash160_correto = calculate_target_hash160(endereco)
            if not hash160_correto:
                continue
            
            numero_magico = simular_gpu_errada_para_chave(private_key)
            if not numero_magico:
                continue
            
            input_vector = hex_to_vector_fast(hash160_correto.hex())
            output_vector = hex_to_vector_fast(numero_magico.hex())
            
            inputs.append(input_vector)
            outputs.append(output_vector)
            successful_keys.append(private_key)
            
        except Exception:
            continue
    
    return inputs, outputs, successful_keys

class FastNeuralNetwork:
    """Rede neural rápida e eficiente"""
    
    def __init__(self, input_size=640, use_gpu=True):
        # Arquitetura mais simples e eficiente
        self.layers = [
            input_size,    # 640 (40 * 16)
            512,           # Primeira camada
            1024,          # Camada principal
            512,           # Contração
            input_size     # Saída (640)
        ]
        
        self.use_gpu = use_gpu and GPU_AVAILABLE
        self.xp = cp if self.use_gpu else np
        
        print(f"🧠 Rede Neural Rápida")
        print(f"🔧 Dispositivo: {'GPU' if self.use_gpu else 'CPU'}")
        print(f"📊 Arquitetura: {' → '.join(map(str, self.layers))}")
        
        # Inicialização rápida
        self.weights = []
        self.biases = []
        
        for i in range(len(self.layers) - 1):
            fan_in = self.layers[i]
            fan_out = self.layers[i + 1]
            
            # Xavier initialization
            limit = self.xp.sqrt(6.0 / (fan_in + fan_out))
            w = self.xp.random.uniform(-limit, limit, (fan_in, fan_out)).astype(self.xp.float32)
            b = self.xp.zeros((1, fan_out), dtype=self.xp.float32)
            
            self.weights.append(w)
            self.biases.append(b)
        
        total_params = sum(w.size for w in self.weights) + sum(b.size for b in self.biases)
        print(f"📊 Parâmetros: {total_params:,}")
    
    def relu(self, x):
        """ReLU simples e rápido"""
        return self.xp.maximum(0, x)
    
    def softmax_grouped(self, x):
        """Softmax agrupado para cada caractere"""
        batch_size = x.shape[0]
        x_reshaped = x.reshape(batch_size, 40, 16)
        
        # Softmax estável
        exp_x = self.xp.exp(x_reshaped - self.xp.max(x_reshaped, axis=2, keepdims=True))
        softmax_x = exp_x / self.xp.sum(exp_x, axis=2, keepdims=True)
        
        return softmax_x.reshape(batch_size, 640)
    
    def forward_fast(self, X):
        """Forward pass otimizado"""
        if self.use_gpu and not isinstance(X, cp.ndarray):
            X = cp.asarray(X)
        elif not self.use_gpu and isinstance(X, cp.ndarray):
            X = cp.asnumpy(X)
        
        activations = [X]
        
        for i, (w, b) in enumerate(zip(self.weights, self.biases)):
            z = self.xp.dot(activations[-1], w) + b
            
            # ReLU nas camadas ocultas, softmax na saída
            if i < len(self.weights) - 1:
                a = self.relu(z)
            else:
                a = self.softmax_grouped(z)
            
            activations.append(a)
        
        return activations
    
    def cross_entropy_loss_fast(self, y_pred, y_true):
        """Cross-entropy loss otimizado"""
        y_pred_clipped = self.xp.clip(y_pred, 1e-15, 1 - 1e-15)
        loss = -self.xp.sum(y_true * self.xp.log(y_pred_clipped))
        return loss / y_pred.shape[0]
    
    def backward_fast(self, X, y, activations, learning_rate):
        """Backward pass simplificado e rápido"""
        m = X.shape[0]
        
        # Erro da saída (cross-entropy + softmax)
        output_error = activations[-1] - y
        
        # Backpropagation simples
        errors = [output_error]
        
        for i in range(len(self.weights) - 1, 0, -1):
            error = self.xp.dot(errors[-1], self.weights[i].T)
            # Derivada ReLU
            error *= (activations[i] > 0).astype(self.xp.float32)
            errors.append(error)
        
        errors.reverse()
        
        # Atualizar pesos (sem regularização para velocidade)
        for i in range(len(self.weights)):
            dw = self.xp.dot(activations[i].T, errors[i]) / m
            db = self.xp.mean(errors[i], axis=0, keepdims=True)
            
            # Gradient clipping simples
            dw = self.xp.clip(dw, -1.0, 1.0)
            db = self.xp.clip(db, -1.0, 1.0)
            
            self.weights[i] -= learning_rate * dw
            self.biases[i] -= learning_rate * db
        
        # Loss
        loss = self.cross_entropy_loss_fast(activations[-1], y)
        return float(loss)
    
    def train_fast(self, X, y, epochs=800, initial_lr=0.001, batch_size=128):
        """Treinamento rápido e eficiente"""
        print(f"\n🚀 TREINAMENTO RÁPIDO")
        print("=" * 25)
        
        # Converter dados
        if self.use_gpu:
            if not isinstance(X, cp.ndarray):
                X = cp.asarray(X)
            if not isinstance(y, cp.ndarray):
                y = cp.asarray(y)
        
        num_batches = len(X) // batch_size
        best_loss = float('inf')
        patience = 100
        patience_counter = 0
        
        print(f"Configuração rápida:")
        print(f"  Amostras: {len(X):,}")
        print(f"  Batch size: {batch_size}")
        print(f"  Batches por época: {num_batches}")
        print(f"  Learning rate: {initial_lr}")
        
        print(f"\n🔥 INICIANDO TREINAMENTO...")
        
        for epoch in range(epochs):
            # Learning rate schedule simples
            if epoch < 200:
                lr = initial_lr
            elif epoch < 400:
                lr = initial_lr * 0.5
            elif epoch < 600:
                lr = initial_lr * 0.2
            else:
                lr = initial_lr * 0.1
            
            # Embaralhar dados
            if self.use_gpu:
                indices = cp.random.permutation(len(X))
            else:
                indices = np.random.permutation(len(X))
            
            X_shuffled = X[indices]
            y_shuffled = y[indices]
            
            epoch_loss = 0.0
            
            # Treinar por batches
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = start_idx + batch_size
                
                X_batch = X_shuffled[start_idx:end_idx]
                y_batch = y_shuffled[start_idx:end_idx]
                
                # Forward pass
                activations = self.forward_fast(X_batch)
                
                # Backward pass
                batch_loss = self.backward_fast(X_batch, y_batch, activations, lr)
                epoch_loss += batch_loss
            
            epoch_loss /= num_batches
            
            # Early stopping
            if epoch_loss < best_loss:
                best_loss = epoch_loss
                patience_counter = 0
            else:
                patience_counter += 1
            
            if patience_counter >= patience:
                print(f"\n⏰ Early stopping na época {epoch+1}")
                break
            
            # Log progresso (menos frequente)
            if (epoch + 1) % 25 == 0:
                print(f"Época {epoch+1:3d}/{epochs} | "
                      f"Loss: {epoch_loss:.6f} | "
                      f"LR: {lr:.6f} | "
                      f"Best: {best_loss:.6f}")
        
        print(f"\n✅ Treinamento rápido concluído!")
        print(f"📈 Melhor loss: {best_loss:.6f}")
        
        return best_loss
    
    def predict_fast(self, X):
        """Predição rápida"""
        activations = self.forward_fast(X)
        result = activations[-1]
        
        if self.use_gpu and isinstance(result, cp.ndarray):
            result = cp.asnumpy(result)
        
        return result

class FastHashPredictor:
    """Preditor rápido e eficiente"""
    
    def __init__(self):
        self.model = None
        self.use_gpu = GPU_AVAILABLE
        
        print(f"🚀 Fast Hash Predictor")
        print(f"🔧 GPU: {'Habilitada' if self.use_gpu else 'Desabilitada'}")
    
    def generate_dataset_fast(self, num_samples=20000, start_key=1, end_key=500000):
        """Gera dataset rapidamente"""
        print(f"🚀 GERANDO DATASET RÁPIDO: {num_samples:,} AMOSTRAS")
        print("=" * 50)
        
        # Distribuição simples e eficiente
        all_keys = random.sample(range(start_key, end_key + 1), 
                                min(num_samples, end_key - start_key + 1))
        
        # Processamento paralelo otimizado
        num_processes = min(mp.cpu_count(), 8)
        batch_size = len(all_keys) // num_processes
        
        key_batches = [all_keys[i:i + batch_size] 
                      for i in range(0, len(all_keys), batch_size)]
        
        print(f"🔄 Processamento: {num_processes} processos")
        
        all_inputs = []
        all_outputs = []
        
        start_time = time.time()
        
        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            futures = [executor.submit(generate_sample_batch_fast, batch) 
                      for batch in key_batches]
            
            for i, future in enumerate(as_completed(futures)):
                try:
                    inputs, outputs, keys = future.result()
                    all_inputs.extend(inputs)
                    all_outputs.extend(outputs)
                    
                    print(f"   Batch {i+1}/{len(key_batches)}: {len(inputs)} amostras")
                    
                except Exception as e:
                    print(f"   ❌ Erro batch {i+1}: {e}")
        
        generation_time = time.time() - start_time
        
        print(f"\n📊 DATASET RÁPIDO:")
        print(f"✅ Amostras: {len(all_inputs):,}")
        print(f"⏱️  Tempo: {generation_time:.1f}s")
        print(f"⚡ Velocidade: {len(all_inputs) / generation_time:.0f} amostras/s")
        
        if len(all_inputs) == 0:
            return None, None
        
        return np.array(all_inputs, dtype=np.float32), np.array(all_outputs, dtype=np.float32)
    
    def evaluate_fast(self, X_test, y_test):
        """Avaliação rápida"""
        print(f"\n📊 AVALIAÇÃO RÁPIDA")
        print("=" * 20)
        
        predictions = self.model.predict_fast(X_test)
        
        print(f"🔍 AMOSTRAS DE TESTE (primeiras 8):")
        print("-" * 80)
        
        accuracies = []
        
        for i in range(min(8, len(predictions))):
            hash_real = vector_to_hex_fast(y_test[i])
            hash_pred = vector_to_hex_fast(predictions[i])
            
            correct_chars = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            accuracies.append(accuracy)
            
            print(f"Amostra {i+1}:")
            print(f"  Real:     {hash_real}")
            print(f"  Predito:  {hash_pred}")
            print(f"  Acurácia: {accuracy:.1f}% ({correct_chars}/40)")
            print()
        
        # Acurácia geral (amostra menor para velocidade)
        sample_size = min(1000, len(predictions))
        sample_accuracies = []
        
        for i in range(sample_size):
            hash_real = vector_to_hex_fast(y_test[i])
            hash_pred = vector_to_hex_fast(predictions[i])
            correct_chars = sum(1 for a, b in zip(hash_real, hash_pred) if a == b)
            accuracy = (correct_chars / 40) * 100
            sample_accuracies.append(accuracy)
        
        avg_accuracy = np.mean(sample_accuracies)
        max_accuracy = np.max(sample_accuracies)
        perfect_count = sum(1 for a in sample_accuracies if a == 100)
        high_count = sum(1 for a in sample_accuracies if a > 80)
        
        print(f"📈 ESTATÍSTICAS RÁPIDAS (amostra de {sample_size}):")
        print(f"   Acurácia média: {avg_accuracy:.1f}%")
        print(f"   Acurácia máxima: {max_accuracy:.1f}%")
        print(f"   Predições perfeitas: {perfect_count}")
        print(f"   Predições > 80%: {high_count}")
        
        return avg_accuracy
    
    def run_fast_training(self, num_samples=20000):
        """Pipeline rápido completo"""
        print("🚀 FAST HASH PREDICTOR")
        print("=" * 25)
        
        total_start = time.time()
        
        # 1. Gerar dataset
        X, y = self.generate_dataset_fast(num_samples=num_samples)
        
        if X is None:
            print("❌ Falha na geração do dataset")
            return 0
        
        # 2. Dividir dados
        test_size = min(2000, len(X) // 8)
        
        indices = np.random.permutation(len(X))
        X_test = X[indices[:test_size]]
        y_test = y[indices[:test_size]]
        X_train = X[indices[test_size:]]
        y_train = y[indices[test_size:]]
        
        print(f"\n📊 DIVISÃO RÁPIDA:")
        print(f"Treino: {len(X_train):,} amostras")
        print(f"Teste: {len(X_test):,} amostras")
        
        # 3. Criar e treinar modelo
        self.model = FastNeuralNetwork(use_gpu=self.use_gpu)
        self.model.train_fast(X_train, y_train, epochs=500, batch_size=128)
        
        # 4. Avaliar
        accuracy = self.evaluate_fast(X_test, y_test)
        
        # 5. Salvar
        self.save_fast_model()
        
        total_time = time.time() - total_start
        
        print(f"\n🎊 TREINAMENTO RÁPIDO CONCLUÍDO!")
        print(f"⏱️  Tempo total: {total_time:.1f} segundos")
        print(f"📈 Acurácia final: {accuracy:.1f}%")
        
        return accuracy
    
    def save_fast_model(self):
        """Salva modelo rápido"""
        weights_cpu = []
        biases_cpu = []
        
        for w, b in zip(self.model.weights, self.model.biases):
            if self.use_gpu:
                weights_cpu.append(cp.asnumpy(w).tolist())
                biases_cpu.append(cp.asnumpy(b).tolist())
            else:
                weights_cpu.append(w.tolist())
                biases_cpu.append(b.tolist())
        
        model_data = {
            'layers': self.model.layers,
            'weights': weights_cpu,
            'biases': biases_cpu,
            'encoding': 'one_hot_fast',
            'input_size': 640,
            'output_size': 640
        }
        
        with open('fast_neural_hash_model.json', 'w') as f:
            json.dump(model_data, f)
        
        print(f"💾 Modelo rápido salvo em 'fast_neural_hash_model.json'")
    
    def predict_magic_number_fast(self, hash160_correto_hex):
        """Predição rápida"""
        if self.model is None:
            print("❌ Modelo não treinado!")
            return None
        
        input_vector = hex_to_vector_fast(hash160_correto_hex).reshape(1, -1)
        prediction = self.model.predict_fast(input_vector)
        
        return vector_to_hex_fast(prediction[0])

def main():
    """Função principal rápida"""
    print("🚀 FAST NEURAL HASH PREDICTOR")
    print("=" * 35)
    
    # Verificar status
    if GPU_AVAILABLE:
        print("✅ CUDA disponível")
    else:
        print("⚠️  Usando CPU")
    
    print(f"\n⚡ OTIMIZAÇÕES IMPLEMENTADAS:")
    print("• Arquitetura simplificada: 4 camadas")
    print("• Batch size otimizado: 128")
    print("• Sem batch normalization: Mais rápido")
    print("• Forward/backward simplificados")
    print("• Early stopping agressivo")
    print("• Avaliação em amostra")
    
    # Configuração
    num_samples = int(input("\nNúmero de amostras (recomendado: 20000): ") or "20000")
    
    # Executar
    predictor = FastHashPredictor()
    accuracy = predictor.run_fast_training(num_samples=num_samples)
    
    if accuracy > 0:
        print(f"\n🎯 RESULTADO: {accuracy:.1f}% de acurácia")
        
        # Teste interativo
        print(f"\n🧪 TESTE RÁPIDO:")
        while True:
            hash_input = input("\nDigite hash160 correto (40 chars) ou 'quit': ").strip()
            
            if hash_input.lower() == 'quit':
                break
            
            if len(hash_input) != 40:
                print("❌ Hash deve ter 40 caracteres")
                continue
            
            magic_number = predictor.predict_magic_number_fast(hash_input.lower())
            print(f"Número mágico rápido: {magic_number}")
            print(f"🚀 Comando: ./buscar_chave_por_hash_errado {magic_number} 1:1000000")

if __name__ == "__main__":
    main()
