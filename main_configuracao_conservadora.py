#!/usr/bin/env python3
"""
Versão do main.py com configuração conservadora da GPU
"""

import os
import sys
import time
import pycuda.driver as cuda
import pycuda.autoinit
from pycuda.compiler import SourceModule
import numpy as np
from bitcoin_conversions import (
    private_key_to_address, private_key_to_hash160, 
    calculate_target_hash160, private_key_to_wif
)

# Configuração CONSERVADORA da GPU
CONSERVATIVE_CONFIG = {
    "threads_per_block": 256,      # Reduzido de 1024
    "blocks_per_grid": 1024,       # Reduzido de 65535
    "batch_multiplier": 1,         # Reduzido de 64
    "max_batch_size": 1000000      # Máximo 1M por vez
}

# Configuração da carteira de teste
TARGET_ADDRESS = "**********************************"
TARGET_HASH160 = bytes.fromhex('c24c028f0ad79d963195436c0ee23a27a37c3985')
START_KEY = 1
END_KEY = 16777216  # 16M

def format_private_key(private_key_int):
    """Formata uma chave privada para exibição"""
    return f"0x{private_key_int:064x}"

def search_keys_conservative(start_key, target_hash160, max_keys=None):
    """Busca com configuração conservadora"""
    print("🔍 BUSCA COM CONFIGURAÇÃO CONSERVADORA")
    print(f"Threads por bloco: {CONSERVATIVE_CONFIG['threads_per_block']}")
    print(f"Blocos por grade: {CONSERVATIVE_CONFIG['blocks_per_grid']}")
    print(f"Batch size máximo: {CONSERVATIVE_CONFIG['max_batch_size']:,}")
    
    try:
        # Importar kernel CUDA
        from cuda_kernel import cuda_code
        
        # Compilar kernel
        print("🔧 Compilando kernel...")
        mod = SourceModule(cuda_code)
        search_func = mod.get_function("search_keys")
        print("✅ Kernel compilado")
        
        # Configuração conservadora
        batch_size = min(
            CONSERVATIVE_CONFIG['max_batch_size'],
            CONSERVATIVE_CONFIG['threads_per_block'] * CONSERVATIVE_CONFIG['blocks_per_grid']
        )
        
        current_key = start_key
        end_key = max_keys if max_keys else END_KEY
        keys_checked = 0
        start_time = time.time()
        
        print(f"Range: {start_key:,} a {end_key:,}")
        print(f"Batch size: {batch_size:,}")
        print(f"Target: {target_hash160.hex()}")
        
        while current_key <= end_key:
            # Ajustar batch para não ultrapassar o range
            remaining = end_key - current_key + 1
            actual_batch = min(batch_size, remaining)
            
            print(f"\n🔍 Testando chaves {current_key:,} a {current_key + actual_batch - 1:,}")
            
            # Preparar dados para GPU
            target_hash160_gpu = cuda.mem_alloc(20)
            cuda.memcpy_htod(target_hash160_gpu, target_hash160)
            
            # Buffers de resultado
            found = np.array([0], dtype=np.uint32)
            found_key = np.array([0], dtype=np.uint64)
            
            found_gpu = cuda.mem_alloc(found.nbytes)
            found_key_gpu = cuda.mem_alloc(found_key.nbytes)
            
            cuda.memcpy_htod(found_gpu, found)
            cuda.memcpy_htod(found_key_gpu, found_key)
            
            # Executar kernel
            try:
                search_func(
                    np.uint64(current_key),    # start_key_lo
                    np.uint64(0),              # start_key_hi
                    np.uint32(actual_batch),   # batch_size
                    target_hash160_gpu,        # target_hash160
                    found_gpu,                 # found
                    found_key_gpu,             # found_key
                    block=(CONSERVATIVE_CONFIG['threads_per_block'], 1, 1),
                    grid=(CONSERVATIVE_CONFIG['blocks_per_grid'], 1)
                )
                
                # Sincronizar
                cuda.Context.synchronize()
                
                # Ler resultados
                cuda.memcpy_dtoh(found, found_gpu)
                cuda.memcpy_dtoh(found_key, found_key_gpu)
                
                print(f"   Resultado: found={found[0]}, key={found_key[0]}")
                
                if found[0] == 1:
                    print(f"\n🎯 GPU ENCONTROU CANDIDATO!")
                    print(f"Chave encontrada: {format_private_key(found_key[0])}")
                    
                    # Verificar na CPU
                    endereco_cpu = private_key_to_address(found_key[0])
                    wif_cpu = private_key_to_wif(found_key[0])
                    hash160_cpu = private_key_to_hash160(found_key[0])
                    
                    print(f"Verificando na CPU...")
                    print(f"Chave: {format_private_key(found_key[0])}")
                    print(f"Hash160 CPU: {hash160_cpu.hex()}")
                    print(f"Endereço CPU: {endereco_cpu}")
                    print(f"WIF: {wif_cpu}")
                    
                    if endereco_cpu == TARGET_ADDRESS:
                        print(f"\n🎉 SUCESSO! CHAVE CORRETA ENCONTRADA!")
                        print(f"✅ Chave privada: {format_private_key(found_key[0])}")
                        print(f"✅ WIF: {wif_cpu}")
                        print(f"✅ Endereço: {endereco_cpu}")
                        
                        # Limpar memória
                        target_hash160_gpu.free()
                        found_gpu.free()
                        found_key_gpu.free()
                        
                        return True, found_key[0]
                    else:
                        print(f"❌ Falso positivo. Continuando...")
                
            except Exception as e:
                print(f"❌ Erro no kernel: {e}")
            
            finally:
                # Limpar memória
                target_hash160_gpu.free()
                found_gpu.free()
                found_key_gpu.free()
            
            # Próximo lote
            current_key += actual_batch
            keys_checked += actual_batch
            
            # Status
            elapsed = time.time() - start_time
            if elapsed > 0:
                rate = keys_checked / elapsed
                print(f"   Progresso: {keys_checked:,} chaves | {rate:.0f} chaves/s | {elapsed:.1f}s")
            
            # Parar se ultrapassou o range
            if current_key > end_key:
                break
        
        print(f"\n❌ Chave não encontrada no range {start_key:,} a {end_key:,}")
        return False, None
        
    except Exception as e:
        print(f"❌ Erro na busca: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_different_batch_sizes():
    """Testa diferentes tamanhos de batch para encontrar o problema"""
    print("🧪 TESTE DE DIFERENTES BATCH SIZES")
    print("="*60)
    
    batch_sizes = [1000, 10000, 100000, 1000000, 10000000]
    
    for batch_size in batch_sizes:
        print(f"\n🔍 TESTANDO BATCH SIZE: {batch_size:,}")
        
        # Configurar batch size temporário
        original_max = CONSERVATIVE_CONFIG['max_batch_size']
        CONSERVATIVE_CONFIG['max_batch_size'] = batch_size
        
        # Testar busca pequena
        found, key = search_keys_conservative(1, TARGET_HASH160, max_keys=batch_size)
        
        if found:
            print(f"✅ SUCESSO com batch size {batch_size:,}!")
            print(f"   Chave encontrada: {key}")
            break
        else:
            print(f"❌ FALHOU com batch size {batch_size:,}")
        
        # Restaurar configuração
        CONSERVATIVE_CONFIG['max_batch_size'] = original_max
    
    print("\n" + "="*60)

def main():
    print("="*80)
    print("🎯 TESTE COM CONFIGURAÇÃO CONSERVADORA")
    print("="*80)
    
    print("Este programa testa o kernel com configuração conservadora")
    print("para identificar problemas com batch sizes agressivos.")
    
    print(f"\n📍 CONFIGURAÇÃO:")
    print(f"Endereço alvo: {TARGET_ADDRESS}")
    print(f"NÚMERO MÁGICO: {TARGET_HASH160.hex()}")
    print(f"Range: {START_KEY:,} a {END_KEY:,}")
    
    print(f"\n⚙️ CONFIGURAÇÃO CONSERVADORA:")
    for key, value in CONSERVATIVE_CONFIG.items():
        print(f"   {key}: {value:,}")
    
    # Teste 1: Configuração conservadora
    print(f"\n🧪 TESTE 1: CONFIGURAÇÃO CONSERVADORA")
    found, key = search_keys_conservative(START_KEY, TARGET_HASH160, max_keys=1000000)
    
    if found:
        print(f"✅ SUCESSO! Configuração conservadora funciona")
    else:
        print(f"❌ FALHOU mesmo com configuração conservadora")
        
        # Teste 2: Diferentes batch sizes
        print(f"\n🧪 TESTE 2: DIFERENTES BATCH SIZES")
        test_different_batch_sizes()

if __name__ == "__main__":
    main()
